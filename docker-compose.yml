version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: tourna-postgres
    environment:
      POSTGRES_DB: tourna
      POSTGRES_USER: tourna
      POSTGRES_PASSWORD: tourna123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d
    networks:
      - tourna-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: tourna-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tourna-network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: tourna-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # 管理界面
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - tourna-network

  # Elasticsearch搜索引擎
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: tourna-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - tourna-network

  # Kibana (可选，用于Elasticsearch管理)
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: tourna-kibana
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - tourna-network

  # MinIO对象存储 (用于文件存储)
  minio:
    image: minio/minio:latest
    container_name: tourna-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"  # 管理界面
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - tourna-network

  # Spring Boot后端应用 (开发环境)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: tourna-backend
    environment:
      SPRING_PROFILES_ACTIVE: dev
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: tourna
      DB_USERNAME: tourna
      DB_PASSWORD: tourna123
      REDIS_HOST: redis
      REDIS_PORT: 6379
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      ELASTICSEARCH_HOST: elasticsearch
      ELASTICSEARCH_PORT: 9200
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - elasticsearch
    volumes:
      - ./backend:/app
      - /app/target  # 排除target目录
    networks:
      - tourna-network
    profiles:
      - backend

  # React Native前端应用 (开发环境)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: tourna-frontend
    environment:
      EXPO_DEVTOOLS_LISTEN_ADDRESS: 0.0.0.0
      REACT_NATIVE_PACKAGER_HOSTNAME: 0.0.0.0
    ports:
      - "19000:19000"  # Expo DevTools
      - "19001:19001"  # Expo DevTools
      - "19002:19002"  # Metro bundler
      - "19006:19006"  # Web
    volumes:
      - ./frontend:/app
      - /app/node_modules  # 排除node_modules
    networks:
      - tourna-network
    profiles:
      - frontend

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  elasticsearch_data:
  minio_data:

networks:
  tourna-network:
    driver: bridge
