package com.tourna.product.entity;

import com.tourna.common.entity.BaseEntity;
import com.tourna.guide.entity.Guide;
import com.tourna.product.enums.DurationType;
import com.tourna.product.enums.ServiceType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 旅游服务产品实体类
 * 导游发布的各种旅游服务产品
 */
@Entity
@Table(name = "products", indexes = {
    @Index(name = "idx_products_guide_id", columnList = "guide_id"),
    @Index(name = "idx_products_service_type", columnList = "serviceType"),
    @Index(name = "idx_products_price", columnList = "priceUsdc"),
    @Index(name = "idx_products_location", columnList = "location")
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Product extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "guide_id", nullable = false)
    private Guide guide;

    @NotBlank
    @Size(max = 200)
    @Column(nullable = false, length = 200)
    private String title;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "service_type", length = 50)
    private ServiceType serviceType;

    @Enumerated(EnumType.STRING)
    @Column(name = "duration_type", length = 20)
    private DurationType durationType;

    @Min(1)
    @Column(name = "duration_hours")
    private Integer durationHours;

    @NotNull
    @DecimalMin("0.01")
    @Column(name = "price_usdc", precision = 10, scale = 2, nullable = false)
    private BigDecimal priceUsdc;

    @Min(1)
    @Column(name = "max_participants")
    private Integer maxParticipants = 1;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> location;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> availability;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<String> images;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<String> tags;

    @Column(name = "is_negotiable")
    private Boolean isNegotiable = false;

    @Column(name = "is_active")
    private Boolean isActive = true;

    // 统计字段
    @Min(0)
    @Column(name = "view_count")
    private Integer viewCount = 0;

    @Min(0)
    @Column(name = "order_count")
    private Integer orderCount = 0;

    @DecimalMin("0.00")
    @DecimalMax("5.00")
    @Column(precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "review_count")
    private Integer reviewCount = 0;

    /**
     * 检查产品是否激活
     */
    public boolean isActive() {
        return isActive != null && isActive;
    }

    /**
     * 检查是否支持议价
     */
    public boolean isNegotiable() {
        return isNegotiable != null && isNegotiable;
    }

    /**
     * 增加浏览次数
     */
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
    }

    /**
     * 增加订单次数
     */
    public void incrementOrderCount() {
        this.orderCount = (this.orderCount == null ? 0 : this.orderCount) + 1;
    }

    /**
     * 更新评分
     */
    public void updateRating(BigDecimal newRating) {
        if (reviewCount == 0) {
            this.rating = newRating;
            this.reviewCount = 1;
        } else {
            BigDecimal totalScore = this.rating.multiply(BigDecimal.valueOf(reviewCount));
            totalScore = totalScore.add(newRating);
            this.reviewCount++;
            this.rating = totalScore.divide(BigDecimal.valueOf(reviewCount), 2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 获取城市名称
     */
    public String getCityName() {
        if (location != null && location.containsKey("city")) {
            return (String) location.get("city");
        }
        return null;
    }

    /**
     * 获取国家名称
     */
    public String getCountryName() {
        if (location != null && location.containsKey("country")) {
            return (String) location.get("country");
        }
        return null;
    }

    /**
     * 检查是否有指定标签
     */
    public boolean hasTag(String tag) {
        return tags != null && tags.contains(tag);
    }
}
