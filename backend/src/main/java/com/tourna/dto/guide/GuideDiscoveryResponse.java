package com.tourna.dto.guide;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 导游发现响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GuideDiscoveryResponse {
    
    /**
     * 导游ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 导游姓名
     */
    private String name;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 平均评分
     */
    private BigDecimal rating;
    
    /**
     * 评价数量
     */
    private Integer reviewCount;
    
    /**
     * 距离（公里）
     */
    private BigDecimal distance;
    
    /**
     * 是否在线
     */
    private Boolean isOnline;
    
    /**
     * 是否认证
     */
    private Boolean isVerified;
    
    /**
     * 专业领域
     */
    private List<String> specialties;
    
    /**
     * 小时费率
     */
    private BigDecimal hourlyRate;
    
    /**
     * 货币
     */
    private String currency;
    
    /**
     * 支持的语言
     */
    private List<String> languages;
    
    /**
     * 响应时间描述
     */
    private String responseTime;
    
    /**
     * 位置信息
     */
    private LocationInfo location;
    
    /**
     * 热门服务
     */
    private List<PopularService> popularServices;
    
    /**
     * 导游简介
     */
    private String bio;
    
    /**
     * 完成的旅游数量
     */
    private Integer completedTours;
    
    /**
     * 响应率（百分比）
     */
    private Integer responseRate;
    
    /**
     * 最后活跃时间
     */
    private LocalDateTime lastActiveAt;
    
    /**
     * 位置信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LocationInfo {
        private String city;
        private String country;
        private String address;
        private BigDecimal latitude;
        private BigDecimal longitude;
    }
    
    /**
     * 热门服务
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PopularService {
        private Long id;
        private String title;
        private BigDecimal price;
        private String duration;
        private String category;
        private BigDecimal rating;
        private Integer bookingCount;
    }
}
