package com.tourna.dto.guide;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.List;

/**
 * 导游发现请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GuideDiscoveryRequest {
    
    /**
     * 用户当前纬度
     */
    @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
    private BigDecimal latitude;
    
    /**
     * 用户当前经度
     */
    @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
    private BigDecimal longitude;
    
    /**
     * 搜索半径（公里）
     */
    @Min(value = 1, message = "Radius must be at least 1 km")
    @Max(value = 100, message = "Radius cannot exceed 100 km")
    @Builder.Default
    private Integer radiusKm = 10;
    
    /**
     * 专业领域筛选
     */
    private List<String> specialties;
    
    /**
     * 语言筛选
     */
    private List<String> languages;
    
    /**
     * 最小评分
     */
    @DecimalMin(value = "0.0", message = "Minimum rating cannot be negative")
    @DecimalMax(value = "5.0", message = "Maximum rating cannot exceed 5.0")
    private BigDecimal minRating;
    
    /**
     * 最大小时费率
     */
    @Min(value = 0, message = "Maximum hourly rate cannot be negative")
    private Integer maxHourlyRate;
    
    /**
     * 仅显示在线导游
     */
    @Builder.Default
    private Boolean onlineOnly = false;
    
    /**
     * 仅显示认证导游
     */
    @Builder.Default
    private Boolean verifiedOnly = false;
    
    /**
     * 排序方式
     */
    @Builder.Default
    private SortBy sortBy = SortBy.DISTANCE;
    
    /**
     * 排序方向
     */
    @Builder.Default
    private SortDirection sortDirection = SortDirection.ASC;
    
    /**
     * 页码（从0开始）
     */
    @Min(value = 0, message = "Page number cannot be negative")
    @Builder.Default
    private Integer page = 0;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "Page size must be at least 1")
    @Max(value = 100, message = "Page size cannot exceed 100")
    @Builder.Default
    private Integer size = 20;
    
    /**
     * 排序方式枚举
     */
    public enum SortBy {
        DISTANCE,
        RATING,
        HOURLY_RATE,
        RESPONSE_TIME,
        CREATED_AT
    }
    
    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        ASC,
        DESC
    }
}
