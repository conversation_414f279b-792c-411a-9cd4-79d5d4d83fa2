package com.tourna.dto.auth;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 社交登录请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocialLoginRequest {
    
    /**
     * 社交平台提供商 (google, apple)
     */
    @NotBlank(message = "Provider is required")
    private String provider;
    
    /**
     * 社交平台用户ID
     */
    @NotBlank(message = "Social ID is required")
    private String socialId;
    
    /**
     * 邮箱地址
     */
    @Email(message = "Invalid email format")
    @NotBlank(message = "Email is required")
    private String email;
    
    /**
     * 用户姓名
     */
    @NotBlank(message = "Name is required")
    private String name;
    
    /**
     * 名字
     */
    private String firstName;
    
    /**
     * 姓氏
     */
    private String lastName;
    
    /**
     * 头像URL
     */
    private String avatar;
}
