package com.tourna.user.mapper;

import com.tourna.user.dto.UserCreateRequest;
import com.tourna.user.dto.UserResponse;
import com.tourna.user.dto.UserUpdateRequest;
import com.tourna.user.entity.User;
import org.springframework.stereotype.Component;

/**
 * 用户实体与DTO转换器
 */
@Component
public class UserMapper {

    /**
     * 将创建请求转换为实体
     */
    public User toEntity(UserCreateRequest request) {
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setLanguagePreference(request.getLanguagePreference());
        user.setWalletAddress(request.getWalletAddress());
        return user;
    }

    /**
     * 将实体转换为响应DTO
     */
    public UserResponse toResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setFirstName(user.getFirstName());
        response.setLastName(user.getLastName());
        response.setFullName(user.getFullName());
        response.setAvatarUrl(user.getAvatarUrl());
        response.setLanguagePreference(user.getLanguagePreference());
        response.setKycStatus(user.getKycStatus());
        response.setKycLevel(user.getKycLevel());
        response.setWalletAddress(user.getWalletAddress());
        response.setIsActive(user.getIsActive());
        response.setRoles(user.getRoles());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());
        return response;
    }

    /**
     * 用更新请求更新实体
     */
    public void updateEntity(User user, UserUpdateRequest request) {
        if (request.getEmail() != null) {
            user.setEmail(request.getEmail());
        }
        if (request.getPhone() != null) {
            user.setPhone(request.getPhone());
        }
        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }
        if (request.getAvatarUrl() != null) {
            user.setAvatarUrl(request.getAvatarUrl());
        }
        if (request.getLanguagePreference() != null) {
            user.setLanguagePreference(request.getLanguagePreference());
        }
        if (request.getWalletAddress() != null) {
            user.setWalletAddress(request.getWalletAddress());
        }
    }
}
