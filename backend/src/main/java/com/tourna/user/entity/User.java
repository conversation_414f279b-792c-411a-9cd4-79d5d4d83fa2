package com.tourna.user.entity;

import com.tourna.common.entity.BaseEntity;
import com.tourna.user.enums.KycStatus;
import com.tourna.user.enums.UserRole;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * 用户实体类
 * 包含用户基本信息、认证状态、钱包地址等
 */
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_users_email", columnList = "email"),
    @Index(name = "idx_users_username", columnList = "username"),
    @Index(name = "idx_users_kyc_status", columnList = "kycStatus")
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User extends BaseEntity {

    @NotBlank
    @Size(min = 3, max = 50)
    @Column(unique = true, nullable = false, length = 50)
    private String username;

    @Email
    @NotBlank
    @Column(unique = true, nullable = false, length = 100)
    private String email;

    @Size(max = 20)
    @Column(length = 20)
    private String phone;

    @NotBlank
    @Column(name = "password_hash", nullable = false)
    private String passwordHash;

    @Size(max = 50)
    @Column(name = "first_name", length = 50)
    private String firstName;

    @Size(max = 50)
    @Column(name = "last_name", length = 50)
    private String lastName;

    @Column(name = "avatar_url")
    private String avatarUrl;

    @Size(max = 10)
    @Column(name = "language_preference", length = 10)
    @Builder.Default
    private String languagePreference = "en";

    @Enumerated(EnumType.STRING)
    @Column(name = "kyc_status", length = 20)
    @Builder.Default
    private KycStatus kycStatus = KycStatus.PENDING;

    @Column(name = "kyc_level")
    @Builder.Default
    private Integer kycLevel = 0;

    @Size(max = 42)
    @Column(name = "wallet_address", length = 42)
    private String walletAddress;

    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;

    // 用户角色（可以是多个角色）
    @ElementCollection(fetch = FetchType.EAGER)
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "user_roles", joinColumns = @JoinColumn(name = "user_id"))
    @Column(name = "role")
    @Builder.Default
    private Set<UserRole> roles = new HashSet<>();

    /**
     * 检查用户是否有指定角色
     */
    public boolean hasRole(UserRole role) {
        return roles.contains(role);
    }

    /**
     * 添加角色
     */
    public void addRole(UserRole role) {
        roles.add(role);
    }

    /**
     * 移除角色
     */
    public void removeRole(UserRole role) {
        roles.remove(role);
    }

    /**
     * 获取用户全名
     */
    public String getFullName() {
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        } else if (firstName != null) {
            return firstName;
        } else if (lastName != null) {
            return lastName;
        } else {
            return username;
        }
    }

    /**
     * 检查是否已完成KYC
     */
    public boolean isKycCompleted() {
        return kycStatus == KycStatus.APPROVED;
    }

    /**
     * 检查账户是否激活
     */
    public boolean isAccountActive() {
        return isActive != null && isActive;
    }

    /**
     * 转换为UserResponse DTO
     */
    public com.tourna.user.dto.UserResponse toUserResponse() {
        return com.tourna.user.dto.UserResponse.builder()
                .id(this.getId())
                .username(this.username)
                .email(this.email)
                .firstName(this.firstName)
                .lastName(this.lastName)
                .fullName(this.getFullName())
                .avatarUrl(this.avatarUrl)
                .phone(this.phone)
                .languagePreference(this.languagePreference)
                .isActive(this.isActive)
                .kycStatus(this.kycStatus)
                .kycLevel(this.kycLevel)
                .walletAddress(this.walletAddress)
                .roles(this.roles)
                .createdAt(this.getCreatedAt())
                .updatedAt(this.getUpdatedAt())
                .build();
    }
}
