package com.tourna.user.service.impl;

import com.tourna.common.exception.ResourceNotFoundException;
import com.tourna.common.exception.DuplicateResourceException;
import com.tourna.user.dto.UserCreateRequest;
import com.tourna.user.dto.UserResponse;
import com.tourna.user.dto.UserUpdateRequest;
import com.tourna.user.entity.User;
import com.tourna.user.enums.KycStatus;
import com.tourna.user.enums.UserRole;
import com.tourna.user.mapper.UserMapper;
import com.tourna.user.repository.UserRepository;
import com.tourna.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    @Override
    public UserResponse createUser(UserCreateRequest request) {
        log.info("Creating user with username: {}", request.getUsername());

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new DuplicateResourceException("用户名已存在: " + request.getUsername());
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new DuplicateResourceException("邮箱已存在: " + request.getEmail());
        }

        // 检查钱包地址是否已存在
        if (request.getWalletAddress() != null && 
            userRepository.existsByWalletAddress(request.getWalletAddress())) {
            throw new DuplicateResourceException("钱包地址已存在: " + request.getWalletAddress());
        }

        // 创建用户实体
        User user = userMapper.toEntity(request);
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.addRole(UserRole.CUSTOMER); // 默认角色为客户

        // 保存用户
        User savedUser = userRepository.save(user);
        log.info("User created successfully with ID: {}", savedUser.getId());

        return userMapper.toResponse(savedUser);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserResponse> getUserById(Long id) {
        return userRepository.findById(id)
                .map(userMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserResponse> getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .map(userMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UserResponse> getUserByEmail(String email) {
        return userRepository.findByEmail(email)
                .map(userMapper::toResponse);
    }

    @Override
    public UserResponse updateUser(Long id, UserUpdateRequest request) {
        log.info("Updating user with ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        // 检查邮箱是否被其他用户使用
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new DuplicateResourceException("邮箱已存在: " + request.getEmail());
            }
        }

        // 检查钱包地址是否被其他用户使用
        if (request.getWalletAddress() != null && 
            !request.getWalletAddress().equals(user.getWalletAddress())) {
            if (userRepository.existsByWalletAddress(request.getWalletAddress())) {
                throw new DuplicateResourceException("钱包地址已存在: " + request.getWalletAddress());
            }
        }

        // 更新用户信息
        userMapper.updateEntity(user, request);
        User updatedUser = userRepository.save(user);

        log.info("User updated successfully with ID: {}", updatedUser.getId());
        return userMapper.toResponse(updatedUser);
    }

    @Override
    public void deleteUser(Long id) {
        log.info("Deleting user with ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        user.setIsActive(false);
        userRepository.save(user);

        log.info("User deleted successfully with ID: {}", id);
    }

    @Override
    public void toggleUserStatus(Long id, boolean active) {
        log.info("Toggling user status for ID: {} to {}", id, active);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        user.setIsActive(active);
        userRepository.save(user);

        log.info("User status toggled successfully for ID: {}", id);
    }

    @Override
    public void updateKycStatus(Long id, KycStatus kycStatus) {
        log.info("Updating KYC status for user ID: {} to {}", id, kycStatus);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        user.setKycStatus(kycStatus);
        if (kycStatus == KycStatus.APPROVED) {
            user.setKycLevel(1);
        }
        userRepository.save(user);

        log.info("KYC status updated successfully for user ID: {}", id);
    }

    @Override
    public void addUserRole(Long id, UserRole role) {
        log.info("Adding role {} to user ID: {}", role, id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        user.addRole(role);
        userRepository.save(user);

        log.info("Role {} added successfully to user ID: {}", role, id);
    }

    @Override
    public void removeUserRole(Long id, UserRole role) {
        log.info("Removing role {} from user ID: {}", role, id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        user.removeRole(role);
        userRepository.save(user);

        log.info("Role {} removed successfully from user ID: {}", role, id);
    }

    @Override
    public void updateWalletAddress(Long id, String walletAddress) {
        log.info("Updating wallet address for user ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + id));

        if (walletAddress != null && userRepository.existsByWalletAddress(walletAddress)) {
            throw new DuplicateResourceException("钱包地址已存在: " + walletAddress);
        }

        user.setWalletAddress(walletAddress);
        userRepository.save(user);

        log.info("Wallet address updated successfully for user ID: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isWalletAddressAvailable(String walletAddress) {
        return !userRepository.existsByWalletAddress(walletAddress);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserResponse> getUsers(Pageable pageable) {
        return userRepository.findAll(pageable)
                .map(userMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserResponse> getUsersByRole(UserRole role, Pageable pageable) {
        return userRepository.findByRole(role, pageable)
                .map(userMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserResponse> getUsersByKycStatus(KycStatus kycStatus, Pageable pageable) {
        return userRepository.findByKycStatus(kycStatus, pageable)
                .map(userMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UserResponse> searchUsers(String keyword, Pageable pageable) {
        return userRepository.searchUsers(keyword, pageable)
                .map(userMapper::toResponse);
    }

    @Override
    @Transactional(readOnly = true)
    public UserStatistics getUserStatistics() {
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countActiveUsers();
        
        Page<User> kycApproved = userRepository.findKycApprovedUsers(Pageable.unpaged());
        Page<User> pendingKyc = userRepository.findPendingKycUsers(Pageable.unpaged());
        
        return new UserStatistics(
            totalUsers,
            activeUsers,
            kycApproved.getTotalElements(),
            pendingKyc.getTotalElements()
        );
    }
}
