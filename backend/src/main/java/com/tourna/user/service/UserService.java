package com.tourna.user.service;

import com.tourna.user.dto.UserCreateRequest;
import com.tourna.user.dto.UserResponse;
import com.tourna.user.dto.UserUpdateRequest;
import com.tourna.user.entity.User;
import com.tourna.user.enums.KycStatus;
import com.tourna.user.enums.UserRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 创建用户
     */
    UserResponse createUser(UserCreateRequest request);

    /**
     * 根据ID获取用户
     */
    Optional<UserResponse> getUserById(Long id);

    /**
     * 根据用户名获取用户
     */
    Optional<UserResponse> getUserByUsername(String username);

    /**
     * 根据邮箱获取用户
     */
    Optional<UserResponse> getUserByEmail(String email);

    /**
     * 更新用户信息
     */
    UserResponse updateUser(Long id, UserUpdateRequest request);

    /**
     * 删除用户（软删除）
     */
    void deleteUser(Long id);

    /**
     * 激活/停用用户
     */
    void toggleUserStatus(Long id, boolean active);

    /**
     * 更新用户KYC状态
     */
    void updateKycStatus(Long id, KycStatus kycStatus);

    /**
     * 为用户添加角色
     */
    void addUserRole(Long id, UserRole role);

    /**
     * 移除用户角色
     */
    void removeUserRole(Long id, UserRole role);

    /**
     * 更新用户钱包地址
     */
    void updateWalletAddress(Long id, String walletAddress);

    /**
     * 检查用户名是否可用
     */
    boolean isUsernameAvailable(String username);

    /**
     * 检查邮箱是否可用
     */
    boolean isEmailAvailable(String email);

    /**
     * 检查钱包地址是否可用
     */
    boolean isWalletAddressAvailable(String walletAddress);

    /**
     * 分页查询用户
     */
    Page<UserResponse> getUsers(Pageable pageable);

    /**
     * 根据角色分页查询用户
     */
    Page<UserResponse> getUsersByRole(UserRole role, Pageable pageable);

    /**
     * 根据KYC状态分页查询用户
     */
    Page<UserResponse> getUsersByKycStatus(KycStatus kycStatus, Pageable pageable);

    /**
     * 搜索用户
     */
    Page<UserResponse> searchUsers(String keyword, Pageable pageable);

    /**
     * 获取用户统计信息
     */
    UserStatistics getUserStatistics();

    /**
     * 用户统计信息内部类
     */
    class UserStatistics {
        private long totalUsers;
        private long activeUsers;
        private long kycApprovedUsers;
        private long pendingKycUsers;

        // 构造函数、getter和setter
        public UserStatistics(long totalUsers, long activeUsers, long kycApprovedUsers, long pendingKycUsers) {
            this.totalUsers = totalUsers;
            this.activeUsers = activeUsers;
            this.kycApprovedUsers = kycApprovedUsers;
            this.pendingKycUsers = pendingKycUsers;
        }

        // Getters
        public long getTotalUsers() { return totalUsers; }
        public long getActiveUsers() { return activeUsers; }
        public long getKycApprovedUsers() { return kycApprovedUsers; }
        public long getPendingKycUsers() { return pendingKycUsers; }
    }
}
