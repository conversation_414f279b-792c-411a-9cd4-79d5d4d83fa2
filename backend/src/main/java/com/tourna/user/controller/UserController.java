package com.tourna.user.controller;

import com.tourna.common.response.ApiResponse;
import com.tourna.user.dto.UserCreateRequest;
import com.tourna.user.dto.UserResponse;
import com.tourna.user.dto.UserUpdateRequest;
import com.tourna.user.enums.KycStatus;
import com.tourna.user.enums.UserRole;
import com.tourna.user.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 用户管理控制器
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 创建用户
     */
    @PostMapping
    public ResponseEntity<ApiResponse<UserResponse>> createUser(@Valid @RequestBody UserCreateRequest request) {
        UserResponse user = userService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(user, "用户创建成功"));
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @userService.getUserById(#id).orElse(null)?.id == authentication.principal.id")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long id) {
        return userService.getUserById(id)
                .map(user -> ResponseEntity.ok(ApiResponse.success(user)))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 根据用户名获取用户
     */
    @GetMapping("/username/{username}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserByUsername(@PathVariable String username) {
        return userService.getUserByUsername(username)
                .map(user -> ResponseEntity.ok(ApiResponse.success(user)))
                .orElse(ResponseEntity.notFound().build());
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<ApiResponse<UserResponse>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        UserResponse user = userService.updateUser(id, request);
        return ResponseEntity.ok(ApiResponse.success(user, "用户信息更新成功"));
    }

    /**
     * 删除用户（软删除）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok(ApiResponse.success(null, "用户删除成功"));
    }

    /**
     * 激活/停用用户
     */
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> toggleUserStatus(
            @PathVariable Long id,
            @RequestParam boolean active) {
        userService.toggleUserStatus(id, active);
        String message = active ? "用户激活成功" : "用户停用成功";
        return ResponseEntity.ok(ApiResponse.success(null, message));
    }

    /**
     * 更新用户KYC状态
     */
    @PatchMapping("/{id}/kyc-status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> updateKycStatus(
            @PathVariable Long id,
            @RequestParam KycStatus kycStatus) {
        userService.updateKycStatus(id, kycStatus);
        return ResponseEntity.ok(ApiResponse.success(null, "KYC状态更新成功"));
    }

    /**
     * 添加用户角色
     */
    @PostMapping("/{id}/roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> addUserRole(
            @PathVariable Long id,
            @RequestParam UserRole role) {
        userService.addUserRole(id, role);
        return ResponseEntity.ok(ApiResponse.success(null, "角色添加成功"));
    }

    /**
     * 移除用户角色
     */
    @DeleteMapping("/{id}/roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> removeUserRole(
            @PathVariable Long id,
            @RequestParam UserRole role) {
        userService.removeUserRole(id, role);
        return ResponseEntity.ok(ApiResponse.success(null, "角色移除成功"));
    }

    /**
     * 更新钱包地址
     */
    @PatchMapping("/{id}/wallet")
    @PreAuthorize("hasRole('ADMIN') or #id == authentication.principal.id")
    public ResponseEntity<ApiResponse<Void>> updateWalletAddress(
            @PathVariable Long id,
            @RequestParam String walletAddress) {
        userService.updateWalletAddress(id, walletAddress);
        return ResponseEntity.ok(ApiResponse.success(null, "钱包地址更新成功"));
    }

    /**
     * 检查用户名可用性
     */
    @GetMapping("/check-username")
    public ResponseEntity<ApiResponse<Boolean>> checkUsernameAvailability(@RequestParam String username) {
        boolean available = userService.isUsernameAvailable(username);
        return ResponseEntity.ok(ApiResponse.success(available));
    }

    /**
     * 检查邮箱可用性
     */
    @GetMapping("/check-email")
    public ResponseEntity<ApiResponse<Boolean>> checkEmailAvailability(@RequestParam String email) {
        boolean available = userService.isEmailAvailable(email);
        return ResponseEntity.ok(ApiResponse.success(available));
    }

    /**
     * 分页查询用户
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsers(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<UserResponse> users = userService.getUsers(pageable);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    /**
     * 根据角色查询用户
     */
    @GetMapping("/by-role")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersByRole(
            @RequestParam UserRole role,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<UserResponse> users = userService.getUsersByRole(role, pageable);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    /**
     * 根据KYC状态查询用户
     */
    @GetMapping("/by-kyc-status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersByKycStatus(
            @RequestParam KycStatus kycStatus,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<UserResponse> users = userService.getUsersByKycStatus(kycStatus, pageable);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> searchUsers(
            @RequestParam String keyword,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<UserResponse> users = userService.searchUsers(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<UserService.UserStatistics>> getUserStatistics() {
        UserService.UserStatistics statistics = userService.getUserStatistics();
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }
}
