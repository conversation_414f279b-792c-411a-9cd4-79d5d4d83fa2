package com.tourna.user.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户更新请求DTO
 */
@Data
public class UserUpdateRequest {

    @Email(message = "邮箱格式不正确")
    private String email;

    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;

    @Size(max = 50, message = "名字长度不能超过50个字符")
    private String firstName;

    @Size(max = 50, message = "姓氏长度不能超过50个字符")
    private String lastName;

    private String avatarUrl;

    @Size(max = 10, message = "语言偏好长度不能超过10个字符")
    private String languagePreference;

    @Size(max = 42, message = "钱包地址长度不能超过42个字符")
    private String walletAddress;
}
