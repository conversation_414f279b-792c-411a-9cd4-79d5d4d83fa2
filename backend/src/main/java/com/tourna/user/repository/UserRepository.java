package com.tourna.user.repository;

import com.tourna.user.entity.User;
import com.tourna.user.enums.KycStatus;
import com.tourna.user.enums.UserRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.Set;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 根据钱包地址查找用户
     */
    Optional<User> findByWalletAddress(String walletAddress);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查钱包地址是否存在
     */
    boolean existsByWalletAddress(String walletAddress);

    /**
     * 根据KYC状态查找用户
     */
    Page<User> findByKycStatus(KycStatus kycStatus, Pageable pageable);

    /**
     * 根据激活状态查找用户
     */
    Page<User> findByIsActive(Boolean isActive, Pageable pageable);

    /**
     * 根据角色查找用户
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r IN :roles")
    Page<User> findByRolesIn(@Param("roles") Set<UserRole> roles, Pageable pageable);

    /**
     * 查找具有指定角色的用户
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r = :role")
    Page<User> findByRole(@Param("role") UserRole role, Pageable pageable);

    /**
     * 根据语言偏好查找用户
     */
    Page<User> findByLanguagePreference(String languagePreference, Pageable pageable);

    /**
     * 查找已完成KYC的用户
     */
    @Query("SELECT u FROM User u WHERE u.kycStatus = 'APPROVED'")
    Page<User> findKycApprovedUsers(Pageable pageable);

    /**
     * 查找待KYC审核的用户
     */
    @Query("SELECT u FROM User u WHERE u.kycStatus IN ('PENDING', 'IN_REVIEW')")
    Page<User> findPendingKycUsers(Pageable pageable);

    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.isActive = true")
    long countActiveUsers();

    /**
     * 统计各KYC状态的用户数量
     */
    @Query("SELECT u.kycStatus, COUNT(u) FROM User u GROUP BY u.kycStatus")
    Object[] countUsersByKycStatus();

    /**
     * 根据关键词搜索用户（用户名、邮箱、姓名）
     */
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(u.username) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    Page<User> searchUsers(@Param("keyword") String keyword, Pageable pageable);
}
