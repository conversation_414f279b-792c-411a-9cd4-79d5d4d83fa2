package com.tourna.service;

import com.tourna.auth.dto.LoginResponse;
import com.tourna.dto.auth.SocialLoginRequest;
import com.tourna.entity.SocialAccount;
import com.tourna.user.entity.User;
import com.tourna.repository.SocialAccountRepository;
import com.tourna.user.repository.UserRepository;
import com.tourna.auth.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * 社交登录服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SocialAuthService {
    
    private final UserRepository userRepository;
    private final SocialAccountRepository socialAccountRepository;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;
    
    /**
     * 社交登录
     */
    @Transactional
    public LoginResponse socialLogin(SocialLoginRequest request) {
        log.info("Processing social login for provider: {}, socialId: {}", 
                request.getProvider(), request.getSocialId());
        
        // 1. 检查是否已存在社交账户
        Optional<SocialAccount> existingSocialAccount = socialAccountRepository
                .findByProviderAndSocialId(request.getProvider(), request.getSocialId());
        
        User user;
        SocialAccount socialAccount;
        
        if (existingSocialAccount.isPresent()) {
            // 已存在社交账户，直接登录
            socialAccount = existingSocialAccount.get();
            user = socialAccount.getUser();
            
            // 更新社交账户信息
            updateSocialAccountInfo(socialAccount, request);
            
            log.info("Existing social account found for user: {}", user.getEmail());
        } else {
            // 不存在社交账户，检查是否有相同邮箱的用户
            Optional<User> existingUser = userRepository.findByEmail(request.getEmail());
            
            if (existingUser.isPresent()) {
                // 用户已存在，绑定社交账户
                user = existingUser.get();
                socialAccount = createSocialAccount(user, request);
                
                log.info("Binding social account to existing user: {}", user.getEmail());
            } else {
                // 创建新用户和社交账户
                user = createUserFromSocialLogin(request);
                socialAccount = createSocialAccount(user, request);
                
                log.info("Created new user from social login: {}", user.getEmail());
            }
        }
        
        // 生成JWT令牌
        String accessToken = jwtUtil.generateAccessToken(user.getEmail(), user.getId(),
                user.getRoles().stream().map(Enum::name).toList());
        String refreshToken = jwtUtil.generateRefreshToken(user.getEmail(), user.getId());

        return LoginResponse.builder()
                .user(user.toUserResponse())
                .token(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(3600L) // 1小时，硬编码暂时
                .build();
    }
    
    /**
     * 从社交登录信息创建新用户
     */
    private User createUserFromSocialLogin(SocialLoginRequest request) {
        // 生成唯一的用户名
        String username = generateUniqueUsername(request.getName(), request.getEmail());
        
        User user = User.builder()
                .username(username)
                .email(request.getEmail())
                .passwordHash(passwordEncoder.encode(UUID.randomUUID().toString())) // 随机密码
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .avatarUrl(request.getAvatar())
                .isActive(true)
                .build();
        
        return userRepository.save(user);
    }
    
    /**
     * 创建社交账户
     */
    private SocialAccount createSocialAccount(User user, SocialLoginRequest request) {
        SocialAccount socialAccount = SocialAccount.builder()
                .user(user)
                .provider(request.getProvider())
                .socialId(request.getSocialId())
                .socialEmail(request.getEmail())
                .socialName(request.getName())
                .avatarUrl(request.getAvatar())
                .isVerified(true)
                .build();
        
        return socialAccountRepository.save(socialAccount);
    }
    
    /**
     * 更新社交账户信息
     */
    private void updateSocialAccountInfo(SocialAccount socialAccount, SocialLoginRequest request) {
        boolean updated = false;
        
        if (!request.getEmail().equals(socialAccount.getSocialEmail())) {
            socialAccount.setSocialEmail(request.getEmail());
            updated = true;
        }
        
        if (!request.getName().equals(socialAccount.getSocialName())) {
            socialAccount.setSocialName(request.getName());
            updated = true;
        }
        
        if (request.getAvatar() != null && !request.getAvatar().equals(socialAccount.getAvatarUrl())) {
            socialAccount.setAvatarUrl(request.getAvatar());
            updated = true;
        }
        
        if (updated) {
            socialAccountRepository.save(socialAccount);
            log.info("Updated social account info for user: {}", socialAccount.getUser().getEmail());
        }
    }
    
    /**
     * 生成唯一的用户名
     */
    private String generateUniqueUsername(String name, String email) {
        // 首先尝试使用名字
        String baseUsername = name.toLowerCase()
                .replaceAll("[^a-z0-9]", "")
                .substring(0, Math.min(name.length(), 20));
        
        if (baseUsername.isEmpty()) {
            // 如果名字无效，使用邮箱前缀
            baseUsername = email.split("@")[0]
                    .replaceAll("[^a-z0-9]", "")
                    .substring(0, Math.min(email.split("@")[0].length(), 20));
        }
        
        String username = baseUsername;
        int counter = 1;
        
        // 确保用户名唯一
        while (userRepository.existsByUsername(username)) {
            username = baseUsername + counter;
            counter++;
        }
        
        return username;
    }
    
    /**
     * 检查社交账户是否已绑定
     */
    public boolean isSocialAccountBound(String provider, String socialId) {
        return socialAccountRepository.existsByProviderAndSocialId(provider, socialId);
    }
    
    /**
     * 获取用户的社交账户
     */
    public Optional<SocialAccount> getUserSocialAccount(Long userId, String provider) {
        return socialAccountRepository.findByProviderAndUserId(provider, userId);
    }
}
