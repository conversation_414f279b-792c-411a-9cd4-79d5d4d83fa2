package com.tourna.service;

import com.tourna.dto.guide.GuideDiscoveryRequest;
import com.tourna.dto.guide.GuideDiscoveryResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导游发现服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GuideDiscoveryService {
    
    /**
     * 发现附近的导游
     */
    public Page<GuideDiscoveryResponse> discoverGuides(GuideDiscoveryRequest request) {
        log.info("Discovering guides with request: {}", request);
        
        // 模拟数据 - 在实际实现中，这里会查询数据库
        List<GuideDiscoveryResponse> mockGuides = createMockGuides();
        
        // 应用筛选条件
        List<GuideDiscoveryResponse> filteredGuides = applyFilters(mockGuides, request);
        
        // 应用排序
        List<GuideDiscoveryResponse> sortedGuides = applySorting(filteredGuides, request);
        
        // 应用分页
        Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), sortedGuides.size());
        
        List<GuideDiscoveryResponse> pageContent = sortedGuides.subList(start, end);
        
        return new PageImpl<>(pageContent, pageable, sortedGuides.size());
    }
    
    /**
     * 创建模拟导游数据
     */
    private List<GuideDiscoveryResponse> createMockGuides() {
        return Arrays.asList(
            GuideDiscoveryResponse.builder()
                .id(1L)
                .userId(101L)
                .name("Maria Santos")
                .avatarUrl("https://via.placeholder.com/100")
                .rating(new BigDecimal("4.9"))
                .reviewCount(234)
                .distance(new BigDecimal("0.8"))
                .isOnline(true)
                .isVerified(true)
                .specialties(Arrays.asList("Cultural Tours", "Food & Wine", "Photography"))
                .hourlyRate(new BigDecimal("35"))
                .currency("USD")
                .languages(Arrays.asList("English", "Spanish", "Portuguese"))
                .responseTime("< 1 hour")
                .location(GuideDiscoveryResponse.LocationInfo.builder()
                    .city("Barcelona")
                    .country("Spain")
                    .address("Gothic Quarter")
                    .latitude(new BigDecimal("41.3851"))
                    .longitude(new BigDecimal("2.1734"))
                    .build())
                .popularServices(Arrays.asList(
                    GuideDiscoveryResponse.PopularService.builder()
                        .id(1L)
                        .title("Gothic Quarter Walking Tour")
                        .price(new BigDecimal("45"))
                        .duration("3h")
                        .category("Cultural")
                        .rating(new BigDecimal("4.8"))
                        .bookingCount(156)
                        .build(),
                    GuideDiscoveryResponse.PopularService.builder()
                        .id(2L)
                        .title("Tapas & Wine Experience")
                        .price(new BigDecimal("65"))
                        .duration("4h")
                        .category("Food")
                        .rating(new BigDecimal("4.9"))
                        .bookingCount(89)
                        .build()
                ))
                .bio("Passionate local guide with 8 years of experience showing travelers the hidden gems of Barcelona.")
                .completedTours(312)
                .responseRate(98)
                .lastActiveAt(LocalDateTime.now().minusMinutes(15))
                .build(),
                
            GuideDiscoveryResponse.builder()
                .id(2L)
                .userId(102L)
                .name("Ahmed Hassan")
                .avatarUrl("https://via.placeholder.com/100")
                .rating(new BigDecimal("4.7"))
                .reviewCount(156)
                .distance(new BigDecimal("1.2"))
                .isOnline(false)
                .isVerified(true)
                .specialties(Arrays.asList("Historical Sites", "Architecture", "Local Culture"))
                .hourlyRate(new BigDecimal("40"))
                .currency("USD")
                .languages(Arrays.asList("English", "Arabic", "French"))
                .responseTime("< 2 hours")
                .location(GuideDiscoveryResponse.LocationInfo.builder()
                    .city("Barcelona")
                    .country("Spain")
                    .address("Eixample District")
                    .latitude(new BigDecimal("41.3888"))
                    .longitude(new BigDecimal("2.1590"))
                    .build())
                .popularServices(Arrays.asList(
                    GuideDiscoveryResponse.PopularService.builder()
                        .id(3L)
                        .title("Sagrada Familia Deep Dive")
                        .price(new BigDecimal("55"))
                        .duration("2.5h")
                        .category("Architecture")
                        .rating(new BigDecimal("4.7"))
                        .bookingCount(203)
                        .build()
                ))
                .bio("Architecture enthusiast specializing in Gaudí's masterpieces and Barcelona's modernist heritage.")
                .completedTours(189)
                .responseRate(95)
                .lastActiveAt(LocalDateTime.now().minusHours(2))
                .build(),
                
            GuideDiscoveryResponse.builder()
                .id(3L)
                .userId(103L)
                .name("Elena Rossi")
                .avatarUrl("https://via.placeholder.com/100")
                .rating(new BigDecimal("4.8"))
                .reviewCount(189)
                .distance(new BigDecimal("2.1"))
                .isOnline(true)
                .isVerified(false)
                .specialties(Arrays.asList("Art & Museums", "Shopping", "Nightlife"))
                .hourlyRate(new BigDecimal("30"))
                .currency("USD")
                .languages(Arrays.asList("English", "Italian", "Catalan"))
                .responseTime("< 30 min")
                .location(GuideDiscoveryResponse.LocationInfo.builder()
                    .city("Barcelona")
                    .country("Spain")
                    .address("El Born")
                    .latitude(new BigDecimal("41.3833"))
                    .longitude(new BigDecimal("2.1833"))
                    .build())
                .popularServices(Arrays.asList(
                    GuideDiscoveryResponse.PopularService.builder()
                        .id(4L)
                        .title("Picasso Museum & Art Walk")
                        .price(new BigDecimal("50"))
                        .duration("3h")
                        .category("Art")
                        .rating(new BigDecimal("4.6"))
                        .bookingCount(134)
                        .build()
                ))
                .bio("Art lover and local insider who knows the best galleries, shops, and nightlife spots in Barcelona.")
                .completedTours(267)
                .responseRate(92)
                .lastActiveAt(LocalDateTime.now().minusMinutes(5))
                .build()
        );
    }
    
    /**
     * 应用筛选条件
     */
    private List<GuideDiscoveryResponse> applyFilters(List<GuideDiscoveryResponse> guides, GuideDiscoveryRequest request) {
        return guides.stream()
            .filter(guide -> {
                // 距离筛选
                if (request.getRadiusKm() != null && guide.getDistance().compareTo(new BigDecimal(request.getRadiusKm())) > 0) {
                    return false;
                }
                
                // 专业领域筛选
                if (request.getSpecialties() != null && !request.getSpecialties().isEmpty()) {
                    boolean hasMatchingSpecialty = guide.getSpecialties().stream()
                        .anyMatch(specialty -> request.getSpecialties().contains(specialty));
                    if (!hasMatchingSpecialty) {
                        return false;
                    }
                }
                
                // 语言筛选
                if (request.getLanguages() != null && !request.getLanguages().isEmpty()) {
                    boolean hasMatchingLanguage = guide.getLanguages().stream()
                        .anyMatch(language -> request.getLanguages().contains(language));
                    if (!hasMatchingLanguage) {
                        return false;
                    }
                }
                
                // 最小评分筛选
                if (request.getMinRating() != null && guide.getRating().compareTo(request.getMinRating()) < 0) {
                    return false;
                }
                
                // 最大小时费率筛选
                if (request.getMaxHourlyRate() != null && guide.getHourlyRate().compareTo(new BigDecimal(request.getMaxHourlyRate())) > 0) {
                    return false;
                }
                
                // 在线状态筛选
                if (request.getOnlineOnly() && !guide.getIsOnline()) {
                    return false;
                }
                
                // 认证状态筛选
                if (request.getVerifiedOnly() && !guide.getIsVerified()) {
                    return false;
                }
                
                return true;
            })
            .collect(Collectors.toList());
    }
    
    /**
     * 应用排序
     */
    private List<GuideDiscoveryResponse> applySorting(List<GuideDiscoveryResponse> guides, GuideDiscoveryRequest request) {
        return guides.stream()
            .sorted((g1, g2) -> {
                int comparison = 0;
                
                switch (request.getSortBy()) {
                    case DISTANCE:
                        comparison = g1.getDistance().compareTo(g2.getDistance());
                        break;
                    case RATING:
                        comparison = g1.getRating().compareTo(g2.getRating());
                        break;
                    case HOURLY_RATE:
                        comparison = g1.getHourlyRate().compareTo(g2.getHourlyRate());
                        break;
                    case CREATED_AT:
                        comparison = g1.getLastActiveAt().compareTo(g2.getLastActiveAt());
                        break;
                    default:
                        comparison = g1.getDistance().compareTo(g2.getDistance());
                }
                
                return request.getSortDirection() == GuideDiscoveryRequest.SortDirection.DESC ? -comparison : comparison;
            })
            .collect(Collectors.toList());
    }
}
