package com.tourna.guide.entity;

import com.tourna.common.entity.BaseEntity;
import com.tourna.guide.enums.CertificationLevel;
import com.tourna.user.entity.User;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 导游实体类
 * 包含导游认证信息、服务区域、评价等级等
 */
@Entity
@Table(name = "guides", indexes = {
    @Index(name = "idx_guides_user_id", columnList = "user_id"),
    @Index(name = "idx_guides_rating", columnList = "rating"),
    @Index(name = "idx_guides_certification_level", columnList = "certificationLevel")
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Guide extends BaseEntity {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(name = "certification_level", length = 20)
    private CertificationLevel certificationLevel = CertificationLevel.BASIC;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "certification_documents", columnDefinition = "jsonb")
    private Map<String, Object> certificationDocuments;

    @Column(columnDefinition = "TEXT")
    private String bio;

    @Min(0)
    @Column(name = "experience_years")
    private Integer experienceYears;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<String> languages;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "jsonb")
    private List<String> specialties;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "service_areas", columnDefinition = "jsonb")
    private List<Map<String, Object>> serviceAreas;

    @DecimalMin("0.00")
    @DecimalMax("5.00")
    @Column(precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    @Min(0)
    @Column(name = "total_reviews")
    private Integer totalReviews = 0;

    @Min(0)
    @Column(name = "total_orders")
    private Integer totalOrders = 0;

    @Column(name = "is_verified")
    private Boolean isVerified = false;

    @Column(name = "is_active")
    private Boolean isActive = true;

    /**
     * 检查导游是否已认证
     */
    public boolean isVerified() {
        return isVerified != null && isVerified;
    }

    /**
     * 检查导游是否激活
     */
    public boolean isActive() {
        return isActive != null && isActive;
    }

    /**
     * 检查是否为专业认证导游
     */
    public boolean isProfessionalCertified() {
        return certificationLevel == CertificationLevel.PROFESSIONAL;
    }

    /**
     * 更新评分
     */
    public void updateRating(BigDecimal newRating) {
        if (totalReviews == 0) {
            this.rating = newRating;
            this.totalReviews = 1;
        } else {
            BigDecimal totalScore = this.rating.multiply(BigDecimal.valueOf(totalReviews));
            totalScore = totalScore.add(newRating);
            this.totalReviews++;
            this.rating = totalScore.divide(BigDecimal.valueOf(totalReviews), 2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 增加订单数量
     */
    public void incrementOrderCount() {
        this.totalOrders = (this.totalOrders == null ? 0 : this.totalOrders) + 1;
    }

    /**
     * 检查是否支持指定语言
     */
    public boolean supportsLanguage(String language) {
        return languages != null && languages.contains(language);
    }

    /**
     * 检查是否有指定专长
     */
    public boolean hasSpecialty(String specialty) {
        return specialties != null && specialties.contains(specialty);
    }
}
