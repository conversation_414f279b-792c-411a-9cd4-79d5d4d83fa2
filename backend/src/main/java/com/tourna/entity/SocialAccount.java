package com.tourna.entity;

import com.tourna.user.entity.User;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 社交账户实体
 */
@Entity
@Table(name = "social_accounts")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SocialAccount {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 关联的用户
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    /**
     * 社交平台提供商 (google, apple, facebook, etc.)
     */
    @Column(name = "provider", nullable = false, length = 50)
    private String provider;
    
    /**
     * 社交平台用户ID
     */
    @Column(name = "social_id", nullable = false, length = 255)
    private String socialId;
    
    /**
     * 社交平台邮箱
     */
    @Column(name = "social_email", length = 255)
    private String socialEmail;
    
    /**
     * 社交平台用户名
     */
    @Column(name = "social_name", length = 255)
    private String socialName;
    
    /**
     * 社交平台头像URL
     */
    @Column(name = "avatar_url", length = 500)
    private String avatarUrl;
    
    /**
     * 访问令牌 (如果需要的话)
     */
    @Column(name = "access_token", length = 1000)
    private String accessToken;
    
    /**
     * 刷新令牌 (如果需要的话)
     */
    @Column(name = "refresh_token", length = 1000)
    private String refreshToken;
    
    /**
     * 令牌过期时间
     */
    @Column(name = "token_expires_at")
    private LocalDateTime tokenExpiresAt;
    
    /**
     * 是否已验证
     */
    @Column(name = "is_verified", nullable = false)
    @Builder.Default
    private Boolean isVerified = true;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    /**
     * 唯一约束：同一个社交平台的同一个用户只能绑定一次
     */
    @Table(uniqueConstraints = {
        @UniqueConstraint(columnNames = {"provider", "social_id"})
    })
    public static class UniqueConstraints {}
}
