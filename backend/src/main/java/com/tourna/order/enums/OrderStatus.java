package com.tourna.order.enums;

/**
 * 订单状态枚举
 */
public enum OrderStatus {
    /**
     * 待确认 - 订单已创建，等待导游确认
     */
    PENDING,
    
    /**
     * 已确认 - 导游已确认订单
     */
    CONFIRMED,
    
    /**
     * 进行中 - 服务正在进行
     */
    IN_PROGRESS,
    
    /**
     * 已完成 - 服务已完成
     */
    COMPLETED,
    
    /**
     * 已取消 - 订单被取消
     */
    CANCELLED,
    
    /**
     * 已退款 - 订单已退款
     */
    REFUNDED
}
