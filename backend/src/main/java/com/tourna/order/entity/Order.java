package com.tourna.order.entity;

import com.tourna.common.entity.BaseEntity;
import com.tourna.guide.entity.Guide;
import com.tourna.order.enums.OrderStatus;
import com.tourna.order.enums.PaymentStatus;
import com.tourna.product.entity.Product;
import com.tourna.user.entity.User;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 * 记录用户预订导游服务的详细信息
 */
@Entity
@Table(name = "orders", indexes = {
    @Index(name = "idx_orders_customer_id", columnList = "customer_id"),
    @Index(name = "idx_orders_guide_id", columnList = "guide_id"),
    @Index(name = "idx_orders_status", columnList = "status"),
    @Index(name = "idx_orders_service_date", columnList = "serviceDate"),
    @Index(name = "idx_orders_order_number", columnList = "orderNumber", unique = true)
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Order extends BaseEntity {

    @NotNull
    @Size(max = 32)
    @Column(name = "order_number", unique = true, nullable = false, length = 32)
    private String orderNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private User customer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "guide_id", nullable = false)
    private Guide guide;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", nullable = false)
    private Product product;

    @NotNull
    @Column(name = "service_date", nullable = false)
    private LocalDateTime serviceDate;

    @Min(1)
    @Column(nullable = false)
    private Integer participants = 1;

    @NotNull
    @DecimalMin("0.01")
    @Column(name = "original_price", precision = 10, scale = 2, nullable = false)
    private BigDecimal originalPrice;

    @DecimalMin("0.01")
    @Column(name = "negotiated_price", precision = 10, scale = 2)
    private BigDecimal negotiatedPrice;

    @NotNull
    @DecimalMin("0.00")
    @Column(name = "platform_fee", precision = 10, scale = 2, nullable = false)
    private BigDecimal platformFee;

    @NotNull
    @DecimalMin("0.01")
    @Column(name = "total_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    private OrderStatus status = OrderStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_status", length = 20, nullable = false)
    private PaymentStatus paymentStatus = PaymentStatus.PENDING;

    @Size(max = 66)
    @Column(name = "payment_tx_hash", length = 66)
    private String paymentTxHash;

    @Column(name = "special_requirements", columnDefinition = "TEXT")
    private String specialRequirements;

    @Column(name = "cancellation_reason", columnDefinition = "TEXT")
    private String cancellationReason;

    @Column(name = "cancelled_at")
    private LocalDateTime cancelledAt;

    @Column(name = "confirmed_at")
    private LocalDateTime confirmedAt;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    /**
     * 获取实际支付价格
     */
    public BigDecimal getActualPrice() {
        return negotiatedPrice != null ? negotiatedPrice : originalPrice;
    }

    /**
     * 计算平台费用
     */
    public void calculatePlatformFee(BigDecimal commissionRate) {
        BigDecimal actualPrice = getActualPrice();
        this.platformFee = actualPrice.multiply(commissionRate);
        this.totalAmount = actualPrice;
    }

    /**
     * 计算导游收入
     */
    public BigDecimal getGuideEarnings() {
        return getActualPrice().subtract(platformFee);
    }

    /**
     * 检查订单是否可以取消
     */
    public boolean canBeCancelled() {
        return status == OrderStatus.PENDING || status == OrderStatus.CONFIRMED;
    }

    /**
     * 检查订单是否可以确认
     */
    public boolean canBeConfirmed() {
        return status == OrderStatus.PENDING && paymentStatus == PaymentStatus.PAID;
    }

    /**
     * 检查订单是否可以完成
     */
    public boolean canBeCompleted() {
        return status == OrderStatus.IN_PROGRESS || status == OrderStatus.CONFIRMED;
    }

    /**
     * 确认订单
     */
    public void confirm() {
        if (canBeConfirmed()) {
            this.status = OrderStatus.CONFIRMED;
            this.confirmedAt = LocalDateTime.now();
        }
    }

    /**
     * 取消订单
     */
    public void cancel(String reason) {
        if (canBeCancelled()) {
            this.status = OrderStatus.CANCELLED;
            this.cancellationReason = reason;
            this.cancelledAt = LocalDateTime.now();
        }
    }

    /**
     * 完成订单
     */
    public void complete() {
        if (canBeCompleted()) {
            this.status = OrderStatus.COMPLETED;
            this.completedAt = LocalDateTime.now();
        }
    }

    /**
     * 开始服务
     */
    public void startService() {
        if (status == OrderStatus.CONFIRMED) {
            this.status = OrderStatus.IN_PROGRESS;
        }
    }

    /**
     * 检查是否已支付
     */
    public boolean isPaid() {
        return paymentStatus == PaymentStatus.PAID;
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return status == OrderStatus.COMPLETED;
    }

    /**
     * 检查是否已取消
     */
    public boolean isCancelled() {
        return status == OrderStatus.CANCELLED;
    }
}
