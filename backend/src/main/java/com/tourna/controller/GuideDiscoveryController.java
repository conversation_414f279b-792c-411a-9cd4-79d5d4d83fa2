package com.tourna.controller;

import com.tourna.common.response.ApiResponse;
import com.tourna.dto.guide.GuideDiscoveryRequest;
import com.tourna.dto.guide.GuideDiscoveryResponse;
import com.tourna.service.GuideDiscoveryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 导游发现控制器
 */
@RestController
@RequestMapping("/guides")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class GuideDiscoveryController {
    
    private final GuideDiscoveryService guideDiscoveryService;
    
    /**
     * 发现附近的导游
     */
    @PostMapping("/discover")
    public ResponseEntity<ApiResponse<Page<GuideDiscoveryResponse>>> discoverGuides(
            @Valid @RequestBody GuideDiscoveryRequest request) {
        try {
            log.info("Received guide discovery request: {}", request);
            
            Page<GuideDiscoveryResponse> guides = guideDiscoveryService.discoverGuides(request);
            
            return ResponseEntity.ok(ApiResponse.success(guides));
        } catch (Exception e) {
            log.error("Error discovering guides", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to discover guides: " + e.getMessage()));
        }
    }
    
    /**
     * 获取导游详细信息
     */
    @GetMapping("/{guideId}")
    public ResponseEntity<ApiResponse<GuideDiscoveryResponse>> getGuideDetails(
            @PathVariable Long guideId) {
        try {
            log.info("Fetching guide details for ID: {}", guideId);
            
            // 模拟获取导游详细信息
            GuideDiscoveryResponse guide = createMockGuideDetail(guideId);
            
            if (guide == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(ApiResponse.success(guide));
        } catch (Exception e) {
            log.error("Error fetching guide details for ID: {}", guideId, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to fetch guide details: " + e.getMessage()));
        }
    }
    
    /**
     * 获取导游的可用时间段
     */
    @GetMapping("/{guideId}/availability")
    public ResponseEntity<ApiResponse<Object>> getGuideAvailability(
            @PathVariable Long guideId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("Fetching availability for guide ID: {} from {} to {}", guideId, startDate, endDate);
            
            // 模拟可用时间段数据
            Object availability = createMockAvailability(guideId);
            
            return ResponseEntity.ok(ApiResponse.success(availability));
        } catch (Exception e) {
            log.error("Error fetching guide availability for ID: {}", guideId, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to fetch guide availability: " + e.getMessage()));
        }
    }
    
    /**
     * 创建模拟导游详细信息
     */
    private GuideDiscoveryResponse createMockGuideDetail(Long guideId) {
        if (guideId == 1L) {
            return GuideDiscoveryResponse.builder()
                    .id(1L)
                    .userId(101L)
                    .name("Maria Santos")
                    .avatarUrl("https://via.placeholder.com/200")
                    .rating(java.math.BigDecimal.valueOf(4.9))
                    .reviewCount(234)
                    .distance(java.math.BigDecimal.valueOf(0.8))
                    .isOnline(true)
                    .isVerified(true)
                    .specialties(java.util.Arrays.asList("Cultural Tours", "Food & Wine", "Photography"))
                    .hourlyRate(java.math.BigDecimal.valueOf(35))
                    .currency("USD")
                    .languages(java.util.Arrays.asList("English", "Spanish", "Portuguese"))
                    .responseTime("< 1 hour")
                    .bio("Passionate local guide with 8 years of experience showing travelers the hidden gems of Barcelona. I specialize in cultural tours, food experiences, and photography walks. My goal is to help you discover the authentic Barcelona that locals love.")
                    .completedTours(312)
                    .responseRate(98)
                    .lastActiveAt(java.time.LocalDateTime.now().minusMinutes(15))
                    .build();
        }
        return null;
    }
    
    /**
     * 创建模拟可用时间段
     */
    private Object createMockAvailability(Long guideId) {
        return java.util.Map.of(
            "availableSlots", java.util.Arrays.asList(
                java.util.Map.of(
                    "date", "2024-01-15",
                    "timeSlots", java.util.Arrays.asList("09:00", "14:00", "16:00")
                ),
                java.util.Map.of(
                    "date", "2024-01-16",
                    "timeSlots", java.util.Arrays.asList("10:00", "15:00")
                )
            ),
            "bookedSlots", java.util.Arrays.asList(
                java.util.Map.of(
                    "date", "2024-01-15",
                    "timeSlots", java.util.Arrays.asList("11:00", "13:00")
                )
            )
        );
    }
}
