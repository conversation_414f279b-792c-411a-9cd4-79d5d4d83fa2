package com.tourna.auth.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;

/**
 * JWT工具类
 * 用于生成、验证和解析JWT令牌
 */
@Slf4j
@Component
public class JwtUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    @Value("${jwt.refresh-expiration}")
    private Long refreshExpiration;

    private static final String ISSUER = "tourna";
    private static final String ROLES_CLAIM = "roles";
    private static final String USER_ID_CLAIM = "userId";
    private static final String TOKEN_TYPE_CLAIM = "tokenType";

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(String username, Long userId, List<String> roles) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        
        return JWT.create()
                .withIssuer(ISSUER)
                .withSubject(username)
                .withClaim(USER_ID_CLAIM, userId)
                .withClaim(ROLES_CLAIM, roles)
                .withClaim(TOKEN_TYPE_CLAIM, "access")
                .withIssuedAt(new Date())
                .withExpiresAt(Date.from(Instant.now().plus(expiration, ChronoUnit.MILLIS)))
                .sign(algorithm);
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(String username, Long userId) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        
        return JWT.create()
                .withIssuer(ISSUER)
                .withSubject(username)
                .withClaim(USER_ID_CLAIM, userId)
                .withClaim(TOKEN_TYPE_CLAIM, "refresh")
                .withIssuedAt(new Date())
                .withExpiresAt(Date.from(Instant.now().plus(refreshExpiration, ChronoUnit.MILLIS)))
                .sign(algorithm);
    }

    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            Algorithm algorithm = Algorithm.HMAC256(secret);
            JWTVerifier verifier = JWT.require(algorithm)
                    .withIssuer(ISSUER)
                    .build();
            
            verifier.verify(token);
            return true;
        } catch (JWTVerificationException e) {
            log.warn("JWT验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            DecodedJWT decodedJWT = decodeToken(token);
            return decodedJWT.getSubject();
        } catch (Exception e) {
            log.error("从令牌获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            DecodedJWT decodedJWT = decodeToken(token);
            return decodedJWT.getClaim(USER_ID_CLAIM).asLong();
        } catch (Exception e) {
            log.error("从令牌获取用户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取角色列表
     */
    public List<String> getRolesFromToken(String token) {
        try {
            DecodedJWT decodedJWT = decodeToken(token);
            return decodedJWT.getClaim(ROLES_CLAIM).asList(String.class);
        } catch (Exception e) {
            log.error("从令牌获取角色失败: {}", e.getMessage());
            return List.of();
        }
    }

    /**
     * 检查令牌是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            DecodedJWT decodedJWT = decodeToken(token);
            return decodedJWT.getExpiresAt().before(new Date());
        } catch (Exception e) {
            log.error("检查令牌过期状态失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 获取令牌过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            DecodedJWT decodedJWT = decodeToken(token);
            return decodedJWT.getExpiresAt();
        } catch (Exception e) {
            log.error("获取令牌过期时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查是否为刷新令牌
     */
    public boolean isRefreshToken(String token) {
        try {
            DecodedJWT decodedJWT = decodeToken(token);
            String tokenType = decodedJWT.getClaim(TOKEN_TYPE_CLAIM).asString();
            return "refresh".equals(tokenType);
        } catch (Exception e) {
            log.error("检查令牌类型失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解码JWT令牌
     */
    private DecodedJWT decodeToken(String token) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer(ISSUER)
                .build();
        
        return verifier.verify(token);
    }

    /**
     * 获取访问令牌过期时间（毫秒）
     */
    public Long getAccessTokenExpiration() {
        return expiration;
    }

    /**
     * 获取刷新令牌过期时间（毫秒）
     */
    public Long getRefreshTokenExpiration() {
        return refreshExpiration;
    }
}
