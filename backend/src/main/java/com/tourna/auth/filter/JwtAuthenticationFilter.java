package com.tourna.auth.filter;

import com.tourna.auth.util.JwtUtil;
import com.tourna.user.entity.User;
import com.tourna.user.repository.UserRepository;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器
 * 从请求头中提取JWT令牌并验证用户身份
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final UserRepository userRepository;

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {

        try {
            // 从请求头中提取JWT令牌
            String token = extractTokenFromRequest(request);

            if (token != null && jwtUtil.validateToken(token)) {
                // 从令牌中获取用户信息
                String username = jwtUtil.getUsernameFromToken(token);
                
                if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                    // 查找用户
                    User user = userRepository.findByUsername(username).orElse(null);
                    
                    if (user != null && user.isAccountActive()) {
                        // 创建认证对象
                        UsernamePasswordAuthenticationToken authentication = createAuthentication(user, token, request);
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        
                        log.debug("用户认证成功: {}", username);
                    }
                }
            }
        } catch (Exception e) {
            log.error("JWT认证过程中发生错误: {}", e.getMessage());
            // 清除安全上下文
            SecurityContextHolder.clearContext();
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中提取JWT令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        
        return null;
    }

    /**
     * 创建认证对象
     */
    private UsernamePasswordAuthenticationToken createAuthentication(
            User user, String token, HttpServletRequest request) {
        
        // 获取用户角色并转换为权限
        List<SimpleGrantedAuthority> authorities = user.getRoles().stream()
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role.name()))
                .collect(Collectors.toList());

        // 创建认证主体
        UserPrincipal principal = new UserPrincipal(
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                authorities
        );

        // 创建认证对象
        UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(principal, token, authorities);
        
        // 设置请求详情
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        
        return authentication;
    }

    /**
     * 用户认证主体
     */
    public static class UserPrincipal {
        private final Long id;
        private final String username;
        private final String email;
        private final List<SimpleGrantedAuthority> authorities;

        public UserPrincipal(Long id, String username, String email, List<SimpleGrantedAuthority> authorities) {
            this.id = id;
            this.username = username;
            this.email = email;
            this.authorities = authorities;
        }

        public Long getId() { return id; }
        public String getUsername() { return username; }
        public String getEmail() { return email; }
        public List<SimpleGrantedAuthority> getAuthorities() { return authorities; }
    }
}
