package com.tourna.auth.service;

import com.tourna.auth.dto.LoginRequest;
import com.tourna.auth.dto.LoginResponse;
import com.tourna.auth.dto.RegisterRequest;
import com.tourna.auth.util.JwtUtil;
import com.tourna.common.exception.ResourceNotFoundException;
import com.tourna.common.exception.DuplicateResourceException;
import com.tourna.user.dto.UserResponse;
import com.tourna.user.entity.User;
import com.tourna.user.enums.UserRole;
import com.tourna.user.mapper.UserMapper;
import com.tourna.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AuthService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;

    /**
     * 用户注册
     */
    public LoginResponse register(RegisterRequest request) {
        log.info("用户注册: {}", request.getEmail());

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new DuplicateResourceException("用户名已存在: " + request.getUsername());
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new DuplicateResourceException("邮箱已存在: " + request.getEmail());
        }

        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setPhone(request.getPhone());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setLanguagePreference(request.getLanguagePreference());
        user.addRole(UserRole.CUSTOMER); // 默认角色

        User savedUser = userRepository.save(user);
        log.info("用户注册成功: {}", savedUser.getId());

        // 生成令牌并返回
        return generateLoginResponse(savedUser);
    }

    /**
     * 用户登录
     */
    public LoginResponse login(LoginRequest request) {
        log.info("用户登录: {}", request.getEmail());

        // 查找用户
        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new BadCredentialsException("邮箱或密码错误"));

        // 检查账户是否激活
        if (!user.isAccountActive()) {
            throw new BadCredentialsException("账户已被停用");
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
            throw new BadCredentialsException("邮箱或密码错误");
        }

        log.info("用户登录成功: {}", user.getId());

        // 生成令牌并返回
        return generateLoginResponse(user);
    }

    /**
     * 刷新令牌
     */
    public LoginResponse refreshToken(String refreshToken) {
        log.info("刷新令牌");

        // 验证刷新令牌
        if (!jwtUtil.validateToken(refreshToken) || !jwtUtil.isRefreshToken(refreshToken)) {
            throw new BadCredentialsException("无效的刷新令牌");
        }

        // 从令牌中获取用户信息
        String username = jwtUtil.getUsernameFromToken(refreshToken);
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));

        // 检查账户是否激活
        if (!user.isAccountActive()) {
            throw new BadCredentialsException("账户已被停用");
        }

        log.info("令牌刷新成功: {}", user.getId());

        // 生成新的令牌并返回
        return generateLoginResponse(user);
    }

    /**
     * 用户登出
     */
    public void logout(String username) {
        log.info("用户登出: {}", username);
        // 这里可以实现令牌黑名单机制
        // 目前简单记录日志
    }

    /**
     * 获取当前用户信息
     */
    @Transactional(readOnly = true)
    public UserResponse getCurrentUser(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在"));
        
        return userMapper.toResponse(user);
    }

    /**
     * 生成登录响应
     */
    private LoginResponse generateLoginResponse(User user) {
        // 获取用户角色
        List<String> roles = user.getRoles().stream()
                .map(Enum::name)
                .collect(Collectors.toList());

        // 生成访问令牌
        String accessToken = jwtUtil.generateAccessToken(
                user.getUsername(), 
                user.getId(), 
                roles
        );

        // 生成刷新令牌
        String refreshToken = jwtUtil.generateRefreshToken(
                user.getUsername(), 
                user.getId()
        );

        // 转换用户信息
        UserResponse userResponse = userMapper.toResponse(user);

        return new LoginResponse(
                userResponse,
                accessToken,
                refreshToken,
                jwtUtil.getAccessTokenExpiration() / 1000 // 转换为秒
        );
    }
}
