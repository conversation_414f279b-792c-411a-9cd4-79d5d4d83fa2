package com.tourna.auth.dto;

import com.tourna.user.dto.UserResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoginResponse {

    private UserResponse user;
    private String token;
    private String refreshToken;
    private Long expiresIn; // 令牌过期时间（秒）
}
