package com.tourna.repository;

import com.tourna.entity.SocialAccount;
import com.tourna.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 社交账户Repository
 */
@Repository
public interface SocialAccountRepository extends JpaRepository<SocialAccount, Long> {
    
    /**
     * 根据提供商和社交ID查找社交账户
     */
    Optional<SocialAccount> findByProviderAndSocialId(String provider, String socialId);
    
    /**
     * 根据用户查找所有社交账户
     */
    List<SocialAccount> findByUser(User user);
    
    /**
     * 根据用户ID查找所有社交账户
     */
    List<SocialAccount> findByUserId(Long userId);
    
    /**
     * 根据提供商和用户查找社交账户
     */
    Optional<SocialAccount> findByProviderAndUser(String provider, User user);
    
    /**
     * 根据提供商和用户ID查找社交账户
     */
    Optional<SocialAccount> findByProviderAndUserId(String provider, Long userId);
    
    /**
     * 检查用户是否已绑定某个社交平台
     */
    boolean existsByProviderAndUserId(String provider, Long userId);
    
    /**
     * 检查社交账户是否已存在
     */
    boolean existsByProviderAndSocialId(String provider, String socialId);
    
    /**
     * 根据社交邮箱查找账户
     */
    List<SocialAccount> findBySocialEmail(String socialEmail);
    
    /**
     * 查找用户的已验证社交账户
     */
    @Query("SELECT sa FROM SocialAccount sa WHERE sa.user.id = :userId AND sa.isVerified = true")
    List<SocialAccount> findVerifiedAccountsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据提供商查找所有账户
     */
    List<SocialAccount> findByProvider(String provider);
    
    /**
     * 删除用户的所有社交账户
     */
    void deleteByUserId(Long userId);
}
