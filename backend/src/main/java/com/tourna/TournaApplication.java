package com.tourna;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Tourna Application Main Class
 * 全球旅游导游撮合平台后端服务主入口
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableAsync
@EnableTransactionManagement
public class TournaApplication {

    public static void main(String[] args) {
        SpringApplication.run(TournaApplication.class, args);
    }
}
