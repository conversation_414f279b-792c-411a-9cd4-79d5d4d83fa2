-- Tourna Platform Database Schema
-- Version 1.0 - Initial Tables

-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    avatar_url VARCHAR(255),
    language_preference VARCHAR(10) DEFAULT 'en',
    kyc_status VARCHAR(20) DEFAULT 'PENDING',
    kyc_level INTEGER DEFAULT 0,
    wallet_address VARCHAR(42),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色表
CREATE TABLE user_roles (
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL,
    PRIMARY KEY (user_id, role)
);

-- 导游表
CREATE TABLE guides (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    certification_level VARCHAR(20) DEFAULT 'BASIC',
    certification_documents JSONB,
    bio TEXT,
    experience_years INTEGER,
    languages JSONB,
    specialties JSONB,
    service_areas JSONB,
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 服务产品表
CREATE TABLE products (
    id BIGSERIAL PRIMARY KEY,
    guide_id BIGINT NOT NULL REFERENCES guides(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    service_type VARCHAR(50),
    duration_type VARCHAR(20),
    duration_hours INTEGER,
    price_usdc DECIMAL(10,2) NOT NULL,
    max_participants INTEGER DEFAULT 1,
    location JSONB,
    availability JSONB,
    images JSONB,
    tags JSONB,
    is_negotiable BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    view_count INTEGER DEFAULT 0,
    order_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    review_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_number VARCHAR(32) UNIQUE NOT NULL,
    customer_id BIGINT NOT NULL REFERENCES users(id),
    guide_id BIGINT NOT NULL REFERENCES guides(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    service_date TIMESTAMP NOT NULL,
    participants INTEGER DEFAULT 1,
    original_price DECIMAL(10,2) NOT NULL,
    negotiated_price DECIMAL(10,2),
    platform_fee DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING',
    payment_status VARCHAR(20) DEFAULT 'PENDING',
    payment_tx_hash VARCHAR(66),
    special_requirements TEXT,
    cancellation_reason TEXT,
    cancelled_at TIMESTAMP,
    confirmed_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 评价表
CREATE TABLE reviews (
    id BIGSERIAL PRIMARY KEY,
    order_id BIGINT UNIQUE NOT NULL REFERENCES orders(id),
    reviewer_id BIGINT NOT NULL REFERENCES users(id),
    reviewee_id BIGINT NOT NULL REFERENCES users(id),
    product_id BIGINT NOT NULL REFERENCES products(id),
    rating DECIMAL(3,2) NOT NULL CHECK (rating >= 1.00 AND rating <= 5.00),
    comment TEXT,
    is_anonymous BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 聊天会话表
CREATE TABLE chat_sessions (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL REFERENCES users(id),
    guide_id BIGINT NOT NULL REFERENCES users(id),
    product_id BIGINT REFERENCES products(id),
    order_id BIGINT REFERENCES orders(id),
    is_active BOOLEAN DEFAULT true,
    last_message_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, guide_id, product_id)
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id BIGSERIAL PRIMARY KEY,
    session_id BIGINT NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    sender_id BIGINT NOT NULL REFERENCES users(id),
    message_type VARCHAR(20) DEFAULT 'TEXT',
    content TEXT NOT NULL,
    translated_content JSONB,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_kyc_status ON users(kyc_status);
CREATE INDEX idx_users_wallet_address ON users(wallet_address);

-- 导游表索引
CREATE INDEX idx_guides_user_id ON guides(user_id);
CREATE INDEX idx_guides_rating ON guides(rating DESC);
CREATE INDEX idx_guides_certification_level ON guides(certification_level);
CREATE INDEX idx_guides_is_verified ON guides(is_verified);

-- 产品表索引
CREATE INDEX idx_products_guide_id ON products(guide_id);
CREATE INDEX idx_products_service_type ON products(service_type);
CREATE INDEX idx_products_price ON products(price_usdc);
CREATE INDEX idx_products_rating ON products(rating DESC);
CREATE INDEX idx_products_location ON products USING GIN(location);
CREATE INDEX idx_products_tags ON products USING GIN(tags);

-- 订单表索引
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_guide_id ON orders(guide_id);
CREATE INDEX idx_orders_product_id ON orders(product_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_service_date ON orders(service_date);
CREATE INDEX idx_orders_order_number ON orders(order_number);

-- 评价表索引
CREATE INDEX idx_reviews_order_id ON reviews(order_id);
CREATE INDEX idx_reviews_reviewer_id ON reviews(reviewer_id);
CREATE INDEX idx_reviews_reviewee_id ON reviews(reviewee_id);
CREATE INDEX idx_reviews_product_id ON reviews(product_id);
CREATE INDEX idx_reviews_rating ON reviews(rating);

-- 聊天表索引
CREATE INDEX idx_chat_sessions_customer_id ON chat_sessions(customer_id);
CREATE INDEX idx_chat_sessions_guide_id ON chat_sessions(guide_id);
CREATE INDEX idx_chat_sessions_product_id ON chat_sessions(product_id);
CREATE INDEX idx_chat_sessions_order_id ON chat_sessions(order_id);

CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_sender_id ON chat_messages(sender_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

-- 添加约束
ALTER TABLE reviews ADD CONSTRAINT chk_reviews_different_users 
    CHECK (reviewer_id != reviewee_id);

ALTER TABLE chat_sessions ADD CONSTRAINT chk_chat_different_users 
    CHECK (customer_id != guide_id);
