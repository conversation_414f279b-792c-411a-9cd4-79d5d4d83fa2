-- 创建社交账户表
CREATE TABLE social_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    provider VARCHAR(50) NOT NULL,
    social_id VARCHAR(255) NOT NULL,
    social_email VARCHAR(255),
    social_name VARCHAR(255),
    avatar_url VARCHAR(500),
    access_token VARCHAR(1000),
    refresh_token VARCHAR(1000),
    token_expires_at TIMESTAMP,
    is_verified BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_social_accounts_user_id 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 唯一约束：同一个社交平台的同一个用户只能绑定一次
    CONSTRAINT uk_social_accounts_provider_social_id 
        UNIQUE (provider, social_id)
);

-- 创建索引
CREATE INDEX idx_social_accounts_user_id ON social_accounts(user_id);
CREATE INDEX idx_social_accounts_provider ON social_accounts(provider);
CREATE INDEX idx_social_accounts_social_email ON social_accounts(social_email);
CREATE INDEX idx_social_accounts_created_at ON social_accounts(created_at);

-- 添加注释
COMMENT ON TABLE social_accounts IS '社交账户表，存储用户绑定的社交平台账户信息';
COMMENT ON COLUMN social_accounts.id IS '主键ID';
COMMENT ON COLUMN social_accounts.user_id IS '关联的用户ID';
COMMENT ON COLUMN social_accounts.provider IS '社交平台提供商 (google, apple, facebook等)';
COMMENT ON COLUMN social_accounts.social_id IS '社交平台用户ID';
COMMENT ON COLUMN social_accounts.social_email IS '社交平台邮箱';
COMMENT ON COLUMN social_accounts.social_name IS '社交平台用户名';
COMMENT ON COLUMN social_accounts.avatar_url IS '社交平台头像URL';
COMMENT ON COLUMN social_accounts.access_token IS '访问令牌';
COMMENT ON COLUMN social_accounts.refresh_token IS '刷新令牌';
COMMENT ON COLUMN social_accounts.token_expires_at IS '令牌过期时间';
COMMENT ON COLUMN social_accounts.is_verified IS '是否已验证';
COMMENT ON COLUMN social_accounts.created_at IS '创建时间';
COMMENT ON COLUMN social_accounts.updated_at IS '更新时间';
