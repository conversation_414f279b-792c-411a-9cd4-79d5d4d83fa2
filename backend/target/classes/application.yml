server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: tourna-backend
  
  profiles:
    active: dev
  
  datasource:
    url: ***************************************
    username: ${DB_USERNAME:tourna}
    password: ${DB_PASSWORD:tourna123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
  
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
  
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:guest}
    password: ${RABBITMQ_PASSWORD:guest}
    virtual-host: /
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  
  jackson:
    time-zone: UTC
    date-format: yyyy-MM-dd'T'HH:mm:ss.SSS'Z'

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:tourna-secret-key-change-in-production}
  expiration: 86400000 # 24 hours
  refresh-expiration: 604800000 # 7 days

# Web3 Configuration
web3:
  provider-url: ${WEB3_PROVIDER_URL:https://mainnet.infura.io/v3/your-project-id}
  usdc-contract-address: ${USDC_CONTRACT_ADDRESS:******************************************}
  platform-wallet-address: ${PLATFORM_WALLET_ADDRESS:******************************************}
  private-key: ${PLATFORM_PRIVATE_KEY:your-private-key}

# File Storage Configuration
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/tmp/tourna/uploads}
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx

# Elasticsearch Configuration
elasticsearch:
  host: ${ELASTICSEARCH_HOST:localhost}
  port: ${ELASTICSEARCH_PORT:9200}
  username: ${ELASTICSEARCH_USERNAME:}
  password: ${ELASTICSEARCH_PASSWORD:}

# Logging Configuration
logging:
  level:
    com.tourna: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/tourna.log

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

# Application Configuration
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:19006}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
  
  platform:
    commission-rate: 0.05 # 5%
    withdrawal-fee: 1.0 # 1 USDC
    min-withdrawal-amount: 10.0 # 10 USDC
  
  kyc:
    required-for-withdrawal: true
    max-daily-amount-without-kyc: 100.0 # 100 USDC
  
  notification:
    email:
      enabled: true
      from: <EMAIL>
    sms:
      enabled: false
    push:
      enabled: true

---
# Development Profile
spring:
  config:
    activate:
      on-profile: dev
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  
  h2:
    console:
      enabled: true

logging:
  level:
    root: INFO
    com.tourna: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: prod
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

logging:
  level:
    root: WARN
    com.tourna: INFO
