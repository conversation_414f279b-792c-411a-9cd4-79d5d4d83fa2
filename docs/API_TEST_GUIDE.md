# Tourna API 测试指南

## 🚀 快速开始

### 基础信息
- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

## 📋 API 测试命令

### 1. 健康检查
```bash
curl -X GET http://localhost:8080/api/v1/health
```

### 2. 认证测试
```bash
curl -X GET http://localhost:8080/api/v1/auth/test
```

### 3. 用户注册
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User",
    "phone": "+**********"
  }'
```

### 4. 用户登录
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 5. 检查用户名可用性
```bash
curl -X GET "http://localhost:8080/api/v1/users/check-username?username=testuser"
```

### 6. 检查邮箱可用性
```bash
curl -X GET "http://localhost:8080/api/v1/users/check-email?email=<EMAIL>"
```

### 7. 根据用户名获取用户
```bash
curl -X GET http://localhost:8080/api/v1/users/username/testuser
```

### 8. 发现导游
```bash
curl -X POST http://localhost:8080/api/v1/guides/discover \
  -H "Content-Type: application/json" \
  -d '{
    "latitude": 40.7128,
    "longitude": -74.0060,
    "radiusKm": 10,
    "specialties": ["CULTURAL", "ADVENTURE"],
    "minRating": 4.0,
    "maxPrice": 100.00,
    "availableDate": "2025-07-15",
    "sortBy": "DISTANCE",
    "sortDirection": "ASC",
    "page": 0,
    "size": 20
  }'
```

### 9. 获取导游详情
```bash
curl -X GET http://localhost:8080/api/v1/guides/1
```

### 10. 获取导游可用时间
```bash
curl -X GET "http://localhost:8080/api/v1/guides/1/availability?startDate=2025-07-15&endDate=2025-07-20"
```

## 🔐 需要认证的API测试

首先获取JWT令牌（通过登录），然后在后续请求中使用：

### 获取令牌
```bash
# 保存登录响应到变量
RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

# 提取访问令牌
TOKEN=$(echo $RESPONSE | jq -r '.data.accessToken')
echo "Token: $TOKEN"
```

### 使用令牌访问受保护的端点

#### 获取当前用户信息
```bash
curl -X GET http://localhost:8080/api/v1/users/me \
  -H "Authorization: Bearer $TOKEN"
```

#### 更新当前用户信息
```bash
curl -X PUT http://localhost:8080/api/v1/users/me \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Updated",
    "lastName": "Name",
    "phone": "+9876543210"
  }'
```

#### 获取用户详情
```bash
curl -X GET http://localhost:8080/api/v1/users/1 \
  -H "Authorization: Bearer $TOKEN"
```

#### 创建用户（需要管理员权限）
```bash
curl -X POST http://localhost:8080/api/v1/users \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "New",
    "lastName": "User",
    "phone": "+**********",
    "role": "USER"
  }'
```

## 🧪 批量测试脚本

创建一个测试脚本来验证所有API：

```bash
#!/bin/bash

BASE_URL="http://localhost:8080/api/v1"

echo "🚀 开始API测试..."

# 1. 健康检查
echo "1. 测试健康检查..."
curl -s $BASE_URL/health | jq '.'

# 2. 认证测试
echo "2. 测试认证端点..."
curl -s $BASE_URL/auth/test | jq '.'

# 3. 用户注册
echo "3. 测试用户注册..."
REGISTER_RESPONSE=$(curl -s -X POST $BASE_URL/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser_'$(date +%s)'",
    "email": "test'$(date +%s)'@example.com",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User",
    "phone": "+**********"
  }')
echo $REGISTER_RESPONSE | jq '.'

# 4. 提取令牌
TOKEN=$(echo $REGISTER_RESPONSE | jq -r '.data.accessToken')
echo "获取到令牌: ${TOKEN:0:20}..."

# 5. 测试受保护的端点
echo "4. 测试获取当前用户信息..."
curl -s -X GET $BASE_URL/users/me \
  -H "Authorization: Bearer $TOKEN" | jq '.'

# 6. 测试导游发现
echo "5. 测试导游发现..."
curl -s -X POST $BASE_URL/guides/discover \
  -H "Content-Type: application/json" \
  -d '{
    "latitude": 40.7128,
    "longitude": -74.0060,
    "radiusKm": 10,
    "page": 0,
    "size": 5
  }' | jq '.'

echo "✅ API测试完成！"
```

## 📊 响应格式示例

### 成功响应
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  },
  "message": "操作成功",
  "errorCode": null,
  "timestamp": "2025-07-14T16:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "data": null,
  "message": "用户名已存在",
  "errorCode": "DUPLICATE_USERNAME",
  "timestamp": "2025-07-14T16:00:00.000Z"
}
```

## 🔍 调试技巧

1. **使用jq格式化JSON输出**:
   ```bash
   curl -s $URL | jq '.'
   ```

2. **查看HTTP状态码**:
   ```bash
   curl -w "%{http_code}" -s $URL
   ```

3. **查看响应头**:
   ```bash
   curl -I $URL
   ```

4. **详细调试信息**:
   ```bash
   curl -v $URL
   ```

## 📝 注意事项

- 确保后端服务在 `http://localhost:8080` 运行
- 某些端点需要有效的JWT令牌
- 管理员端点需要ADMIN角色权限
- 使用真实的邮箱格式进行测试
- 密码至少8位字符
