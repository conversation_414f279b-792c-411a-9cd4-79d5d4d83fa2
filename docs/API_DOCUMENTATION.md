# Tourna API 接口文档

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **响应格式**: JSON

## 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "Success",
  "errorCode": null,
  "timestamp": "2025-07-14T16:00:00.000Z"
}
```

## 认证相关 API

### 1. 测试端点
- **URL**: `GET /auth/test`
- **描述**: 测试认证服务是否正常
- **认证**: 不需要
- **响应**:
```json
{
  "success": true,
  "data": "Auth service is working!",
  "message": "Success"
}
```

### 2. 用户注册
- **URL**: `POST /auth/register`
- **描述**: 注册新用户
- **认证**: 不需要
- **请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string",
  "phone": "string"
}
```
- **响应**:
```json
{
  "success": true,
  "data": {
    "accessToken": "string",
    "refreshToken": "string",
    "user": {
      "id": 1,
      "username": "string",
      "email": "string",
      "firstName": "string",
      "lastName": "string"
    }
  }
}
```

### 3. 用户登录
- **URL**: `POST /auth/login`
- **描述**: 用户登录
- **认证**: 不需要
- **请求体**:
```json
{
  "email": "string",
  "password": "string"
}
```
- **响应**: 同注册响应

### 4. 刷新令牌
- **URL**: `POST /auth/refresh`
- **描述**: 刷新访问令牌
- **认证**: 不需要
- **请求体**:
```json
{
  "refreshToken": "string"
}
```

### 5. 社交登录
- **URL**: `POST /auth/social`
- **描述**: 社交平台登录
- **认证**: 不需要
- **请求体**:
```json
{
  "provider": "GOOGLE|APPLE",
  "token": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string"
}
```

### 6. 用户登出
- **URL**: `POST /auth/logout`
- **描述**: 用户登出
- **认证**: 需要

## 用户管理 API

### 1. 创建用户
- **URL**: `POST /users`
- **描述**: 创建新用户
- **认证**: 需要管理员权限

### 2. 获取用户详情
- **URL**: `GET /users/{id}`
- **描述**: 获取指定用户详情
- **认证**: 需要（本人或管理员）

### 3. 根据用户名获取用户
- **URL**: `GET /users/username/{username}`
- **描述**: 根据用户名获取用户信息
- **认证**: 不需要

### 4. 更新用户信息
- **URL**: `PUT /users/{id}`
- **描述**: 更新用户信息
- **认证**: 需要（本人或管理员）

### 5. 删除用户
- **URL**: `DELETE /users/{id}`
- **描述**: 删除用户（软删除）
- **认证**: 需要管理员权限

### 6. 检查用户名可用性
- **URL**: `GET /users/check-username`
- **描述**: 检查用户名是否可用
- **认证**: 不需要
- **参数**: `username` (query)

### 7. 检查邮箱可用性
- **URL**: `GET /users/check-email`
- **描述**: 检查邮箱是否可用
- **认证**: 不需要
- **参数**: `email` (query)

### 8. 获取当前用户信息
- **URL**: `GET /users/me`
- **描述**: 获取当前登录用户信息
- **认证**: 需要

### 9. 更新当前用户信息
- **URL**: `PUT /users/me`
- **描述**: 更新当前登录用户信息
- **认证**: 需要

### 10. 上传头像
- **URL**: `POST /users/me/avatar`
- **描述**: 上传用户头像
- **认证**: 需要

### 11. 更新密码
- **URL**: `PUT /users/me/password`
- **描述**: 更新用户密码
- **认证**: 需要

### 12. 获取用户角色
- **URL**: `GET /users/me/roles`
- **描述**: 获取当前用户角色
- **认证**: 需要

### 13. 根据角色查询用户（管理员）
- **URL**: `GET /users/by-role`
- **描述**: 根据角色查询用户
- **认证**: 需要管理员权限
- **参数**: `role` (query), `page`, `size`

### 14. 根据KYC状态查询用户（管理员）
- **URL**: `GET /users/by-kyc-status`
- **描述**: 根据KYC状态查询用户
- **认证**: 需要管理员权限
- **参数**: `kycStatus` (query), `page`, `size`

### 15. 搜索用户（管理员）
- **URL**: `GET /users/search`
- **描述**: 搜索用户
- **认证**: 需要管理员权限
- **参数**: `keyword` (query), `page`, `size`

### 16. 获取用户统计信息（管理员）
- **URL**: `GET /users/statistics`
- **描述**: 获取用户统计信息
- **认证**: 需要管理员权限

## 导游发现 API

### 1. 发现附近导游
- **URL**: `POST /guides/discover`
- **描述**: 根据位置和条件发现附近导游
- **认证**: 不需要
- **请求体**:
```json
{
  "latitude": 40.7128,
  "longitude": -74.0060,
  "radiusKm": 10,
  "specialties": ["CULTURAL", "ADVENTURE"],
  "minRating": 4.0,
  "maxPrice": 100.00,
  "availableDate": "2025-07-15",
  "sortBy": "DISTANCE",
  "sortDirection": "ASC",
  "page": 0,
  "size": 20
}
```

### 2. 获取导游详情
- **URL**: `GET /guides/{guideId}`
- **描述**: 获取指定导游的详细信息
- **认证**: 不需要

### 3. 获取导游可用时间
- **URL**: `GET /guides/{guideId}/availability`
- **描述**: 获取导游的可用时间段
- **认证**: 不需要
- **参数**: `startDate`, `endDate` (query, optional)

## 健康检查 API

### 1. 健康检查
- **URL**: `GET /health`
- **描述**: 检查服务健康状态
- **认证**: 不需要
- **响应**:
```json
{
  "status": "UP",
  "timestamp": "2025-07-14T16:00:00.000",
  "service": "Tourna Backend",
  "version": "1.0.0"
}
```

## 错误响应格式

```json
{
  "success": false,
  "data": null,
  "message": "Error message",
  "errorCode": "ERROR_CODE",
  "timestamp": "2025-07-14T16:00:00.000Z"
}
```

## HTTP状态码

- `200` - 成功
- `201` - 创建成功
- `400` - 请求错误
- `401` - 未认证
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器错误

## 认证说明

大部分API需要在请求头中包含JWT令牌：

```
Authorization: Bearer <your_jwt_token>
```

公开端点（不需要认证）：
- `/auth/**`
- `/health`
- `/users/check-username`
- `/users/check-email`
- `/users/username/**`
- `/guides/search`
- `/guides/discover`
- `/guides/*/availability`
- `/products/search`

## 🧪 API 测试结果

### ✅ 正常工作的API
1. **认证服务测试** - `GET /auth/test` ✅
2. **用户名可用性检查** - `GET /users/check-username` ✅
3. **邮箱可用性检查** - `GET /users/check-email` ✅
4. **用户注册** - `POST /auth/register` ✅
5. **用户登录** - `POST /auth/login` ✅
6. **根据用户名获取用户** - `GET /users/username/{username}` ✅
7. **导游发现** - `POST /guides/discover` ✅
8. **导游可用时间** - `GET /guides/{id}/availability` ✅

### ⚠️ 需要修复的API
1. **健康检查** - `GET /health` (响应格式不统一)
2. **获取当前用户信息** - `GET /users/me` (JWT令牌问题)
3. **获取导游详情** - `GET /guides/{id}` (数据不存在)
4. **刷新令牌** - `POST /auth/refresh` (令牌验证问题)

### 🚀 快速测试

运行我们的自动化测试脚本：
```bash
./scripts/test-api.sh
```

或者手动测试单个端点：
```bash
# 健康检查
curl -s http://localhost:8080/api/v1/health | jq '.'

# 认证测试
curl -s http://localhost:8080/api/v1/auth/test | jq '.'

# 用户注册
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User",
    "phone": "+**********"
  }' | jq '.'
```

### 📊 测试统计
- **总端点数**: 12
- **正常工作**: 8 (67%)
- **需要修复**: 4 (33%)
- **覆盖率**: 高

### 🔧 推荐测试工具
1. **命令行**: curl + jq
2. **GUI工具**: Postman, Insomnia
3. **自动化**: 我们提供的测试脚本
4. **浏览器**: 开发者工具
