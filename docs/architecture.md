# Tourna 系统架构设计

## 1. 整体架构

### 1.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Client    │    │  iOS Client     │    │ Android Client  │
│  (React Native) │    │ (React Native)  │    │ (React Native)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │     (Kong)      │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Auth Service  │    │  Core Service   │    │  Chat Service   │
│  (Spring Boot)  │    │ (Spring Boot)   │    │ (Spring Boot)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Message Bus   │
                    │   (RabbitMQ)    │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PostgreSQL    │    │     Redis       │    │ Elasticsearch   │
│   (主数据库)     │    │    (缓存)       │    │   (搜索引擎)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 微服务架构

#### 核心服务模块
1. **用户认证服务 (Auth Service)**
   - 用户注册/登录
   - JWT令牌管理
   - KYC验证
   - 权限控制

2. **核心业务服务 (Core Service)**
   - 用户管理
   - 导游管理
   - 产品管理
   - 订单管理
   - 评价管理

3. **聊天服务 (Chat Service)**
   - 实时消息
   - AI翻译
   - 消息历史
   - 文件传输

4. **支付服务 (Payment Service)**
   - USDC集成
   - 钱包管理
   - 交易记录
   - 资金托管

5. **通知服务 (Notification Service)**
   - 推送通知
   - 邮件通知
   - 短信通知
   - 站内消息

## 2. 数据库设计

### 2.1 核心数据表

#### 用户表 (users)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    avatar_url VARCHAR(255),
    language_preference VARCHAR(10) DEFAULT 'en',
    kyc_status VARCHAR(20) DEFAULT 'pending',
    kyc_level INTEGER DEFAULT 0,
    wallet_address VARCHAR(42),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 导游表 (guides)
```sql
CREATE TABLE guides (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id),
    certification_level VARCHAR(20) DEFAULT 'basic', -- basic, professional
    certification_documents JSONB,
    bio TEXT,
    experience_years INTEGER,
    languages JSONB, -- ["en", "zh", "ja"]
    specialties JSONB, -- ["photography", "history", "food"]
    service_areas JSONB, -- [{"city": "Tokyo", "country": "Japan"}]
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    total_orders INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 服务产品表 (products)
```sql
CREATE TABLE products (
    id BIGSERIAL PRIMARY KEY,
    guide_id BIGINT REFERENCES guides(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    service_type VARCHAR(50), -- tour, transport, photography, translation
    duration_type VARCHAR(20), -- hourly, half_day, full_day, multi_day
    duration_hours INTEGER,
    price_usdc DECIMAL(10,2) NOT NULL,
    max_participants INTEGER DEFAULT 1,
    location JSONB, -- {"city": "Tokyo", "country": "Japan", "coordinates": []}
    availability JSONB, -- 可用时间段
    images JSONB, -- 图片URLs
    tags JSONB, -- 标签
    is_negotiable BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 订单表 (orders)
```sql
CREATE TABLE orders (
    id BIGSERIAL PRIMARY KEY,
    order_number VARCHAR(32) UNIQUE NOT NULL,
    customer_id BIGINT REFERENCES users(id),
    guide_id BIGINT REFERENCES guides(id),
    product_id BIGINT REFERENCES products(id),
    service_date TIMESTAMP,
    participants INTEGER DEFAULT 1,
    original_price DECIMAL(10,2),
    negotiated_price DECIMAL(10,2),
    platform_fee DECIMAL(10,2),
    total_amount DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'pending', -- pending, confirmed, in_progress, completed, cancelled
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, paid, refunded
    payment_tx_hash VARCHAR(66),
    special_requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 索引优化
```sql
-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_kyc_status ON users(kyc_status);

-- 导游表索引
CREATE INDEX idx_guides_user_id ON guides(user_id);
CREATE INDEX idx_guides_rating ON guides(rating DESC);
CREATE INDEX idx_guides_certification_level ON guides(certification_level);

-- 产品表索引
CREATE INDEX idx_products_guide_id ON products(guide_id);
CREATE INDEX idx_products_service_type ON products(service_type);
CREATE INDEX idx_products_price ON products(price_usdc);
CREATE INDEX idx_products_location ON products USING GIN(location);

-- 订单表索引
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_guide_id ON orders(guide_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_service_date ON orders(service_date);
```

## 3. API设计

### 3.1 RESTful API规范

#### 基础URL结构
```
https://api.tourna.com/v1/{resource}
```

#### 认证方式
```
Authorization: Bearer {jwt_token}
```

#### 响应格式
```json
{
  "success": true,
  "data": {},
  "message": "Success",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 3.2 核心API端点

#### 用户认证
```
POST /auth/register          # 用户注册
POST /auth/login             # 用户登录
POST /auth/refresh           # 刷新令牌
POST /auth/logout            # 用户登出
POST /auth/kyc               # KYC验证
```

#### 用户管理
```
GET    /users/profile        # 获取用户信息
PUT    /users/profile        # 更新用户信息
POST   /users/avatar         # 上传头像
GET    /users/{id}           # 获取用户详情
```

#### 导游管理
```
GET    /guides               # 搜索导游列表
GET    /guides/{id}          # 获取导游详情
POST   /guides               # 申请成为导游
PUT    /guides/{id}          # 更新导游信息
POST   /guides/{id}/verify   # 导游认证
```

#### 产品管理
```
GET    /products             # 搜索产品列表
GET    /products/{id}        # 获取产品详情
POST   /products             # 发布产品
PUT    /products/{id}        # 更新产品
DELETE /products/{id}        # 删除产品
```

#### 订单管理
```
GET    /orders               # 获取订单列表
GET    /orders/{id}          # 获取订单详情
POST   /orders               # 创建订单
PUT    /orders/{id}/confirm  # 确认订单
PUT    /orders/{id}/cancel   # 取消订单
PUT    /orders/{id}/complete # 完成订单
```

## 4. 安全设计

### 4.1 认证与授权
- JWT令牌认证
- 角色权限控制 (RBAC)
- API限流保护
- CORS跨域配置

### 4.2 数据安全
- 密码加密存储 (BCrypt)
- 敏感数据加密
- SQL注入防护
- XSS攻击防护

### 4.3 支付安全
- 多重签名钱包
- 交易确认机制
- 资金托管保护
- 风控监控系统

## 5. 性能优化

### 5.1 缓存策略
- Redis缓存热点数据
- CDN静态资源加速
- 数据库查询优化
- 分页查询实现

### 5.2 扩展性设计
- 微服务架构
- 水平扩展支持
- 负载均衡配置
- 数据库分片策略

## 6. 监控与运维

### 6.1 监控指标
- 系统性能监控
- 业务指标监控
- 错误日志监控
- 用户行为分析

### 6.2 部署策略
- Docker容器化
- Kubernetes编排
- 蓝绿部署
- 自动化CI/CD
