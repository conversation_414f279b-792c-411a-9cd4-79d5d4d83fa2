{"info": {"name": "Tourna API", "description": "Tourna Global Travel Guide Platform API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api/v1", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}], "item": [{"name": "Health & Status", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Auth Test", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/test", "host": ["{{baseUrl}}"], "path": ["auth", "test"]}}}]}, {"name": "Authentication", "item": [{"name": "Register User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.success && response.data.accessToken) {", "        pm.collectionVariables.set('accessToken', response.data.accessToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"firstName\": \"Test\",\n  \"lastName\": \"User\",\n  \"phone\": \"+**********\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.accessToken) {", "        pm.collectionVariables.set('accessToken', response.data.accessToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your_refresh_token_here\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/refresh", "host": ["{{baseUrl}}"], "path": ["auth", "refresh"]}}}, {"name": "Social Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"provider\": \"GOOGLE\",\n  \"token\": \"google_oauth_token\",\n  \"email\": \"<EMAIL>\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"Doe\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/social", "host": ["{{baseUrl}}"], "path": ["auth", "social"]}}}]}, {"name": "User Management", "item": [{"name": "Check Username Availability", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/check-username?username=testuser", "host": ["{{baseUrl}}"], "path": ["users", "check-username"], "query": [{"key": "username", "value": "testuser"}]}}}, {"name": "Check Email Availability", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/check-email?email=<EMAIL>", "host": ["{{baseUrl}}"], "path": ["users", "check-email"], "query": [{"key": "email", "value": "<EMAIL>"}]}}}, {"name": "Get User by <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/username/testuser", "host": ["{{baseUrl}}"], "path": ["users", "username", "testuser"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}], "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}, {"name": "Update Current User", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Updated\",\n  \"lastName\": \"Name\",\n  \"phone\": \"+9876543210\"\n}"}, "url": {"raw": "{{baseUrl}}/users/me", "host": ["{{baseUrl}}"], "path": ["users", "me"]}}}]}, {"name": "Guide Discovery", "item": [{"name": "Discover Guides", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"latitude\": 40.7128,\n  \"longitude\": -74.0060,\n  \"radiusKm\": 10,\n  \"specialties\": [\"CULTURAL\", \"ADVENTURE\"],\n  \"minRating\": 4.0,\n  \"maxPrice\": 100.00,\n  \"availableDate\": \"2025-07-15\",\n  \"sortBy\": \"DISTANCE\",\n  \"sortDirection\": \"ASC\",\n  \"page\": 0,\n  \"size\": 20\n}"}, "url": {"raw": "{{baseUrl}}/guides/discover", "host": ["{{baseUrl}}"], "path": ["guides", "discover"]}}}, {"name": "Get Guide Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/guides/1", "host": ["{{baseUrl}}"], "path": ["guides", "1"]}}}, {"name": "Get Guide Availability", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/guides/1/availability?startDate=2025-07-15&endDate=2025-07-20", "host": ["{{baseUrl}}"], "path": ["guides", "1", "availability"], "query": [{"key": "startDate", "value": "2025-07-15"}, {"key": "endDate", "value": "2025-07-20"}]}}}]}]}