# Tourna 开发指南

## 快速开始

### 环境要求

- **Java**: 17+
- **Node.js**: 18+
- **Docker**: 20+
- **Docker Compose**: 2.0+
- **Git**: 2.30+

### 一键启动

```bash
# 克隆项目
git clone <repository-url>
cd tourna

# 运行初始化脚本
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 手动启动

#### 1. 启动基础设施

```bash
# 启动数据库、缓存、消息队列等
docker-compose up -d postgres redis rabbitmq elasticsearch minio
```

#### 2. 启动后端服务

```bash
cd backend

# 使用Maven启动
mvn spring-boot:run

# 或使用IDE启动TournaApplication.java
```

#### 3. 启动前端应用

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm start

# 在不同平台运行
npm run android  # Android
npm run ios      # iOS
npm run web      # Web浏览器
```

## 项目结构

```
tourna/
├── backend/                 # Java后端服务
│   ├── src/main/java/com/tourna/
│   │   ├── auth/           # 认证模块
│   │   ├── user/           # 用户管理
│   │   ├── guide/          # 导游管理
│   │   ├── product/        # 产品管理
│   │   ├── order/          # 订单系统
│   │   ├── payment/        # 支付系统
│   │   ├── chat/           # 聊天系统
│   │   ├── review/         # 评价系统
│   │   ├── common/         # 公共组件
│   │   └── config/         # 配置类
│   ├── src/main/resources/
│   │   ├── db/migration/   # 数据库迁移脚本
│   │   └── application.yml # 应用配置
│   └── pom.xml            # Maven配置
├── frontend/               # React Native前端
│   ├── src/
│   │   ├── components/     # 公共组件
│   │   ├── screens/        # 页面组件
│   │   ├── navigation/     # 导航配置
│   │   ├── services/       # API服务
│   │   ├── store/          # Redux状态管理
│   │   ├── types/          # TypeScript类型
│   │   ├── utils/          # 工具函数
│   │   └── constants/      # 常量定义
│   ├── assets/            # 静态资源
│   ├── package.json       # 依赖配置
│   └── app.json          # Expo配置
├── docs/                  # 项目文档
├── docker/               # Docker配置
├── scripts/              # 脚本文件
└── docker-compose.yml    # 容器编排
```

## 开发规范

### 代码规范

#### Java后端

- 使用Spring Boot 3.x框架
- 遵循RESTful API设计原则
- 使用Lombok减少样板代码
- 统一异常处理和响应格式
- 使用JPA进行数据访问
- 编写单元测试和集成测试

```java
// 控制器示例
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long id) {
        return userService.getUserById(id)
                .map(user -> ResponseEntity.ok(ApiResponse.success(user)))
                .orElse(ResponseEntity.notFound().build());
    }
}
```

#### React Native前端

- 使用TypeScript进行类型安全
- 使用Redux Toolkit进行状态管理
- 使用React Navigation进行导航
- 使用React Native Paper作为UI组件库
- 遵循React Hooks最佳实践

```typescript
// 组件示例
interface UserProfileProps {
  user: User;
  onUpdate: (user: User) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ user, onUpdate }) => {
  const [isEditing, setIsEditing] = useState(false);
  
  return (
    <View style={styles.container}>
      <Avatar source={{ uri: user.avatarUrl }} />
      <Text variant="headlineSmall">{user.fullName}</Text>
    </View>
  );
};
```

### Git工作流

#### 分支策略

- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成测试
- `feature/*`: 功能分支，用于新功能开发
- `bugfix/*`: 修复分支，用于bug修复
- `hotfix/*`: 热修复分支，用于紧急修复

#### 提交规范

使用Conventional Commits规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(auth): add JWT token refresh mechanism

- Implement automatic token refresh
- Add refresh token storage
- Handle token expiration gracefully

Closes #123
```

## API开发

### 接口设计原则

1. **RESTful设计**: 使用标准HTTP方法和状态码
2. **统一响应格式**: 所有接口返回统一的JSON格式
3. **版本控制**: 在URL中包含版本号 `/api/v1/`
4. **分页支持**: 列表接口支持分页参数
5. **错误处理**: 提供详细的错误信息和错误码

### 响应格式

```json
{
  "success": true,
  "data": {},
  "message": "Success",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 认证机制

使用JWT Bearer Token认证：

```
Authorization: Bearer <jwt_token>
```

## 数据库开发

### 迁移脚本

使用Flyway进行数据库版本控制：

```sql
-- V1__Create_initial_tables.sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    -- ...
);
```

### 命名规范

- 表名：小写，下划线分隔，复数形式 (`users`, `products`)
- 字段名：小写，下划线分隔 (`first_name`, `created_at`)
- 索引名：`idx_表名_字段名` (`idx_users_email`)
- 外键名：`fk_表名_引用表名` (`fk_orders_users`)

## 测试

### 后端测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=UserServiceTest

# 生成测试报告
mvn test jacoco:report
```

### 前端测试

```bash
# 运行单元测试
npm test

# 运行测试覆盖率
npm run test:coverage

# 运行E2E测试
npm run test:e2e
```

## 部署

### 开发环境

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
```

### 生产环境

```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 部署到生产环境
docker-compose -f docker-compose.prod.yml up -d
```

## 常见问题

### 1. 数据库连接失败

检查PostgreSQL是否启动：
```bash
docker-compose ps postgres
```

### 2. 前端无法连接后端

检查后端服务是否启动，确认端口配置正确。

### 3. 依赖安装失败

清除缓存重新安装：
```bash
# 前端
rm -rf node_modules package-lock.json
npm install

# 后端
mvn clean install
```

## 开发工具推荐

### IDE
- **后端**: IntelliJ IDEA / Eclipse
- **前端**: VS Code / WebStorm

### 插件
- **VS Code**: 
  - React Native Tools
  - TypeScript Hero
  - Prettier
  - ESLint
  
- **IntelliJ IDEA**:
  - Lombok Plugin
  - Spring Boot Assistant
  - Database Navigator

### 调试工具
- **API测试**: Postman / Insomnia
- **数据库**: DBeaver / pgAdmin
- **Redis**: RedisInsight
- **移动端调试**: React Native Debugger / Flipper

## 联系方式

如有问题，请联系开发团队或在项目仓库中提交Issue。
