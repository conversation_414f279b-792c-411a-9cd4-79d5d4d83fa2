<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tourna API 文档</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            background: white;
            margin-bottom: 20px;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .endpoint {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        
        .endpoint-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .method {
            padding: 5px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 15px;
            font-size: 0.9em;
        }
        
        .method.get { background-color: #28a745; color: white; }
        .method.post { background-color: #007bff; color: white; }
        .method.put { background-color: #ffc107; color: black; }
        .method.delete { background-color: #dc3545; color: white; }
        
        .endpoint-url {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 1.1em;
            color: #495057;
        }
        
        .endpoint-description {
            margin-bottom: 15px;
            color: #6c757d;
        }
        
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.working { background-color: #d4edda; color: #155724; }
        .status.issue { background-color: #f8d7da; color: #721c24; }
        
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }
        
        .test-button {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            margin-top: 10px;
        }
        
        .test-button:hover {
            background-color: #5a6fd8;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 Tourna API</h1>
            <p>全球旅游导游平台 API 文档</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">12</div>
                <div class="stat-label">总端点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">8</div>
                <div class="stat-label">正常工作</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">67%</div>
                <div class="stat-label">成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4</div>
                <div class="stat-label">需要修复</div>
            </div>
        </div>
        
        <div class="section">
            <h2>🏥 健康检查</h2>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/health</span>
                    <span class="status issue">需要修复</span>
                </div>
                <div class="endpoint-description">检查服务健康状态</div>
                <div class="code-block">curl -X GET http://localhost:8080/api/v1/health</div>
                <button class="test-button" onclick="testEndpoint('/health', 'GET')">测试端点</button>
            </div>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/auth/test</span>
                    <span class="status working">正常</span>
                </div>
                <div class="endpoint-description">测试认证服务</div>
                <div class="code-block">curl -X GET http://localhost:8080/api/v1/auth/test</div>
                <button class="test-button" onclick="testEndpoint('/auth/test', 'GET')">测试端点</button>
            </div>
        </div>
        
        <div class="section">
            <h2>🔐 用户认证</h2>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method post">POST</span>
                    <span class="endpoint-url">/auth/register</span>
                    <span class="status working">正常</span>
                </div>
                <div class="endpoint-description">用户注册</div>
                <div class="code-block">curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"test","email":"<EMAIL>","password":"password123"}'</div>
                <button class="test-button" onclick="testRegister()">测试注册</button>
            </div>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method post">POST</span>
                    <span class="endpoint-url">/auth/login</span>
                    <span class="status working">正常</span>
                </div>
                <div class="endpoint-description">用户登录</div>
                <div class="code-block">curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'</div>
                <button class="test-button" onclick="testLogin()">测试登录</button>
            </div>
        </div>
        
        <div class="section">
            <h2>👥 用户管理</h2>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/users/check-username</span>
                    <span class="status working">正常</span>
                </div>
                <div class="endpoint-description">检查用户名可用性</div>
                <div class="code-block">curl -X GET "http://localhost:8080/api/v1/users/check-username?username=testuser"</div>
                <button class="test-button" onclick="testEndpoint('/users/check-username?username=testuser', 'GET')">测试端点</button>
            </div>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method get">GET</span>
                    <span class="endpoint-url">/users/me</span>
                    <span class="status issue">需要修复</span>
                </div>
                <div class="endpoint-description">获取当前用户信息（需要认证）</div>
                <div class="code-block">curl -X GET http://localhost:8080/api/v1/users/me \
  -H "Authorization: Bearer YOUR_TOKEN"</div>
                <button class="test-button" onclick="alert('需要先登录获取令牌')">需要认证</button>
            </div>
        </div>
        
        <div class="section">
            <h2>🗺️ 导游发现</h2>
            
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method post">POST</span>
                    <span class="endpoint-url">/guides/discover</span>
                    <span class="status working">正常</span>
                </div>
                <div class="endpoint-description">发现附近导游</div>
                <div class="code-block">curl -X POST http://localhost:8080/api/v1/guides/discover \
  -H "Content-Type: application/json" \
  -d '{"latitude":40.7128,"longitude":-74.0060,"radiusKm":10}'</div>
                <button class="test-button" onclick="testGuideDiscovery()">测试发现</button>
            </div>
        </div>
        
        <div class="section">
            <h2>🛠️ 测试工具</h2>
            <p>使用以下命令运行完整的API测试：</p>
            <div class="code-block">./scripts/test-api.sh</div>
            
            <p>或者导入Postman集合：</p>
            <div class="code-block">docs/Tourna_API.postman_collection.json</div>
        </div>
    </div>
    
    <script>
        const baseUrl = 'http://localhost:8080/api/v1';
        
        async function testEndpoint(path, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(baseUrl + path, options);
                const result = await response.json();
                
                alert(`状态: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                alert(`错误: ${error.message}`);
            }
        }
        
        function testRegister() {
            const data = {
                username: 'testuser_' + Date.now(),
                email: 'test_' + Date.now() + '@example.com',
                password: 'password123',
                firstName: 'Test',
                lastName: 'User',
                phone: '+1234567890'
            };
            testEndpoint('/auth/register', 'POST', data);
        }
        
        function testLogin() {
            const data = {
                email: '<EMAIL>',
                password: 'password123'
            };
            testEndpoint('/auth/login', 'POST', data);
        }
        
        function testGuideDiscovery() {
            const data = {
                latitude: 40.7128,
                longitude: -74.0060,
                radiusKm: 10,
                page: 0,
                size: 5
            };
            testEndpoint('/guides/discover', 'POST', data);
        }
    </script>
</body>
</html>
