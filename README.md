# Tourna - 全球旅游导游撮合平台

## 项目概述

Tourna是一个连接自由旅行者与当地导游的全球化撮合平台，支持多语言、多平台部署，使用加密货币USDC进行交易。

## 核心功能

### 用户端功能
- 🔍 按城市、服务类型、价格筛选导游服务
- 💬 实时聊天与AI翻译
- 💳 USDC加密货币支付
- ⭐ 服务评价与反馈
- 🌍 多语言支持（英文、中文、日文、韩文、西班牙文、泰文）

### 导游端功能
- 📝 发布旅游服务产品
- 🏆 分级认证系统（基础认证/专业认证）
- 💰 自主定价与议价
- 📊 收入管理与提现
- 👥 客户管理与沟通

### 平台功能
- 🔐 用户认证与KYC
- 💼 交易担保与资金托管
- 🏅 信誉评级系统
- 📈 数据分析与报告
- 🛡️ 安全与合规

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.x
- **数据库**: PostgreSQL + Redis
- **认证**: JWT + OAuth2
- **支付**: Web3 + USDC集成
- **消息队列**: RabbitMQ
- **文件存储**: AWS S3 / 阿里云OSS
- **搜索引擎**: Elasticsearch

### 前端技术栈
- **框架**: React Native + Expo
- **状态管理**: Redux Toolkit
- **导航**: React Navigation
- **UI组件**: NativeBase / React Native Elements
- **地图**: React Native Maps
- **聊天**: Socket.io客户端
- **国际化**: react-i18next

### 基础设施
- **容器化**: Docker + Kubernetes
- **CI/CD**: GitHub Actions
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack
- **API网关**: Kong / Nginx

## 项目结构

```
tourna/
├── backend/                 # Java后端服务
│   ├── src/main/java/
│   │   └── com/tourna/
│   │       ├── auth/        # 认证模块
│   │       ├── user/        # 用户管理
│   │       ├── guide/       # 导游管理
│   │       ├── product/     # 产品管理
│   │       ├── order/       # 订单系统
│   │       ├── payment/     # 支付系统
│   │       ├── chat/        # 聊天系统
│   │       ├── review/      # 评价系统
│   │       └── common/      # 公共组件
│   ├── src/main/resources/
│   └── pom.xml
├── frontend/                # React Native前端
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── screens/         # 页面组件
│   │   ├── navigation/      # 导航配置
│   │   ├── services/        # API服务
│   │   ├── store/           # 状态管理
│   │   ├── utils/           # 工具函数
│   │   └── locales/         # 多语言文件
│   ├── package.json
│   └── app.json
├── docs/                    # 项目文档
├── docker/                  # Docker配置
└── scripts/                 # 部署脚本
```

## 商业模式

- **平台佣金**: 5%（由卖方承担）
- **提现手续费**: 1 USDC/次
- **增值服务**: 广告推广、优先展示
- **激励机制**: 入驻奖励、邀请奖励、优质服务奖励

## 开发计划

1. **阶段一**: 核心架构搭建与用户认证
2. **阶段二**: 导游管理与产品发布
3. **阶段三**: 搜索筛选与聊天系统
4. **阶段四**: 支付系统与订单管理
5. **阶段五**: 评价系统与多语言支持
6. **阶段六**: 测试优化与部署上线

## 快速开始

### 环境要求
- Java 17+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose

### 本地开发
```bash
# 克隆项目
git clone <repository-url>
cd tourna

# 启动后端服务
cd backend
./mvnw spring-boot:run

# 启动前端应用
cd frontend
npm install
npm start
```

## 许可证

MIT License
