#!/bin/bash

# Tourna项目初始化脚本
# 用于快速搭建开发环境

set -e

echo "🚀 开始初始化Tourna项目..."

# 检查必要的工具
check_requirements() {
    echo "📋 检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Java (用于后端开发)
    if ! command -v java &> /dev/null; then
        echo "⚠️  Java未安装，后端开发需要Java 17+"
    fi
    
    # 检查Node.js (用于前端开发)
    if ! command -v node &> /dev/null; then
        echo "⚠️  Node.js未安装，前端开发需要Node.js 18+"
    fi
    
    echo "✅ 系统要求检查完成"
}

# 创建必要的目录
create_directories() {
    echo "📁 创建项目目录..."
    
    # 后端目录
    mkdir -p backend/src/main/java/com/tourna/{auth,user,guide,product,order,payment,chat,review,common,config}
    mkdir -p backend/src/main/resources/{db/migration,static,templates}
    mkdir -p backend/src/test/java/com/tourna
    
    # 前端目录
    mkdir -p frontend/src/{components,screens,navigation,services,store,utils,types,constants,hooks,contexts}
    mkdir -p frontend/src/store/slices
    mkdir -p frontend/assets/{images,icons,fonts}
    
    # 文档目录
    mkdir -p docs/{api,database,deployment,design}
    
    # Docker目录
    mkdir -p docker/{backend,frontend,nginx}
    
    # 日志目录
    mkdir -p logs
    
    echo "✅ 目录创建完成"
}

# 启动基础服务
start_infrastructure() {
    echo "🐳 启动基础设施服务..."
    
    # 启动数据库、缓存、消息队列等基础服务
    docker-compose up -d postgres redis rabbitmq elasticsearch minio
    
    echo "⏳ 等待服务启动..."
    sleep 30
    
    # 检查服务状态
    echo "📊 检查服务状态..."
    docker-compose ps
    
    echo "✅ 基础设施服务启动完成"
}

# 初始化数据库
init_database() {
    echo "🗄️  初始化数据库..."
    
    # 等待PostgreSQL启动
    echo "⏳ 等待PostgreSQL启动..."
    until docker-compose exec postgres pg_isready -U tourna; do
        sleep 2
    done
    
    # 运行数据库迁移脚本
    echo "📝 执行数据库迁移..."
    docker-compose exec postgres psql -U tourna -d tourna -f /docker-entrypoint-initdb.d/V1__Create_initial_tables.sql
    
    echo "✅ 数据库初始化完成"
}

# 安装前端依赖
install_frontend_deps() {
    echo "📦 安装前端依赖..."
    
    if [ -d "frontend" ]; then
        cd frontend
        
        # 检查是否有package.json
        if [ -f "package.json" ]; then
            # 使用npm或yarn安装依赖
            if command -v yarn &> /dev/null; then
                echo "使用Yarn安装依赖..."
                yarn install
            else
                echo "使用npm安装依赖..."
                npm install
            fi
        else
            echo "⚠️  frontend/package.json不存在，跳过依赖安装"
        fi
        
        cd ..
    fi
    
    echo "✅ 前端依赖安装完成"
}

# 构建后端项目
build_backend() {
    echo "🔨 构建后端项目..."
    
    if [ -d "backend" ]; then
        cd backend
        
        # 检查是否有pom.xml
        if [ -f "pom.xml" ]; then
            # 使用Maven构建
            if command -v mvn &> /dev/null; then
                echo "使用Maven构建项目..."
                mvn clean compile
            else
                echo "⚠️  Maven未安装，跳过后端构建"
            fi
        else
            echo "⚠️  backend/pom.xml不存在，跳过后端构建"
        fi
        
        cd ..
    fi
    
    echo "✅ 后端项目构建完成"
}

# 创建环境配置文件
create_env_files() {
    echo "⚙️  创建环境配置文件..."
    
    # 创建.env文件
    cat > .env << EOF
# Tourna环境配置

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tourna
DB_USERNAME=tourna
DB_PASSWORD=tourna123

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# RabbitMQ配置
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USERNAME=guest
RABBITMQ_PASSWORD=guest

# Elasticsearch配置
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200

# JWT配置
JWT_SECRET=tourna-secret-key-change-in-production
JWT_EXPIRATION=86400000

# Web3配置
WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/your-project-id
USDC_CONTRACT_ADDRESS=******************************************
PLATFORM_WALLET_ADDRESS=******************************************

# 文件上传配置
FILE_UPLOAD_PATH=/tmp/tourna/uploads
FILE_MAX_SIZE=10485760

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:19006

# 开发模式
NODE_ENV=development
SPRING_PROFILES_ACTIVE=dev
EOF
    
    echo "✅ 环境配置文件创建完成"
}

# 显示启动信息
show_startup_info() {
    echo ""
    echo "🎉 Tourna项目初始化完成！"
    echo ""
    echo "📋 服务访问地址："
    echo "   🌐 PostgreSQL:     localhost:5432"
    echo "   🔴 Redis:          localhost:6379"
    echo "   🐰 RabbitMQ:       localhost:15672 (guest/guest)"
    echo "   🔍 Elasticsearch:  localhost:9200"
    echo "   📊 Kibana:         localhost:5601"
    echo "   📁 MinIO:          localhost:9001 (minioadmin/minioadmin123)"
    echo ""
    echo "🚀 启动应用："
    echo "   后端: cd backend && mvn spring-boot:run"
    echo "   前端: cd frontend && npm start"
    echo ""
    echo "🐳 Docker命令："
    echo "   启动所有服务: docker-compose up -d"
    echo "   停止所有服务: docker-compose down"
    echo "   查看日志: docker-compose logs -f [service-name]"
    echo ""
    echo "📚 更多信息请查看 README.md"
}

# 主函数
main() {
    check_requirements
    create_directories
    create_env_files
    start_infrastructure
    init_database
    install_frontend_deps
    build_backend
    show_startup_info
}

# 执行主函数
main "$@"
