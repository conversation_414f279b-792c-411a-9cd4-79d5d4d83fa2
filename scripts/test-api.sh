#!/bin/bash

# Tourna API 测试脚本
# 用法: ./scripts/test-api.sh

set -e

BASE_URL="http://localhost:8080/api/v1"
TIMESTAMP=$(date +%s)
TEST_USERNAME="testuser_$TIMESTAMP"
TEST_EMAIL="test_$<EMAIL>"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}🚀 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 测试函数
test_endpoint() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local headers="$5"
    
    print_info "测试: $name"
    
    if [ -n "$data" ]; then
        if [ -n "$headers" ]; then
            response=$(curl -s -X "$method" "$url" -H "Content-Type: application/json" -H "$headers" -d "$data")
        else
            response=$(curl -s -X "$method" "$url" -H "Content-Type: application/json" -d "$data")
        fi
    else
        if [ -n "$headers" ]; then
            response=$(curl -s -X "$method" "$url" -H "$headers")
        else
            response=$(curl -s -X "$method" "$url")
        fi
    fi
    
    # 检查响应是否为有效JSON
    if echo "$response" | jq . >/dev/null 2>&1; then
        success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            print_success "$name - 成功"
            echo "$response" | jq '.' | head -10
        else
            print_error "$name - 失败"
            echo "$response" | jq '.'
        fi
    else
        print_error "$name - 无效响应"
        echo "Response: $response"
    fi
    
    echo ""
    return 0
}

# 主测试流程
main() {
    print_header "Tourna API 测试开始"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq 未安装，请安装: brew install jq"
        exit 1
    fi
    
    # 1. 健康检查
    test_endpoint "健康检查" "GET" "$BASE_URL/health"
    
    # 2. 认证测试
    test_endpoint "认证服务测试" "GET" "$BASE_URL/auth/test"
    
    # 3. 检查用户名可用性
    test_endpoint "检查用户名可用性" "GET" "$BASE_URL/users/check-username?username=$TEST_USERNAME"
    
    # 4. 检查邮箱可用性
    test_endpoint "检查邮箱可用性" "GET" "$BASE_URL/users/check-email?email=$TEST_EMAIL"
    
    # 5. 用户注册
    register_data='{
        "username": "'$TEST_USERNAME'",
        "email": "'$TEST_EMAIL'",
        "password": "password123",
        "firstName": "Test",
        "lastName": "User",
        "phone": "+**********"
    }'
    
    print_info "测试: 用户注册"
    register_response=$(curl -s -X POST "$BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d "$register_data")
    
    if echo "$register_response" | jq . >/dev/null 2>&1; then
        success=$(echo "$register_response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            print_success "用户注册 - 成功"
            TOKEN=$(echo "$register_response" | jq -r '.data.accessToken')
            print_info "获取到JWT令牌: ${TOKEN:0:20}..."
            echo "$register_response" | jq '.' | head -15
        else
            print_error "用户注册 - 失败"
            echo "$register_response" | jq '.'
            exit 1
        fi
    else
        print_error "用户注册 - 无效响应"
        echo "Response: $register_response"
        exit 1
    fi
    echo ""
    
    # 6. 用户登录
    login_data='{
        "email": "'$TEST_EMAIL'",
        "password": "password123"
    }'
    test_endpoint "用户登录" "POST" "$BASE_URL/auth/login" "$login_data"
    
    # 7. 获取当前用户信息（需要认证）
    test_endpoint "获取当前用户信息" "GET" "$BASE_URL/users/me" "" "Authorization: Bearer $TOKEN"
    
    # 8. 根据用户名获取用户
    test_endpoint "根据用户名获取用户" "GET" "$BASE_URL/users/username/$TEST_USERNAME"
    
    # 9. 导游发现
    discover_data='{
        "latitude": 40.7128,
        "longitude": -74.0060,
        "radiusKm": 10,
        "specialties": ["CULTURAL"],
        "minRating": 4.0,
        "maxPrice": 100.00,
        "sortBy": "DISTANCE",
        "sortDirection": "ASC",
        "page": 0,
        "size": 5
    }'
    test_endpoint "导游发现" "POST" "$BASE_URL/guides/discover" "$discover_data"
    
    # 10. 获取导游详情
    test_endpoint "获取导游详情" "GET" "$BASE_URL/guides/1"
    
    # 11. 获取导游可用时间
    test_endpoint "获取导游可用时间" "GET" "$BASE_URL/guides/1/availability?startDate=2025-07-15&endDate=2025-07-20"
    
    # 12. 刷新令牌
    refresh_data='{
        "refreshToken": "dummy_refresh_token"
    }'
    test_endpoint "刷新令牌" "POST" "$BASE_URL/auth/refresh" "$refresh_data"
    
    print_header "API 测试完成"
    print_success "所有基础API端点已测试"
    print_info "JWT令牌: $TOKEN"
    print_info "测试用户: $TEST_USERNAME"
    print_info "测试邮箱: $TEST_EMAIL"
}

# 运行主函数
main "$@"
