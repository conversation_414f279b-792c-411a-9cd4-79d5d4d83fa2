#!/bin/bash

# 测试认证流程脚本
# 用法: ./scripts/test-auth-flow.sh

set -e

BASE_URL="http://localhost:8080/api/v1"
TIMESTAMP=$(date +%s)
TEST_USERNAME="testuser_$TIMESTAMP"
TEST_EMAIL="test_$<EMAIL>"
TEST_PASSWORD="password123"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}🚀 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# 测试用户注册
test_register() {
    print_info "测试用户注册..."
    
    local register_data='{
        "username": "'$TEST_USERNAME'",
        "email": "'$TEST_EMAIL'",
        "password": "'$TEST_PASSWORD'",
        "firstName": "Test",
        "lastName": "User",
        "phone": "+1234567890"
    }'
    
    local response=$(curl -s -X POST "$BASE_URL/auth/register" \
        -H "Content-Type: application/json" \
        -d "$register_data")
    
    if echo "$response" | jq . >/dev/null 2>&1; then
        local success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            print_success "用户注册成功"
            ACCESS_TOKEN=$(echo "$response" | jq -r '.data.token // empty')
            REFRESH_TOKEN=$(echo "$response" | jq -r '.data.refreshToken // empty')
            USER_ID=$(echo "$response" | jq -r '.data.user.id // empty')
            
            if [ -n "$ACCESS_TOKEN" ]; then
                print_info "获取到访问令牌: ${ACCESS_TOKEN:0:20}..."
            else
                print_error "未获取到访问令牌"
                return 1
            fi
            
            echo "$response" | jq '.' | head -15
            return 0
        else
            print_error "用户注册失败"
            echo "$response" | jq '.'
            return 1
        fi
    else
        print_error "注册响应格式错误"
        echo "Response: $response"
        return 1
    fi
}

# 测试用户登录
test_login() {
    print_info "测试用户登录..."
    
    local login_data='{
        "email": "'$TEST_EMAIL'",
        "password": "'$TEST_PASSWORD'"
    }'
    
    local response=$(curl -s -X POST "$BASE_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d "$login_data")
    
    if echo "$response" | jq . >/dev/null 2>&1; then
        local success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            print_success "用户登录成功"
            ACCESS_TOKEN=$(echo "$response" | jq -r '.data.token // empty')
            
            if [ -n "$ACCESS_TOKEN" ]; then
                print_info "获取到新的访问令牌: ${ACCESS_TOKEN:0:20}..."
            fi
            
            echo "$response" | jq '.' | head -10
            return 0
        else
            print_error "用户登录失败"
            echo "$response" | jq '.'
            return 1
        fi
    else
        print_error "登录响应格式错误"
        echo "Response: $response"
        return 1
    fi
}

# 测试获取当前用户信息
test_get_current_user() {
    if [ -z "$ACCESS_TOKEN" ]; then
        print_error "没有访问令牌，跳过获取用户信息测试"
        return 1
    fi
    
    print_info "测试获取当前用户信息..."
    
    local response=$(curl -s -X GET "$BASE_URL/auth/me" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    if echo "$response" | jq . >/dev/null 2>&1; then
        local success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            print_success "获取用户信息成功"
            echo "$response" | jq '.'
            return 0
        else
            print_error "获取用户信息失败"
            echo "$response" | jq '.'
            return 1
        fi
    else
        print_error "用户信息响应格式错误"
        echo "Response: $response"
        return 1
    fi
}

# 测试令牌刷新
test_refresh_token() {
    if [ -z "$REFRESH_TOKEN" ]; then
        print_error "没有刷新令牌，跳过令牌刷新测试"
        return 1
    fi
    
    print_info "测试令牌刷新..."
    
    local refresh_data='{
        "refreshToken": "'$REFRESH_TOKEN'"
    }'
    
    local response=$(curl -s -X POST "$BASE_URL/auth/refresh" \
        -H "Content-Type: application/json" \
        -d "$refresh_data")
    
    if echo "$response" | jq . >/dev/null 2>&1; then
        local success=$(echo "$response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            print_success "令牌刷新成功"
            echo "$response" | jq '.'
            return 0
        else
            print_error "令牌刷新失败"
            echo "$response" | jq '.'
            return 1
        fi
    else
        print_error "令牌刷新响应格式错误"
        echo "Response: $response"
        return 1
    fi
}

# 主测试流程
main() {
    print_header "认证流程测试开始"
    
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        print_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        print_error "jq 未安装，请安装: brew install jq"
        exit 1
    fi
    
    # 检查后端是否运行
    print_info "检查后端服务..."
    if ! curl -s "$BASE_URL/health" >/dev/null; then
        print_error "后端服务未运行，请先启动后端服务"
        exit 1
    fi
    print_success "后端服务正常运行"
    
    echo ""
    
    # 执行测试
    local tests_passed=0
    local tests_total=4
    
    # 1. 测试注册
    if test_register; then
        ((tests_passed++))
    fi
    echo ""
    
    # 2. 测试登录
    if test_login; then
        ((tests_passed++))
    fi
    echo ""
    
    # 3. 测试获取用户信息
    if test_get_current_user; then
        ((tests_passed++))
    fi
    echo ""
    
    # 4. 测试令牌刷新
    if test_refresh_token; then
        ((tests_passed++))
    fi
    echo ""
    
    # 总结
    print_header "测试结果总结"
    print_info "通过测试: $tests_passed/$tests_total"
    
    if [ $tests_passed -eq $tests_total ]; then
        print_success "所有认证流程测试通过！🎉"
        print_info "测试用户信息:"
        print_info "  用户名: $TEST_USERNAME"
        print_info "  邮箱: $TEST_EMAIL"
        print_info "  用户ID: $USER_ID"
        exit 0
    else
        print_error "部分测试失败，需要修复"
        exit 1
    fi
}

# 运行主函数
main "$@"
