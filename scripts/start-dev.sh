#!/bin/bash

# Tourna开发环境启动脚本

set -e

echo "🚀 启动Tourna开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 启动基础设施服务
echo "🐳 启动基础设施服务..."
docker-compose up -d postgres redis rabbitmq elasticsearch minio

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 启动后端服务（如果存在）
if [ -f "backend/pom.xml" ]; then
    echo "🔧 启动后端服务..."
    cd backend
    if command -v mvn &> /dev/null; then
        mvn spring-boot:run &
        BACKEND_PID=$!
        echo "后端服务PID: $BACKEND_PID"
    else
        echo "⚠️  Maven未安装，跳过后端启动"
    fi
    cd ..
fi

# 启动前端服务（如果存在）
if [ -f "frontend/package.json" ]; then
    echo "📱 启动前端服务..."
    cd frontend
    if command -v npm &> /dev/null; then
        npm start &
        FRONTEND_PID=$!
        echo "前端服务PID: $FRONTEND_PID"
    else
        echo "⚠️  npm未安装，跳过前端启动"
    fi
    cd ..
fi

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📋 服务访问地址："
echo "   🌐 PostgreSQL:     localhost:5432"
echo "   🔴 Redis:          localhost:6379"
echo "   🐰 RabbitMQ:       localhost:15672"
echo "   🔍 Elasticsearch:  localhost:9200"
echo "   📁 MinIO:          localhost:9001"
echo "   🔧 后端API:        localhost:8080"
echo "   📱 前端应用:       localhost:19006"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo "正在停止服务..."; docker-compose down; exit 0' INT
wait
