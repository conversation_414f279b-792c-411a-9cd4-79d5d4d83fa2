{"name": "tourna-frontend", "version": "1.0.0", "description": "Tourna - Global Travel Guide Platform Frontend", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "expo build:android", "build:ios": "expo build:ios", "build:web": "expo build:web", "eject": "expo eject", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.2", "expo": "~49.0.15", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-image-picker": "~14.3.2", "expo-location": "~16.1.0", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-status-bar": "~1.6.0", "i18next": "^23.7.6", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.60.0", "react-i18next": "^13.5.0", "react-native": "0.72.6", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.12.0", "react-native-maps": "1.7.1", "react-native-paper": "^5.11.6", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-vector-icons": "^10.0.2", "react-native-web": "~0.19.6", "react-redux": "^8.1.3", "socket.io-client": "^4.7.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.54.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.2.1", "typescript": "^5.1.3"}, "keywords": ["react-native", "expo", "travel", "guide", "tourism", "mobile-app"], "author": "Tourna Team", "license": "MIT", "private": true}