{"name": "compare-urls", "version": "2.0.0", "description": "Compare URLs by first normalizing them", "license": "MIT", "repository": "sindresorhus/compare-urls", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "ava"}, "files": ["index.js"], "keywords": ["compare", "equal", "same", "url", "uri", "normalize"], "dependencies": {"normalize-url": "^2.0.1"}, "devDependencies": {"ava": "*", "xo": "*"}}