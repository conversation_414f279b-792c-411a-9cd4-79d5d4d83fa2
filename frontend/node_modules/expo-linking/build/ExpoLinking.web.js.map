{"version": 3, "file": "ExpoLinking.web.js", "sourceRoot": "", "sources": ["../src/ExpoLinking.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,SAAS,MAAM,WAAW,CAAC;AAIlC,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,CAAC;AAE3B,MAAM,SAAS,GAAmE,EAAE,CAAC;AAErF,eAAe;IACb,gBAAgB,CAAC,IAAW,EAAE,QAAqB;QACjD,qCAAqC;QACrC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC5B,OAAO,EAAE,MAAM,KAAI,CAAC,EAAE,CAAC;SACxB;QAED,SAAS,CACP,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC/B,+BAA+B,IAAI,uBAAuB,CAC3D,CAAC;QACF,MAAM,cAAc,GAAsB,CAAC,WAAW,EAAE,EAAE,CACxD,QAAQ,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;QACvD,SAAS,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC,CAAC;QAC7C,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO;YACL,MAAM,EAAE,GAAG,EAAE;gBACX,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC3C,CAAC;SACF,CAAC;IACJ,CAAC;IAED,mBAAmB,CAAC,IAAW,EAAE,QAAqB;QACpD,qCAAqC;QACrC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC5B,OAAO;SACR;QACD,SAAS,CACP,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAC/B,kCAAkC,IAAI,wBAAwB,CAC/D,CAAC;QACF,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;QAChF,SAAS,CACP,aAAa,KAAK,CAAC,CAAC,EACpB,8EAA8E,CAC/E,CAAC;QACF,MAAM,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC;QAC/D,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;QAC7D,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,GAAW;QAC1B,uFAAuF;QACvF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,cAAc;YAAE,OAAO,EAAE,CAAC;QACxC,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,GAAW;QACvB,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,aAAa;YACb,MAAM,CAAC,QAAQ,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;SAC5D;IACH,CAAC;CACF,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport invariant from 'invariant';\n\nimport { NativeURLListener, URLListener } from './Linking.types';\n\nconst EventTypes = ['url'];\n\nconst listeners: { listener: URLListener; nativeListener: NativeURLListener }[] = [];\n\nexport default {\n  addEventListener(type: 'url', listener: URLListener): { remove(): void } {\n    // Do nothing in Node.js environments\n    if (!Platform.isDOMAvailable) {\n      return { remove() {} };\n    }\n\n    invariant(\n      EventTypes.indexOf(type) !== -1,\n      `Linking.addEventListener(): ${type} is not a valid event`\n    );\n    const nativeListener: NativeURLListener = (nativeEvent) =>\n      listener({ url: window.location.href, nativeEvent });\n    listeners.push({ listener, nativeListener });\n    window.addEventListener('message', nativeListener, false);\n    return {\n      remove: () => {\n        this.removeEventListener(type, listener);\n      },\n    };\n  },\n\n  removeEventListener(type: 'url', listener: URLListener): void {\n    // Do nothing in Node.js environments\n    if (!Platform.isDOMAvailable) {\n      return;\n    }\n    invariant(\n      EventTypes.indexOf(type) !== -1,\n      `Linking.removeEventListener(): ${type} is not a valid event.`\n    );\n    const listenerIndex = listeners.findIndex((pair) => pair.listener === listener);\n    invariant(\n      listenerIndex !== -1,\n      'Linking.removeEventListener(): cannot remove an unregistered event listener.'\n    );\n    const nativeListener = listeners[listenerIndex].nativeListener;\n    window.removeEventListener('message', nativeListener, false);\n    listeners.splice(listenerIndex, 1);\n  },\n\n  async canOpenURL(url: string): Promise<boolean> {\n    // In reality this should be able to return false for links like `chrome://` on chrome.\n    return true;\n  },\n\n  async getInitialURL(): Promise<string> {\n    if (!Platform.isDOMAvailable) return '';\n    return window.location.href;\n  },\n\n  async openURL(url: string): Promise<void> {\n    if (Platform.isDOMAvailable) {\n      // @ts-ignore\n      window.location = new URL(url, window.location).toString();\n    }\n  },\n};\n"]}