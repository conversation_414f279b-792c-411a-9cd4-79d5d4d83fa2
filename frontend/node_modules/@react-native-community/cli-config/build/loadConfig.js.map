{"version": 3, "names": ["getDependencyConfig", "root", "dependencyName", "finalConfig", "config", "userConfig", "isPlatform", "merge", "name", "platforms", "Object", "keys", "reduce", "dependency", "platform", "platformConfig", "dependencyConfig", "dependencies", "getReactNativeVersion", "reactNativePath", "semver", "version", "current", "major", "minor", "e", "UnknownProjectError", "loadConfig", "projectRoot", "findProjectRoot", "lazyProject", "readConfigFromDisk", "initialConfig", "path", "resolve", "resolveReactNativePath", "reactNativeVersion", "commands", "healthChecks", "project", "projectConfig", "Array", "from", "Set", "findDependencies", "acc", "localDependencyRoot", "resolveNodeModuleDir", "readDependencyConfigFromDisk", "length", "assign"], "sources": ["../src/loadConfig.ts"], "sourcesContent": ["import path from 'path';\nimport {\n  UserDependencyConfig,\n  ProjectConfig,\n  DependencyConfig,\n  UserConfig,\n  Config,\n} from '@react-native-community/cli-types';\nimport {\n  findProjectRoot,\n  version,\n  resolveNodeModuleDir,\n  UnknownProjectError,\n} from '@react-native-community/cli-tools';\nimport findDependencies from './findDependencies';\nimport resolveReactNativePath from './resolveReactNativePath';\nimport {\n  readConfigFromDisk,\n  readDependencyConfigFromDisk,\n} from './readConfigFromDisk';\nimport assign from './assign';\nimport merge from './merge';\n\nfunction getDependencyConfig(\n  root: string,\n  dependencyName: string,\n  finalConfig: Config,\n  config: UserDependencyConfig,\n  userConfig: UserConfig,\n  isPlatform: boolean,\n): DependencyConfig {\n  return merge(\n    {\n      root,\n      name: dependencyName,\n      platforms: Object.keys(finalConfig.platforms).reduce(\n        (dependency, platform) => {\n          const platformConfig = finalConfig.platforms[platform];\n          dependency[platform] =\n            // Linking platforms is not supported\n            isPlatform || !platformConfig\n              ? null\n              : platformConfig.dependencyConfig(\n                  root,\n                  config.dependency.platforms[platform],\n                );\n          return dependency;\n        },\n        {} as Config['platforms'],\n      ),\n    },\n    userConfig.dependencies[dependencyName] || {},\n  ) as DependencyConfig;\n}\n\n// Try our best to figure out what version of React Native we're running. This is\n// currently being used to get our deeplinks working, so it's only worried with\n// the major and minor version.\nfunction getReactNativeVersion(reactNativePath: string) {\n  try {\n    let semver = version.current(reactNativePath);\n    if (semver) {\n      // Retain only these version, since they correspond with our documentation.\n      return `${semver.major}.${semver.minor}`;\n    }\n  } catch (e) {\n    // If we don't seem to be in a well formed project, give up quietly.\n    if (!(e instanceof UnknownProjectError)) {\n      throw e;\n    }\n  }\n  return 'unknown';\n}\n\n/**\n * Loads CLI configuration\n */\nfunction loadConfig(projectRoot: string = findProjectRoot()): Config {\n  let lazyProject: ProjectConfig;\n  const userConfig = readConfigFromDisk(projectRoot);\n\n  const initialConfig: Config = {\n    root: projectRoot,\n    get reactNativePath() {\n      return userConfig.reactNativePath\n        ? path.resolve(projectRoot, userConfig.reactNativePath)\n        : resolveReactNativePath(projectRoot);\n    },\n    get reactNativeVersion() {\n      return getReactNativeVersion(initialConfig.reactNativePath);\n    },\n    dependencies: userConfig.dependencies,\n    commands: userConfig.commands,\n    healthChecks: [],\n    platforms: userConfig.platforms,\n    get project() {\n      if (lazyProject) {\n        return lazyProject;\n      }\n\n      lazyProject = {};\n      for (const platform in finalConfig.platforms) {\n        const platformConfig = finalConfig.platforms[platform];\n        if (platformConfig) {\n          lazyProject[platform] = platformConfig.projectConfig(\n            projectRoot,\n            userConfig.project[platform] || {},\n          );\n        }\n      }\n\n      return lazyProject;\n    },\n  };\n\n  const finalConfig = Array.from(\n    new Set([\n      ...Object.keys(userConfig.dependencies),\n      ...findDependencies(projectRoot),\n    ]),\n  ).reduce((acc: Config, dependencyName) => {\n    const localDependencyRoot =\n      userConfig.dependencies[dependencyName] &&\n      userConfig.dependencies[dependencyName].root;\n    try {\n      let root =\n        localDependencyRoot ||\n        resolveNodeModuleDir(projectRoot, dependencyName);\n      let config = readDependencyConfigFromDisk(root, dependencyName);\n\n      const isPlatform = Object.keys(config.platforms).length > 0;\n\n      return assign({}, acc, {\n        dependencies: assign({}, acc.dependencies, {\n          get [dependencyName](): DependencyConfig {\n            return getDependencyConfig(\n              root,\n              dependencyName,\n              finalConfig,\n              config,\n              userConfig,\n              isPlatform,\n            );\n          },\n        }),\n        commands: [...acc.commands, ...config.commands],\n        platforms: {\n          ...acc.platforms,\n          ...config.platforms,\n        },\n        healthChecks: [...acc.healthChecks, ...config.healthChecks],\n      }) as Config;\n    } catch {\n      return acc;\n    }\n  }, initialConfig);\n\n  return finalConfig;\n}\n\nexport default loadConfig;\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AACA;AACA;AAIA;AACA;AAA4B;AAE5B,SAASA,mBAAmB,CAC1BC,IAAY,EACZC,cAAsB,EACtBC,WAAmB,EACnBC,MAA4B,EAC5BC,UAAsB,EACtBC,UAAmB,EACD;EAClB,OAAO,IAAAC,cAAK,EACV;IACEN,IAAI;IACJO,IAAI,EAAEN,cAAc;IACpBO,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACR,WAAW,CAACM,SAAS,CAAC,CAACG,MAAM,CAClD,CAACC,UAAU,EAAEC,QAAQ,KAAK;MACxB,MAAMC,cAAc,GAAGZ,WAAW,CAACM,SAAS,CAACK,QAAQ,CAAC;MACtDD,UAAU,CAACC,QAAQ,CAAC;MAClB;MACAR,UAAU,IAAI,CAACS,cAAc,GACzB,IAAI,GACJA,cAAc,CAACC,gBAAgB,CAC7Bf,IAAI,EACJG,MAAM,CAACS,UAAU,CAACJ,SAAS,CAACK,QAAQ,CAAC,CACtC;MACP,OAAOD,UAAU;IACnB,CAAC,EACD,CAAC,CAAC;EAEN,CAAC,EACDR,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,IAAI,CAAC,CAAC,CAC9C;AACH;;AAEA;AACA;AACA;AACA,SAASgB,qBAAqB,CAACC,eAAuB,EAAE;EACtD,IAAI;IACF,IAAIC,MAAM,GAAGC,mBAAO,CAACC,OAAO,CAACH,eAAe,CAAC;IAC7C,IAAIC,MAAM,EAAE;MACV;MACA,OAAQ,GAAEA,MAAM,CAACG,KAAM,IAAGH,MAAM,CAACI,KAAM,EAAC;IAC1C;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV;IACA,IAAI,EAAEA,CAAC,YAAYC,+BAAmB,CAAC,EAAE;MACvC,MAAMD,CAAC;IACT;EACF;EACA,OAAO,SAAS;AAClB;;AAEA;AACA;AACA;AACA,SAASE,UAAU,CAACC,WAAmB,GAAG,IAAAC,2BAAe,GAAE,EAAU;EACnE,IAAIC,WAA0B;EAC9B,MAAMzB,UAAU,GAAG,IAAA0B,sCAAkB,EAACH,WAAW,CAAC;EAElD,MAAMI,aAAqB,GAAG;IAC5B/B,IAAI,EAAE2B,WAAW;IACjB,IAAIT,eAAe,GAAG;MACpB,OAAOd,UAAU,CAACc,eAAe,GAC7Bc,eAAI,CAACC,OAAO,CAACN,WAAW,EAAEvB,UAAU,CAACc,eAAe,CAAC,GACrD,IAAAgB,+BAAsB,EAACP,WAAW,CAAC;IACzC,CAAC;IACD,IAAIQ,kBAAkB,GAAG;MACvB,OAAOlB,qBAAqB,CAACc,aAAa,CAACb,eAAe,CAAC;IAC7D,CAAC;IACDF,YAAY,EAAEZ,UAAU,CAACY,YAAY;IACrCoB,QAAQ,EAAEhC,UAAU,CAACgC,QAAQ;IAC7BC,YAAY,EAAE,EAAE;IAChB7B,SAAS,EAAEJ,UAAU,CAACI,SAAS;IAC/B,IAAI8B,OAAO,GAAG;MACZ,IAAIT,WAAW,EAAE;QACf,OAAOA,WAAW;MACpB;MAEAA,WAAW,GAAG,CAAC,CAAC;MAChB,KAAK,MAAMhB,QAAQ,IAAIX,WAAW,CAACM,SAAS,EAAE;QAC5C,MAAMM,cAAc,GAAGZ,WAAW,CAACM,SAAS,CAACK,QAAQ,CAAC;QACtD,IAAIC,cAAc,EAAE;UAClBe,WAAW,CAAChB,QAAQ,CAAC,GAAGC,cAAc,CAACyB,aAAa,CAClDZ,WAAW,EACXvB,UAAU,CAACkC,OAAO,CAACzB,QAAQ,CAAC,IAAI,CAAC,CAAC,CACnC;QACH;MACF;MAEA,OAAOgB,WAAW;IACpB;EACF,CAAC;EAED,MAAM3B,WAAW,GAAGsC,KAAK,CAACC,IAAI,CAC5B,IAAIC,GAAG,CAAC,CACN,GAAGjC,MAAM,CAACC,IAAI,CAACN,UAAU,CAACY,YAAY,CAAC,EACvC,GAAG,IAAA2B,yBAAgB,EAAChB,WAAW,CAAC,CACjC,CAAC,CACH,CAAChB,MAAM,CAAC,CAACiC,GAAW,EAAE3C,cAAc,KAAK;IACxC,MAAM4C,mBAAmB,GACvBzC,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,IACvCG,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,CAACD,IAAI;IAC9C,IAAI;MACF,IAAIA,IAAI,GACN6C,mBAAmB,IACnB,IAAAC,gCAAoB,EAACnB,WAAW,EAAE1B,cAAc,CAAC;MACnD,IAAIE,MAAM,GAAG,IAAA4C,gDAA4B,EAAC/C,IAAI,EAAEC,cAAc,CAAC;MAE/D,MAAMI,UAAU,GAAGI,MAAM,CAACC,IAAI,CAACP,MAAM,CAACK,SAAS,CAAC,CAACwC,MAAM,GAAG,CAAC;MAE3D,OAAO,IAAAC,eAAM,EAAC,CAAC,CAAC,EAAEL,GAAG,EAAE;QACrB5B,YAAY,EAAE,IAAAiC,eAAM,EAAC,CAAC,CAAC,EAAEL,GAAG,CAAC5B,YAAY,EAAE;UACzC,KAAKf,cAAc,IAAsB;YACvC,OAAOF,mBAAmB,CACxBC,IAAI,EACJC,cAAc,EACdC,WAAW,EACXC,MAAM,EACNC,UAAU,EACVC,UAAU,CACX;UACH;QACF,CAAC,CAAC;QACF+B,QAAQ,EAAE,CAAC,GAAGQ,GAAG,CAACR,QAAQ,EAAE,GAAGjC,MAAM,CAACiC,QAAQ,CAAC;QAC/C5B,SAAS,EAAE;UACT,GAAGoC,GAAG,CAACpC,SAAS;UAChB,GAAGL,MAAM,CAACK;QACZ,CAAC;QACD6B,YAAY,EAAE,CAAC,GAAGO,GAAG,CAACP,YAAY,EAAE,GAAGlC,MAAM,CAACkC,YAAY;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC,MAAM;MACN,OAAOO,GAAG;IACZ;EACF,CAAC,EAAEb,aAAa,CAAC;EAEjB,OAAO7B,WAAW;AACpB;AAAC,eAEcwB,UAAU;AAAA"}