{"name": "@react-native-community/cli-config", "version": "11.3.10", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-tools": "11.3.10", "chalk": "^4.1.2", "cosmiconfig": "^5.1.0", "deepmerge": "^4.3.0", "glob": "^7.1.3", "joi": "^17.2.1"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "11.3.10", "@types/cosmiconfig": "^5.0.3", "@types/glob": "^7.1.1"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-config", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-config"}, "gitHead": "59e4dac7e56fb05f33508ff804c0eac7448c16a8"}