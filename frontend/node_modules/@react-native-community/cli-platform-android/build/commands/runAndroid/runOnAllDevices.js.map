{"version": 3, "names": ["runOnAllDevices", "args", "cmd", "adbPath", "androidProject", "devices", "adb", "getDevices", "length", "logger", "info", "result", "tryLaunchEmulator", "success", "error", "chalk", "dim", "warn", "variant", "binaryPath", "grad<PERSON><PERSON><PERSON><PERSON>", "getTaskNames", "appName", "mode", "tasks", "extraParams", "push", "port", "activeArchOnly", "architectures", "map", "device", "getCPU", "filter", "arch", "index", "array", "indexOf", "join", "debug", "execa", "stdio", "cwd", "sourceDir", "printRunDoctorTip", "createInstallError", "undefined", "for<PERSON>ach", "tryRunAdbReverse", "tryInstallAppOnDevice", "tryLaunchAppOnDevice", "packageName", "stderr", "toString", "message", "log", "includes", "bold", "CLIError"], "sources": ["../../../src/commands/runAndroid/runOnAllDevices.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport chalk from 'chalk';\nimport execa from 'execa';\nimport {Config} from '@react-native-community/cli-types';\nimport {\n  logger,\n  CLIError,\n  printRunDoctorTip,\n} from '@react-native-community/cli-tools';\nimport adb from './adb';\nimport tryRunAdbReverse from './tryRunAdbReverse';\nimport tryLaunchAppOnDevice from './tryLaunchAppOnDevice';\nimport tryLaunchEmulator from './tryLaunchEmulator';\nimport tryInstallAppOnDevice from './tryInstallAppOnDevice';\nimport {getTaskNames} from './getTaskNames';\nimport type {Flags} from '.';\n\ntype AndroidProject = NonNullable<Config['project']['android']>;\n\nasync function runOnAllDevices(\n  args: Flags,\n  cmd: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n) {\n  let devices = adb.getDevices(adbPath);\n  if (devices.length === 0) {\n    logger.info('Launching emulator...');\n    const result = await tryLaunchEmulator(adbPath);\n    if (result.success) {\n      logger.info('Successfully launched emulator.');\n      devices = adb.getDevices(adbPath);\n    } else {\n      logger.error(\n        `Failed to launch emulator. Reason: ${chalk.dim(result.error || '')}.`,\n      );\n      logger.warn(\n        'Please launch an emulator manually or connect a device. Otherwise app may fail to launch.',\n      );\n    }\n  }\n  if (args.variant) {\n    logger.warn(\n      '\"variant\" flag is deprecated and will be removed in future release. Please switch to \"mode\" flag.',\n    );\n  }\n\n  try {\n    if (!args.binaryPath) {\n      let gradleArgs = getTaskNames(\n        androidProject.appName,\n        args.mode || args.variant,\n        args.tasks,\n        'install',\n      );\n\n      if (args.extraParams) {\n        gradleArgs.push(...args.extraParams);\n      }\n\n      if (args.port != null) {\n        gradleArgs.push('-PreactNativeDevServerPort=' + args.port);\n      }\n\n      if (args.activeArchOnly) {\n        const architectures = devices\n          .map((device) => {\n            return adb.getCPU(adbPath, device);\n          })\n          .filter(\n            (arch, index, array) =>\n              arch != null && array.indexOf(arch) === index,\n          );\n\n        if (architectures.length > 0) {\n          logger.info(`Detected architectures ${architectures.join(', ')}`);\n          // `reactNativeDebugArchitectures` was renamed to `reactNativeArchitectures` in 0.68.\n          // Can be removed when 0.67 no longer needs to be supported.\n          gradleArgs.push(\n            '-PreactNativeDebugArchitectures=' + architectures.join(','),\n          );\n          gradleArgs.push(\n            '-PreactNativeArchitectures=' + architectures.join(','),\n          );\n        }\n      }\n\n      logger.info('Installing the app...');\n      logger.debug(\n        `Running command \"cd android && ${cmd} ${gradleArgs.join(' ')}\"`,\n      );\n\n      await execa(cmd, gradleArgs, {\n        stdio: ['inherit', 'inherit', 'pipe'],\n        cwd: androidProject.sourceDir,\n      });\n    }\n  } catch (error) {\n    printRunDoctorTip();\n    throw createInstallError(error as any);\n  }\n\n  (devices.length > 0 ? devices : [undefined]).forEach(\n    (device: string | void) => {\n      tryRunAdbReverse(args.port, device);\n      if (args.binaryPath && device) {\n        tryInstallAppOnDevice(args, adbPath, device, androidProject);\n      }\n      tryLaunchAppOnDevice(device, androidProject.packageName, adbPath, args);\n    },\n  );\n}\n\nfunction createInstallError(error: Error & {stderr: string}) {\n  const stderr = (error.stderr || '').toString();\n  let message = '';\n  // Pass the error message from the command to stdout because we pipe it to\n  // parent process so it's not visible\n  logger.log(stderr);\n\n  // Handle some common failures and make the errors more helpful\n  if (stderr.includes('No connected devices')) {\n    message =\n      'Make sure you have an Android emulator running or a device connected.';\n  } else if (\n    stderr.includes('licences have not been accepted') ||\n    stderr.includes('accept the SDK license')\n  ) {\n    message = `Please accept all necessary Android SDK licenses using Android SDK Manager: \"${chalk.bold(\n      '$ANDROID_HOME/tools/bin/sdkmanager --licenses',\n    )}.\"`;\n  }\n\n  return new CLIError(\n    `Failed to install the app.${message ? ' ' + message : ''}`,\n    error.message.length > 0 ? undefined : error,\n  );\n}\n\nexport default runOnAllDevices;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AAA4C;AArB5C;AACA;AACA;AACA;AACA;AACA;AACA;;AAoBA,eAAeA,eAAe,CAC5BC,IAAW,EACXC,GAAW,EACXC,OAAe,EACfC,cAA8B,EAC9B;EACA,IAAIC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;EACrC,IAAIE,OAAO,CAACG,MAAM,KAAK,CAAC,EAAE;IACxBC,kBAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC;IACpC,MAAMC,MAAM,GAAG,MAAM,IAAAC,0BAAiB,EAACT,OAAO,CAAC;IAC/C,IAAIQ,MAAM,CAACE,OAAO,EAAE;MAClBJ,kBAAM,CAACC,IAAI,CAAC,iCAAiC,CAAC;MAC9CL,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;IACnC,CAAC,MAAM;MACLM,kBAAM,CAACK,KAAK,CACT,sCAAqCC,gBAAK,CAACC,GAAG,CAACL,MAAM,CAACG,KAAK,IAAI,EAAE,CAAE,GAAE,CACvE;MACDL,kBAAM,CAACQ,IAAI,CACT,2FAA2F,CAC5F;IACH;EACF;EACA,IAAIhB,IAAI,CAACiB,OAAO,EAAE;IAChBT,kBAAM,CAACQ,IAAI,CACT,mGAAmG,CACpG;EACH;EAEA,IAAI;IACF,IAAI,CAAChB,IAAI,CAACkB,UAAU,EAAE;MACpB,IAAIC,UAAU,GAAG,IAAAC,0BAAY,EAC3BjB,cAAc,CAACkB,OAAO,EACtBrB,IAAI,CAACsB,IAAI,IAAItB,IAAI,CAACiB,OAAO,EACzBjB,IAAI,CAACuB,KAAK,EACV,SAAS,CACV;MAED,IAAIvB,IAAI,CAACwB,WAAW,EAAE;QACpBL,UAAU,CAACM,IAAI,CAAC,GAAGzB,IAAI,CAACwB,WAAW,CAAC;MACtC;MAEA,IAAIxB,IAAI,CAAC0B,IAAI,IAAI,IAAI,EAAE;QACrBP,UAAU,CAACM,IAAI,CAAC,6BAA6B,GAAGzB,IAAI,CAAC0B,IAAI,CAAC;MAC5D;MAEA,IAAI1B,IAAI,CAAC2B,cAAc,EAAE;QACvB,MAAMC,aAAa,GAAGxB,OAAO,CAC1ByB,GAAG,CAAEC,MAAM,IAAK;UACf,OAAOzB,YAAG,CAAC0B,MAAM,CAAC7B,OAAO,EAAE4B,MAAM,CAAC;QACpC,CAAC,CAAC,CACDE,MAAM,CACL,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,KACjBF,IAAI,IAAI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,KAAKC,KAAK,CAChD;QAEH,IAAIN,aAAa,CAACrB,MAAM,GAAG,CAAC,EAAE;UAC5BC,kBAAM,CAACC,IAAI,CAAE,0BAAyBmB,aAAa,CAACS,IAAI,CAAC,IAAI,CAAE,EAAC,CAAC;UACjE;UACA;UACAlB,UAAU,CAACM,IAAI,CACb,kCAAkC,GAAGG,aAAa,CAACS,IAAI,CAAC,GAAG,CAAC,CAC7D;UACDlB,UAAU,CAACM,IAAI,CACb,6BAA6B,GAAGG,aAAa,CAACS,IAAI,CAAC,GAAG,CAAC,CACxD;QACH;MACF;MAEA7B,kBAAM,CAACC,IAAI,CAAC,uBAAuB,CAAC;MACpCD,kBAAM,CAAC8B,KAAK,CACT,kCAAiCrC,GAAI,IAAGkB,UAAU,CAACkB,IAAI,CAAC,GAAG,CAAE,GAAE,CACjE;MAED,MAAM,IAAAE,gBAAK,EAACtC,GAAG,EAAEkB,UAAU,EAAE;QAC3BqB,KAAK,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;QACrCC,GAAG,EAAEtC,cAAc,CAACuC;MACtB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;IACd,IAAA8B,6BAAiB,GAAE;IACnB,MAAMC,kBAAkB,CAAC/B,KAAK,CAAQ;EACxC;EAEA,CAACT,OAAO,CAACG,MAAM,GAAG,CAAC,GAAGH,OAAO,GAAG,CAACyC,SAAS,CAAC,EAAEC,OAAO,CACjDhB,MAAqB,IAAK;IACzB,IAAAiB,yBAAgB,EAAC/C,IAAI,CAAC0B,IAAI,EAAEI,MAAM,CAAC;IACnC,IAAI9B,IAAI,CAACkB,UAAU,IAAIY,MAAM,EAAE;MAC7B,IAAAkB,8BAAqB,EAAChD,IAAI,EAAEE,OAAO,EAAE4B,MAAM,EAAE3B,cAAc,CAAC;IAC9D;IACA,IAAA8C,6BAAoB,EAACnB,MAAM,EAAE3B,cAAc,CAAC+C,WAAW,EAAEhD,OAAO,EAAEF,IAAI,CAAC;EACzE,CAAC,CACF;AACH;AAEA,SAAS4C,kBAAkB,CAAC/B,KAA+B,EAAE;EAC3D,MAAMsC,MAAM,GAAG,CAACtC,KAAK,CAACsC,MAAM,IAAI,EAAE,EAAEC,QAAQ,EAAE;EAC9C,IAAIC,OAAO,GAAG,EAAE;EAChB;EACA;EACA7C,kBAAM,CAAC8C,GAAG,CAACH,MAAM,CAAC;;EAElB;EACA,IAAIA,MAAM,CAACI,QAAQ,CAAC,sBAAsB,CAAC,EAAE;IAC3CF,OAAO,GACL,uEAAuE;EAC3E,CAAC,MAAM,IACLF,MAAM,CAACI,QAAQ,CAAC,iCAAiC,CAAC,IAClDJ,MAAM,CAACI,QAAQ,CAAC,wBAAwB,CAAC,EACzC;IACAF,OAAO,GAAI,gFAA+EvC,gBAAK,CAAC0C,IAAI,CAClG,+CAA+C,CAC/C,IAAG;EACP;EAEA,OAAO,KAAIC,oBAAQ,EAChB,6BAA4BJ,OAAO,GAAG,GAAG,GAAGA,OAAO,GAAG,EAAG,EAAC,EAC3DxC,KAAK,CAACwC,OAAO,CAAC9C,MAAM,GAAG,CAAC,GAAGsC,SAAS,GAAGhC,KAAK,CAC7C;AACH;AAAC,eAEcd,eAAe;AAAA"}