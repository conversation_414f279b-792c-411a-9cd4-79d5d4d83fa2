{"name": "expo-crypto", "version": "12.4.1", "description": "Expo universal module for crypto", "main": "build/Crypto.js", "types": "build/Crypto.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "react-native-web", "expo", "crypto", "ios", "android", "web", "native"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-crypto"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/crypto/", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"base64-js": "^1.3.0"}, "devDependencies": {"expo-module-scripts": "^3.0.0"}, "peerDependencies": {"expo": "*"}, "gitHead": "cf90d5c30c2a08a6493ebfa8aa3791aa70666759"}