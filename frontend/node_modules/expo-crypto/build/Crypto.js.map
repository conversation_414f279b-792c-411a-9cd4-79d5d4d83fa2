{"version": 3, "file": "Crypto.js", "sourceRoot": "", "sources": ["../src/Crypto.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,WAAW,CAAC;AACxC,OAAO,EAAE,mBAAmB,EAA2C,MAAM,mBAAmB,CAAC;AAEjG,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAA+B,MAAM,gBAAgB,CAAC;AACpG,OAAO,UAAU,MAAM,cAAc,CAAC;AAItC,cAAc,gBAAgB,CAAC;AAE/B,MAAM,WAAY,SAAQ,SAAS;IACjC,IAAI,GAAG,YAAY,CAAC;IAEpB,YAAY,OAAe;QACzB,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC,CAAC;IACnC,CAAC;CACF;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,UAAU,cAAc,CAAC,SAAiB;IAC9C,eAAe,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;IAC7C,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC7C,IAAI,OAAO,EAAE;QACX,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,aAAa,EAAE;YACtD,yCAAyC;YACzC,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,cAAc,CAAC,CAAC;YAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;gBACvC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;aAC5C;YACD,OAAO,KAAK,CAAC;SACd;KACF;IACD,IAAI,UAAU,CAAC,cAAc,EAAE;QAC7B,OAAO,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;KAClD;SAAM,IAAI,UAAU,CAAC,qBAAqB,EAAE;QAC3C,MAAM,MAAM,GAAG,UAAU,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;QAChE,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;KAC5B;SAAM;QACL,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;KAChE;AACH,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,SAAiB;IACzD,eAAe,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;IAClD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAC7C,IAAI,UAAU,CAAC,mBAAmB,EAAE;QAClC,OAAO,MAAM,UAAU,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;KAC7D;SAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE;QAChD,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;QAC3E,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;KAC5B;SAAM;QACL,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,qBAAqB,CAAC,CAAC;KACrE;AACH,CAAC;AAED,SAAS,eAAe,CAAC,KAAU,EAAE,UAAkB;IACrD,IACE,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,CAAC,KAAK,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,EACxB;QACA,MAAM,IAAI,SAAS,CACjB,gBAAgB,UAAU,IAAI,KAAK,+CAA+C,CACnF,CAAC;KACH;AACH,CAAC;AAED,SAAS,eAAe,CAAC,SAAgC;IACvD,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC7D,MAAM,IAAI,WAAW,CACnB,sEAAsE,MAAM,CAAC,IAAI,CAC/E,qBAAqB,CACtB,CAAC,IAAI,CAAC,mCAAmC,CAAC,EAAE,CAC9C,CAAC;KACH;AACH,CAAC;AAED,SAAS,UAAU,CAAC,IAAY;IAC9B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAM,IAAI,WAAW,CAAC,2CAA2C,CAAC,CAAC;KACpE;AACH,CAAC;AAED,SAAS,cAAc,CAAC,QAAwB;IAC9C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACrD,MAAM,IAAI,WAAW,CACnB,8DAA8D,MAAM,CAAC,IAAI,CACvE,cAAc,CACf,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAC9B,CAAC;KACH;AACH,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,SAAgC,EAChC,IAAY,EACZ,UAA+B,EAAE,QAAQ,EAAE,cAAc,CAAC,GAAG,EAAE;IAE/D,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE;QACjC,MAAM,IAAI,mBAAmB,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;KACnE;IAED,eAAe,CAAC,SAAS,CAAC,CAAC;IAC3B,UAAU,CAAC,IAAI,CAAC,CAAC;IACjB,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAEjC,OAAO,MAAM,UAAU,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,eAAe,CAC7B,UAAa;IAEb,UAAU,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;IACvC,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,UAAU,UAAU;IACxB,OAAO,UAAU,CAAC,UAAU,EAAE,CAAC;AACjC,CAAC;AAED,MAAM,aAAa,GAAG;IACpB,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE;IAChC,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE;IAClC,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE;IAClC,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE,EAAE;IAClC,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE;IAC/B,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE;IAC/B,CAAC,qBAAqB,CAAC,GAAG,CAAC,EAAE,EAAE;CAChC,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,MAAM,UAAU,MAAM,CAAC,SAAgC,EAAE,IAAkB;IACzE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI;YACF,IAAI,OAAO,UAAU,CAAC,WAAW,KAAK,UAAU,EAAE;gBAChD,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;aAClD;iBAAM;gBACL,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;gBACxD,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;gBAC3C,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aACxB;SACF;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,CAAC,KAAK,CAAC,CAAC;SACf;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { toByteArray } from 'base64-js';\nimport { UnavailabilityError, UintBasedTypedArray, IntBasedTypedArray } from 'expo-modules-core';\n\nimport { CryptoDigestAlgorithm, CryptoEncoding, CryptoDigestOptions, Digest } from './Crypto.types';\nimport ExpoCrypto from './ExpoCrypto';\n\ndeclare const global: any;\n\nexport * from './Crypto.types';\n\nclass CryptoError extends TypeError {\n  code = 'ERR_CRYPTO';\n\n  constructor(message: string) {\n    super(`expo-crypto: ${message}`);\n  }\n}\n\n// @needsAudit\n/**\n * Generates completely random bytes using native implementations. The `byteCount` property\n * is a `number` indicating the number of bytes to generate in the form of a `Uint8Array`.\n * Falls back to `Math.random` during development to prevent issues with React Native Debugger.\n * @param byteCount - A number within the range from `0` to `1024`. Anything else will throw a `TypeError`.\n * @return An array of random bytes with the same length as the `byteCount`.\n */\nexport function getRandomBytes(byteCount: number): Uint8Array {\n  assertByteCount(byteCount, 'getRandomBytes');\n  const validByteCount = Math.floor(byteCount);\n  if (__DEV__) {\n    if (!global.nativeCallSyncHook || global.__REMOTEDEV__) {\n      // remote javascript debugging is enabled\n      const array = new Uint8Array(validByteCount);\n      for (let i = 0; i < validByteCount; i++) {\n        array[i] = Math.floor(Math.random() * 256);\n      }\n      return array;\n    }\n  }\n  if (ExpoCrypto.getRandomBytes) {\n    return ExpoCrypto.getRandomBytes(validByteCount);\n  } else if (ExpoCrypto.getRandomBase64String) {\n    const base64 = ExpoCrypto.getRandomBase64String(validByteCount);\n    return toByteArray(base64);\n  } else {\n    throw new UnavailabilityError('expo-crypto', 'getRandomBytes');\n  }\n}\n\n// @needsAudit\n/**\n * Generates completely random bytes using native implementations. The `byteCount` property\n * is a `number` indicating the number of bytes to generate in the form of a `Uint8Array`.\n * @param byteCount - A number within the range from `0` to `1024`. Anything else will throw a `TypeError`.\n * @return A promise that fulfills with an array of random bytes with the same length as the `byteCount`.\n */\nexport async function getRandomBytesAsync(byteCount: number): Promise<Uint8Array> {\n  assertByteCount(byteCount, 'getRandomBytesAsync');\n  const validByteCount = Math.floor(byteCount);\n  if (ExpoCrypto.getRandomBytesAsync) {\n    return await ExpoCrypto.getRandomBytesAsync(validByteCount);\n  } else if (ExpoCrypto.getRandomBase64StringAsync) {\n    const base64 = await ExpoCrypto.getRandomBase64StringAsync(validByteCount);\n    return toByteArray(base64);\n  } else {\n    throw new UnavailabilityError('expo-crypto', 'getRandomBytesAsync');\n  }\n}\n\nfunction assertByteCount(value: any, methodName: string): void {\n  if (\n    typeof value !== 'number' ||\n    isNaN(value) ||\n    Math.floor(value) < 0 ||\n    Math.floor(value) > 1024\n  ) {\n    throw new TypeError(\n      `expo-crypto: ${methodName}(${value}) expected a valid number from range 0...1024`\n    );\n  }\n}\n\nfunction assertAlgorithm(algorithm: CryptoDigestAlgorithm): void {\n  if (!Object.values(CryptoDigestAlgorithm).includes(algorithm)) {\n    throw new CryptoError(\n      `Invalid algorithm provided. Expected one of: CryptoDigestAlgorithm.${Object.keys(\n        CryptoDigestAlgorithm\n      ).join(', AlgCryptoDigestAlgorithmorithm.')}`\n    );\n  }\n}\n\nfunction assertData(data: string): void {\n  if (typeof data !== 'string') {\n    throw new CryptoError(`Invalid data provided. Expected a string.`);\n  }\n}\n\nfunction assertEncoding(encoding: CryptoEncoding): void {\n  if (!Object.values(CryptoEncoding).includes(encoding)) {\n    throw new CryptoError(\n      `Invalid encoding provided. Expected one of: CryptoEncoding.${Object.keys(\n        CryptoEncoding\n      ).join(', CryptoEncoding.')}`\n    );\n  }\n}\n\n// @needsAudit\n/**\n * The `digestStringAsync()` method of `Crypto` generates a digest of the supplied `data` string with the provided digest `algorithm`.\n * A digest is a short fixed-length value derived from some variable-length input. **Cryptographic digests** should exhibit _collision-resistance_,\n * meaning that it's very difficult to generate multiple inputs that have equal digest values.\n * You can specify the returned string format as one of `CryptoEncoding`. By default, the resolved value will be formatted as a `HEX` string.\n * On web, this method can only be called from a secure origin (HTTPS) otherwise, an error will be thrown.\n *\n * @param algorithm The cryptographic hash function to use to transform a block of data into a fixed-size output.\n * @param data The value that will be used to generate a digest.\n * @param options Format of the digest string. Defaults to: `CryptoDigestOptions.HEX`.\n * @return Return a Promise which fulfills with a value representing the hashed input.\n *\n * @example\n * ```ts\n * const digest = await Crypto.digestStringAsync(\n *   Crypto.CryptoDigestAlgorithm.SHA512,\n *   '🥓 Easy to Digest! 💙'\n * );\n * ```\n */\nexport async function digestStringAsync(\n  algorithm: CryptoDigestAlgorithm,\n  data: string,\n  options: CryptoDigestOptions = { encoding: CryptoEncoding.HEX }\n): Promise<Digest> {\n  if (!ExpoCrypto.digestStringAsync) {\n    throw new UnavailabilityError('expo-crypto', 'digestStringAsync');\n  }\n\n  assertAlgorithm(algorithm);\n  assertData(data);\n  assertEncoding(options.encoding);\n\n  return await ExpoCrypto.digestStringAsync(algorithm, data, options);\n}\n\n/**\n * The `getRandomValues()` method of `Crypto` fills a provided `TypedArray` with cryptographically secure random values.\n *\n * @param typedArray An integer based [`TypedArray`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/TypedArray) to fill with cryptographically secure random values. It modifies the input array in place.\n * @return The input array filled with cryptographically secure random values.\n *\n * @example\n * ```ts\n * const byteArray = new Uint8Array(16);\n * Crypto.getRandomValues(byteArray);\n * console.log('Your lucky bytes: ' + byteArray);\n * ```\n */\nexport function getRandomValues<T extends IntBasedTypedArray | UintBasedTypedArray>(\n  typedArray: T\n): T {\n  ExpoCrypto.getRandomValues(typedArray);\n  return typedArray;\n}\n\n/**\n * The `randomUUID()` method returns a unique identifier based on the V4 UUID spec (RFC4122).\n * It uses cryptographically secure random values to generate the UUID.\n *\n * @return A string containing a newly generated UUIDv4 identifier\n * @example\n * ```ts\n * const UUID = Crypto.randomUUID();\n * console.log('Your UUID: ' + UUID);\n * ```\n */\nexport function randomUUID(): string {\n  return ExpoCrypto.randomUUID();\n}\n\nconst digestLengths = {\n  [CryptoDigestAlgorithm.SHA1]: 20,\n  [CryptoDigestAlgorithm.SHA256]: 32,\n  [CryptoDigestAlgorithm.SHA384]: 48,\n  [CryptoDigestAlgorithm.SHA512]: 64,\n  [CryptoDigestAlgorithm.MD2]: 16,\n  [CryptoDigestAlgorithm.MD4]: 16,\n  [CryptoDigestAlgorithm.MD5]: 16,\n};\n\n/**\n * The `digest()` method of `Crypto` generates a digest of the supplied `TypedArray` of bytes `data` with the provided digest `algorithm`.\n * A digest is a short fixed-length value derived from some variable-length input. **Cryptographic digests** should exhibit _collision-resistance_,\n * meaning that it's very difficult to generate multiple inputs that have equal digest values.\n * On web, this method can only be called from a secure origin (HTTPS) otherwise, an error will be thrown.\n *\n * @param algorithm The cryptographic hash function to use to transform a block of data into a fixed-size output.\n * @param data The value that will be used to generate a digest.\n * @return A Promise which fulfills with an ArrayBuffer representing the hashed input.\n * @example\n * ```ts\n * const array = new Uint8Array([1, 2, 3, 4, 5]);\n * const digest = await Crypto.digest(Crypto.CryptoDigestAlgorithm.SHA512, array);\n * console.log('Your digest: ' + digest);\n * ```\n */\nexport function digest(algorithm: CryptoDigestAlgorithm, data: BufferSource): Promise<ArrayBuffer> {\n  return new Promise((resolve, reject) => {\n    try {\n      if (typeof ExpoCrypto.digestAsync === 'function') {\n        resolve(ExpoCrypto.digestAsync(algorithm, data));\n      } else {\n        const output = new Uint8Array(digestLengths[algorithm]);\n        ExpoCrypto.digest(algorithm, output, data);\n        resolve(output.buffer);\n      }\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n"]}