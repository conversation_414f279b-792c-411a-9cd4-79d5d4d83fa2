{"name": "expo-auth-session", "version": "5.0.2", "description": "Expo module for browser-based authentication", "main": "build/AuthSession.js", "types": "build/AuthSession.d.ts", "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-auth-session", "auth", "o<PERSON>h", "authentication", "auth-session"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-auth-session"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/auth-session", "dependencies": {"expo-constants": "~14.4.2", "expo-crypto": "~12.4.0", "expo-linking": "~5.0.0", "expo-web-browser": "~12.3.0", "invariant": "^2.2.4", "qs": "^6.11.0"}, "devDependencies": {"@types/qs": "^6.9.7", "expo-module-scripts": "^3.0.0"}, "jest": {"preset": "expo-module-scripts"}, "gitHead": "4a38f32842594bb0ef39228dacde53042f12a47b"}