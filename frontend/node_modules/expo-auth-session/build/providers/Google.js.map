{"version": 3, "file": "Google.js", "sourceRoot": "", "sources": ["../../src/providers/Google.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACrD,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AAExC,OAAO,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AACjF,OAAO,EACL,WAAW,EAMX,sBAAsB,EACtB,eAAe,EACf,MAAM,EACN,YAAY,GACb,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAEzE,MAAM,QAAQ,GAAG;IACf,cAAc,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE;IAC3C,aAAa,EAAE;QACb,QAAQ;QACR,kDAAkD;QAClD,gDAAgD;KACjD;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAsB;IAC1C,qBAAqB,EAAE,8CAA8C;IACrE,aAAa,EAAE,qCAAqC;IACpD,kBAAkB,EAAE,sCAAsC;IAC1D,gBAAgB,EAAE,kDAAkD;CACrE,CAAC;AA6CF,cAAc;AACd;;GAEG;AACH,MAAM,iBAAkB,SAAQ,WAAW;IACzC,KAAK,CAAU;IAEf,YAAY,EACV,QAAQ,EACR,SAAS,EACT,aAAa,EACb,WAAW,GAAG,EAAE,EAChB,YAAY,EACZ,GAAG,MAAM,EACe;QACxB,MAAM,WAAW,GAAG;YAClB,GAAG,WAAW;SACf,CAAC;QACF,IAAI,QAAQ;YAAE,WAAW,CAAC,EAAE,GAAG,QAAQ,CAAC;QACxC,IAAI,SAAS;YAAE,WAAW,CAAC,UAAU,GAAG,SAAS,CAAC;QAClD,IAAI,aAAa;YAAE,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;QAE7D,2BAA2B;QAC3B,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC1E,MAAM,UAAU,GACd,MAAM,CAAC,YAAY,KAAK,YAAY,CAAC,KAAK,IAAI,MAAM,CAAC,YAAY,KAAK,YAAY,CAAC,OAAO,CAAC;QAC7F,IAAI,UAAU,EAAE;YACd,0CAA0C;YAC1C,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;SACxB;QACD,IAAI,iBAAqC,CAAC;QAC1C,6DAA6D;QAC7D,IAAI,MAAM,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,EAAE;YACpE,4EAA4E;YAC5E,iBAAiB,GAAG,YAAY,CAAC;SAClC;QACD,KAAK,CAAC;YACJ,GAAG,MAAM;YACT,YAAY,EAAE,iBAAiB;YAC/B,MAAM;YACN,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB;QAC7B,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,KAAK,CAAC,yBAAyB,EAAE,CAAC;QAChF,IAAI,MAAM,CAAC,YAAY,KAAK,YAAY,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACrF,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBACf,IAAI,CAAC,KAAK,GAAG,MAAM,sBAAsB,CAAC,EAAE,CAAC,CAAC;aAC/C;YACD,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;SAChC;QACD,OAAO;YACL,GAAG,MAAM;YACT,WAAW;SACZ,CAAC;IACJ,CAAC;CACF;AAED;;;;;;;;;;;;GAYG;AACH,MAAM,UAAU,qBAAqB,CACnC,MAAwC,EACxC,qBAA6D,EAAE;IAM/D,MAAM,SAAS,GAAG,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC;IAExC,OAAO,cAAc,CACnB;QACE,GAAG,MAAM;QACT,YAAY;QACV,yDAAyD;QACzD,CAAC,MAAM,CAAC,YAAY;YACpB,2FAA2F;YAC3F,SAAS;YACP,CAAC,CAAC,YAAY,CAAC,OAAO;YACtB,CAAC,CAAC,SAAS;KAChB,EACD,EAAE,GAAG,kBAAkB,EAAE,CAC1B,CAAC;AACJ,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,cAAc,CAC5B,SAA2C,EAAE,EAC7C,qBAA6D,EAAE;IAM/D,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAW,EAAE;QACpC,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC;YACnC,GAAG,EAAE,aAAa;YAClB,OAAO,EAAE,iBAAiB;YAC1B,OAAO,EAAE,aAAa;SACvB,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAmB,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC;QAChE,iBAAiB,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACpD,OAAO,QAAQ,CAAC;IAClB,CAAC,EAAE;QACD,MAAM,CAAC,YAAY;QACnB,MAAM,CAAC,WAAW;QAClB,MAAM,CAAC,eAAe;QACtB,MAAM,CAAC,WAAW;QAClB,MAAM,CAAC,QAAQ;KAChB,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;QAChC,mBAAmB;QACnB,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,WAAW,EAAE;YAC9C,OAAO,MAAM,CAAC,YAAY,CAAC;SAC5B;QACD,yFAAyF;QACzF,sGAAsG;QACtG,MAAM,cAAc,GAAG,QAAQ,CAAC,EAAE,KAAK,KAAK,CAAC;QAC7C,8FAA8F;QAC9F,IAAI,MAAM,CAAC,YAAY,IAAI,cAAc,EAAE;YACzC,OAAO,YAAY,CAAC,IAAI,CAAC;SAC1B;QACD,4HAA4H;QAC5H,OAAO,YAAY,CAAC,KAAK,CAAC;IAC5B,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;IAE/C,MAAM,WAAW,GAAG,OAAO,CAAC,GAAW,EAAE;QACvC,IAAI,OAAO,MAAM,CAAC,WAAW,KAAK,WAAW,EAAE;YAC7C,OAAO,MAAM,CAAC,WAAW,CAAC;SAC3B;QAED,OAAO,eAAe,CAAC;YACrB,MAAM,EAAE,GAAG,WAAW,CAAC,aAAa,iBAAiB;YACrD,GAAG,kBAAkB;YACrB,+DAA+D;SAChE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAE7C,MAAM,WAAW,GAAG,OAAO,CAAC,GAA2C,EAAE;QACvE,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEnE,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC;SAC7B;QACD,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;SACtC;QACD,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC;SACtC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;IAElF,MAAM,OAAO,GAAG,oBAAoB,CAClC;QACE,GAAG,MAAM;QACT,YAAY;QACZ,WAAW;QACX,QAAQ;QACR,WAAW;KACZ,EACD,SAAS,EACT,iBAAiB,CAClB,CAAC;IAEF,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,oBAAoB,CAAC,OAAO,EAAE,SAAS,EAAE;QACrE,cAAc,EAAE,QAAQ,CAAC,cAAc;KACxC,CAAC,CAAC;IAEH,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAA2B,IAAI,CAAC,CAAC;IAE7E,MAAM,sBAAsB,GAAG,OAAO,CAAC,GAAG,EAAE;QAC1C,kBAAkB;QAClB,IAAI,OAAO,MAAM,CAAC,sBAAsB,KAAK,WAAW,EAAE;YACxD,OAAO,MAAM,CAAC,sBAAsB,CAAC;SACtC;QAED,iEAAiE;QACjE,OAAO,MAAM,EAAE,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;IACpF,CAAC,EAAE,CAAC,MAAM,CAAC,sBAAsB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAElD,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,sBAAsB,IAAI,MAAM,EAAE,IAAI,KAAK,SAAS,EAAE;YACxD,MAAM,eAAe,GAAG,IAAI,kBAAkB,CAAC;gBAC7C,QAAQ;gBACR,YAAY,EAAE,MAAM,CAAC,YAAY;gBACjC,WAAW;gBACX,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI;gBACxB,WAAW,EAAE;oBACX,aAAa,EAAE,OAAO,EAAE,YAAY,IAAI,EAAE;iBAC3C;aACF,CAAC,CAAC;YACH,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;gBAC9D,IAAI,SAAS,EAAE;oBACb,aAAa,CAAC;wBACZ,GAAG,MAAM;wBACT,MAAM,EAAE;4BACN,QAAQ,EAAE,cAAc,EAAE,OAAO,IAAI,EAAE;4BACvC,YAAY,EAAE,cAAc,CAAC,WAAW;4BACxC,GAAG,MAAM,CAAC,MAAM;yBACjB;wBACD,cAAc;qBACf,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,aAAa,CAAC,MAAM,CAAC,CAAC;SACvB;QACD,OAAO,GAAG,EAAE;YACV,SAAS,GAAG,KAAK,CAAC;QACpB,CAAC,CAAC;IACJ,CAAC,EAAE;QACD,QAAQ;QACR,WAAW;QACX,sBAAsB;QACtB,MAAM,CAAC,YAAY;QACnB,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC;QACxB,OAAO,EAAE,YAAY;QACrB,MAAM;KACP,CAAC,CAAC;IAEH,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;AAC5C,CAAC", "sourcesContent": ["import * as Application from 'expo-application';\nimport { useEffect, useMemo, useState } from 'react';\nimport { Platform } from 'react-native';\n\nimport { useAuthRequestResult, useLoadedAuthRequest } from '../AuthRequestHooks';\nimport {\n  AuthRequest,\n  AuthRequestConfig,\n  AuthRequestPromptOptions,\n  AuthSessionRedirectUriOptions,\n  AuthSessionResult,\n  DiscoveryDocument,\n  generateHexStringAsync,\n  makeRedirectUri,\n  Prompt,\n  ResponseType,\n} from '../AuthSession';\nimport { AccessTokenRequest } from '../TokenRequest';\nimport { ProviderAuthRequestConfig } from './Provider.types';\nimport { applyRequiredScopes, invariantClientId } from './ProviderUtils';\n\nconst settings = {\n  windowFeatures: { width: 515, height: 680 },\n  minimumScopes: [\n    'openid',\n    'https://www.googleapis.com/auth/userinfo.profile',\n    'https://www.googleapis.com/auth/userinfo.email',\n  ],\n};\n\nexport const discovery: DiscoveryDocument = {\n  authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',\n  tokenEndpoint: 'https://oauth2.googleapis.com/token',\n  revocationEndpoint: 'https://oauth2.googleapis.com/revoke',\n  userInfoEndpoint: 'https://openidconnect.googleapis.com/v1/userinfo',\n};\n\n// @needsAudit\n/**\n * @deprecated See [Google authentication](/guides/google-authentication/).\n */\nexport interface GoogleAuthRequestConfig extends ProviderAuthRequestConfig {\n  /**\n   * If the user's email address is known ahead of time, it can be supplied to be the default option.\n   * If the user has approved access for this app in the past then auth may return without any further interaction.\n   */\n  loginHint?: string;\n  /**\n   * When `true`, the service will allow the user to switch between accounts (if possible).\n   * @default false.\n   */\n  selectAccount?: boolean;\n  /**\n   * Proxy client ID for use in the Expo client on Android and iOS.\n   */\n  expoClientId?: string;\n  /**\n   * Expo web client ID for use in the browser.\n   */\n  webClientId?: string;\n  /**\n   * iOS native client ID for use in standalone, bare workflow, and custom clients.\n   */\n  iosClientId?: string;\n  /**\n   * Android native client ID for use in standalone, and bare workflow.\n   */\n  androidClientId?: string;\n  /**\n   * Should the hook automatically exchange the response code for an authentication token.\n   *\n   * Defaults to `true` on installed apps (Android, iOS) when `ResponseType.Code` is used (default).\n   */\n  shouldAutoExchangeCode?: boolean;\n  /**\n   * Language code ISO 3166-1 alpha-2 region code, such as 'it' or 'pt-PT'.\n   */\n  language?: string;\n}\n\n// @needsAudit\n/**\n * Extends [`AuthRequest`](#authrequest) and accepts [`GoogleAuthRequestConfig`](#googleauthrequestconfig) in the constructor.\n */\nclass GoogleAuthRequest extends AuthRequest {\n  nonce?: string;\n\n  constructor({\n    language,\n    loginHint,\n    selectAccount,\n    extraParams = {},\n    clientSecret,\n    ...config\n  }: GoogleAuthRequestConfig) {\n    const inputParams = {\n      ...extraParams,\n    };\n    if (language) inputParams.hl = language;\n    if (loginHint) inputParams.login_hint = loginHint;\n    if (selectAccount) inputParams.prompt = Prompt.SelectAccount;\n\n    // Apply the default scopes\n    const scopes = applyRequiredScopes(config.scopes, settings.minimumScopes);\n    const isImplicit =\n      config.responseType === ResponseType.Token || config.responseType === ResponseType.IdToken;\n    if (isImplicit) {\n      // PKCE must be disabled in implicit mode.\n      config.usePKCE = false;\n    }\n    let inputClientSecret: string | undefined;\n    //  Google will throw if you attempt to use the client secret\n    if (config.responseType && config.responseType !== ResponseType.Code) {\n      // TODO: maybe warn that you shouldn't store the client secret on the client\n      inputClientSecret = clientSecret;\n    }\n    super({\n      ...config,\n      clientSecret: inputClientSecret,\n      scopes,\n      extraParams: inputParams,\n    });\n  }\n\n  /**\n   * Load and return a valid auth request based on the input config.\n   */\n  async getAuthRequestConfigAsync(): Promise<AuthRequestConfig> {\n    const { extraParams = {}, ...config } = await super.getAuthRequestConfigAsync();\n    if (config.responseType === ResponseType.IdToken && !extraParams.nonce && !this.nonce) {\n      if (!this.nonce) {\n        this.nonce = await generateHexStringAsync(16);\n      }\n      extraParams.nonce = this.nonce;\n    }\n    return {\n      ...config,\n      extraParams,\n    };\n  }\n}\n\n/**\n * Load an authorization request with an ID Token for authentication with Firebase.\n *\n * Returns a loaded request, a response, and a prompt method.\n * When the prompt method completes then the response will be fulfilled.\n *\n * The id token can be retrieved with `response.params.id_token`.\n *\n * - [Get Started](https://docs.expo.dev/guides/authentication/#google)\n *\n * @param config\n * @param redirectUriOptions\n */\nexport function useIdTokenAuthRequest(\n  config: Partial<GoogleAuthRequestConfig>,\n  redirectUriOptions: Partial<AuthSessionRedirectUriOptions> = {}\n): [\n  GoogleAuthRequest | null,\n  AuthSessionResult | null,\n  (options?: AuthRequestPromptOptions) => Promise<AuthSessionResult>\n] {\n  const isWebAuth = Platform.OS === 'web';\n\n  return useAuthRequest(\n    {\n      ...config,\n      responseType:\n        // If the client secret is provided then code can be used\n        !config.clientSecret &&\n        // When web auth is used, we can request the `id_token` directly without exchanging a code.\n        isWebAuth\n          ? ResponseType.IdToken\n          : undefined,\n    },\n    { ...redirectUriOptions }\n  );\n}\n\n/**\n * Load an authorization request.\n * Returns a loaded request, a response, and a prompt method.\n * When the prompt method completes, then the response will be fulfilled.\n *\n * - [Get Started](https://docs.expo.dev/guides/authentication/#google)\n *\n * @param config\n * @param redirectUriOptions\n */\nexport function useAuthRequest(\n  config: Partial<GoogleAuthRequestConfig> = {},\n  redirectUriOptions: Partial<AuthSessionRedirectUriOptions> = {}\n): [\n  GoogleAuthRequest | null,\n  AuthSessionResult | null,\n  (options?: AuthRequestPromptOptions) => Promise<AuthSessionResult>\n] {\n  const clientId = useMemo((): string => {\n    const propertyName = Platform.select({\n      ios: 'iosClientId',\n      android: 'androidClientId',\n      default: 'webClientId',\n    });\n\n    const clientId = config[propertyName as any] ?? config.clientId;\n    invariantClientId(propertyName, clientId, 'Google');\n    return clientId;\n  }, [\n    config.expoClientId,\n    config.iosClientId,\n    config.androidClientId,\n    config.webClientId,\n    config.clientId,\n  ]);\n\n  const responseType = useMemo(() => {\n    // Allow overrides.\n    if (typeof config.responseType !== 'undefined') {\n      return config.responseType;\n    }\n    // You can only use `response_token=code` on installed apps (iOS, Android without proxy).\n    // Installed apps can auto exchange without a client secret and get the token and id-token (Firebase).\n    const isInstalledApp = Platform.OS !== 'web';\n    // If the user provided the client secret (they shouldn't!) then use code exchange by default.\n    if (config.clientSecret || isInstalledApp) {\n      return ResponseType.Code;\n    }\n    // This seems the most pragmatic option since it can result in a full authentication on web and proxy platforms as expected.\n    return ResponseType.Token;\n  }, [config.responseType, config.clientSecret]);\n\n  const redirectUri = useMemo((): string => {\n    if (typeof config.redirectUri !== 'undefined') {\n      return config.redirectUri;\n    }\n\n    return makeRedirectUri({\n      native: `${Application.applicationId}:/oauthredirect`,\n      ...redirectUriOptions,\n      // native: `com.googleusercontent.apps.${guid}:/oauthredirect`,\n    });\n  }, [config.redirectUri, redirectUriOptions]);\n\n  const extraParams = useMemo((): GoogleAuthRequestConfig['extraParams'] => {\n    const output = config.extraParams ? { ...config.extraParams } : {};\n\n    if (config.language) {\n      output.hl = output.language;\n    }\n    if (config.loginHint) {\n      output.login_hint = output.loginHint;\n    }\n    if (config.selectAccount) {\n      output.prompt = Prompt.SelectAccount;\n    }\n    return output;\n  }, [config.extraParams, config.language, config.loginHint, config.selectAccount]);\n\n  const request = useLoadedAuthRequest(\n    {\n      ...config,\n      responseType,\n      extraParams,\n      clientId,\n      redirectUri,\n    },\n    discovery,\n    GoogleAuthRequest\n  );\n\n  const [result, promptAsync] = useAuthRequestResult(request, discovery, {\n    windowFeatures: settings.windowFeatures,\n  });\n\n  const [fullResult, setFullResult] = useState<AuthSessionResult | null>(null);\n\n  const shouldAutoExchangeCode = useMemo(() => {\n    // allow overrides\n    if (typeof config.shouldAutoExchangeCode !== 'undefined') {\n      return config.shouldAutoExchangeCode;\n    }\n\n    // has a code to exchange and doesn't have an authentication yet.\n    return result?.type === 'success' && result.params.code && !result.authentication;\n  }, [config.shouldAutoExchangeCode, result?.type]);\n\n  useEffect(() => {\n    let isMounted = true;\n    if (shouldAutoExchangeCode && result?.type === 'success') {\n      const exchangeRequest = new AccessTokenRequest({\n        clientId,\n        clientSecret: config.clientSecret,\n        redirectUri,\n        scopes: config.scopes,\n        code: result.params.code,\n        extraParams: {\n          code_verifier: request?.codeVerifier || '',\n        },\n      });\n      exchangeRequest.performAsync(discovery).then((authentication) => {\n        if (isMounted) {\n          setFullResult({\n            ...result,\n            params: {\n              id_token: authentication?.idToken || '',\n              access_token: authentication.accessToken,\n              ...result.params,\n            },\n            authentication,\n          });\n        }\n      });\n    } else {\n      setFullResult(result);\n    }\n    return () => {\n      isMounted = false;\n    };\n  }, [\n    clientId,\n    redirectUri,\n    shouldAutoExchangeCode,\n    config.clientSecret,\n    config.scopes?.join(','),\n    request?.codeVerifier,\n    result,\n  ]);\n\n  return [request, fullResult, promptAsync];\n}\n"]}