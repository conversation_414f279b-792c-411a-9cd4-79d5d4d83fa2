{"version": 3, "file": "Fetch.js", "sourceRoot": "", "sources": ["../src/Fetch.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,MAAM,IAAI,CAAC;AAepB,iEAAiE;AACjE,MAAM,cAAc,GAClB,QAAQ,CAAC,EAAE,KAAK,KAAK;IACrB,OAAO,MAAM,KAAK,WAAW;IAC7B,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,aAAa;IAChC,OAAO,GAAG,KAAK,WAAW,CAAC;AAE7B,MAAM,CAAC,KAAK,UAAU,YAAY,CAAI,UAAkB,EAAE,YAA0B;IAClF,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,cAAc,EAAE;QAC5C,aAAa;QACb,OAAO;KACR;IACD,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;IAEhC,MAAM,OAAO,GAA4D;QACvE,MAAM,EAAE,YAAY,CAAC,MAAM;QAC3B,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,EAAE;KACZ,CAAC;IAEF,MAAM,cAAc,GAAG,YAAY,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC;IAEvE,IAAI,YAAY,CAAC,OAAO,EAAE;QACxB,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE;YACpC,IAAI,CAAC,IAAI,YAAY,CAAC,OAAO,EAAE;gBAC7B,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC,CAAW,CAAC;aACxD;SACF;KACF;IAED,IAAI,YAAY,CAAC,IAAI,EAAE;QACrB,IAAI,YAAY,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,MAAM,EAAE;YACjD,OAAO,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;SAChD;aAAM;YACL,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;gBAChD,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aACtD;SACF;KACF;IAED,IAAI,cAAc,IAAI,CAAC,CAAC,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QACpD,kFAAkF;QAClF,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,2CAA2C,CAAC;KACzE;IAED,8EAA8E;IAC9E,MAAM,YAAY,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAEvD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAEpD,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACzD,IAAI,cAAc,IAAI,WAAW,EAAE,QAAQ,CAAC,kBAAkB,CAAC,EAAE;QAC/D,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;KACxB;IACD,2DAA2D;IAC3D,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport qs from 'qs';\n\nexport type Headers = Record<string, string> & {\n  'Content-Type': string;\n  Authorization?: string;\n  Accept?: string;\n};\n\nexport type FetchRequest = {\n  headers?: Headers;\n  body?: Record<string, string>;\n  dataType?: string;\n  method?: string;\n};\n\n// TODO(Bacon): pending react-native-adapter publish after sdk 38\nconst isDOMAvailable =\n  Platform.OS === 'web' &&\n  typeof window !== 'undefined' &&\n  !!window.document?.createElement &&\n  typeof URL !== 'undefined';\n\nexport async function requestAsync<T>(requestUrl: string, fetchRequest: FetchRequest): Promise<T> {\n  if (Platform.OS === 'web' && !isDOMAvailable) {\n    // @ts-ignore\n    return;\n  }\n  const url = new URL(requestUrl);\n\n  const request: Omit<RequestInit, 'headers'> & { headers: HeadersInit } = {\n    method: fetchRequest.method,\n    mode: 'cors',\n    headers: {},\n  };\n\n  const isJsonDataType = fetchRequest.dataType?.toLowerCase() === 'json';\n\n  if (fetchRequest.headers) {\n    for (const i in fetchRequest.headers) {\n      if (i in fetchRequest.headers) {\n        request.headers[i] = fetchRequest.headers[i] as string;\n      }\n    }\n  }\n\n  if (fetchRequest.body) {\n    if (fetchRequest.method?.toUpperCase() === 'POST') {\n      request.body = qs.stringify(fetchRequest.body);\n    } else {\n      for (const key of Object.keys(fetchRequest.body)) {\n        url.searchParams.append(key, fetchRequest.body[key]);\n      }\n    }\n  }\n\n  if (isJsonDataType && !('Accept' in request.headers)) {\n    // NOTE: Github authentication will return XML if this includes the standard `*/*`\n    request.headers['Accept'] = 'application/json, text/javascript; q=0.01';\n  }\n\n  // Fix a problem with React Native `URL` causing a trailing slash to be added.\n  const correctedUrl = url.toString().replace(/\\/$/, '');\n\n  const response = await fetch(correctedUrl, request);\n\n  const contentType = response.headers.get('content-type');\n  if (isJsonDataType || contentType?.includes('application/json')) {\n    return response.json();\n  }\n  // @ts-ignore: Type 'string' is not assignable to type 'T'.\n  return response.text();\n}\n"]}