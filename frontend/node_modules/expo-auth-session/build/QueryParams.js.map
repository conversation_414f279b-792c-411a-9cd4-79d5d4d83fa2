{"version": 3, "file": "QueryParams.js", "sourceRoot": "", "sources": ["../src/QueryParams.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,MAAM,IAAI,CAAC;AAEpB,MAAM,UAAU,gBAAgB,CAAC,KAA6B;IAC5D,OAAO,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,GAAW;IAIxC,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IACtB,MAAM,gBAAgB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,WAAW,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAElE,kCAAkC;IAClC,MAAM,YAAY,GAAG,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC;IAEnE,+BAA+B;IAC/B,MAAM,SAAS,GAAG,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI,CAAkB,CAAC;IACpE,SAAS,CACP,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,EACnD,yDAAyD,CAC1D,CAAC;IACF,OAAO,YAAY,CAAC,SAAS,CAAC;IAE9B,0BAA0B;IAC1B,IAAI,UAAU,GAAG,EAAE,CAAC;IACpB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE;QACZ,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC7B;IAED,wBAAwB;IACxB,MAAM,MAAM,GAAG;QACb,GAAG,YAAY;QACf,GAAG,UAAU;KACd,CAAC;IAEF,OAAO;QACL,SAAS;QACT,MAAM;KACP,CAAC;AACJ,CAAC", "sourcesContent": ["import invariant from 'invariant';\nimport qs from 'qs';\n\nexport function buildQueryString(input: Record<string, string>): string {\n  return qs.stringify(input);\n}\n\nexport function getQueryParams(url: string): {\n  errorCode: string | null;\n  params: { [key: string]: string };\n} {\n  const parts = url.split('#');\n  const hash = parts[1];\n  const partsWithoutHash = parts[0].split('?');\n  const queryString = partsWithoutHash[partsWithoutHash.length - 1];\n\n  // Get query string (?hello=world)\n  const parsedSearch = qs.parse(queryString, { parseArrays: false });\n\n  // Pull errorCode off of params\n  const errorCode = (parsedSearch.errorCode ?? null) as string | null;\n  invariant(\n    typeof errorCode === 'string' || errorCode === null,\n    `The \"errorCode\" parameter must be a string if specified`\n  );\n  delete parsedSearch.errorCode;\n\n  // Get hash (#abc=example)\n  let parsedHash = {};\n  if (parts[1]) {\n    parsedHash = qs.parse(hash);\n  }\n\n  // Merge search and hash\n  const params = {\n    ...parsedSearch,\n    ...parsedHash,\n  };\n\n  return {\n    errorCode,\n    params,\n  };\n}\n"]}