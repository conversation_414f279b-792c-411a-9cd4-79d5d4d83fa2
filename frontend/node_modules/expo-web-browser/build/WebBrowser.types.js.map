{"version": 3, "file": "WebBrowser.types.js", "sourceRoot": "", "sources": ["../src/WebBrowser.types.ts"], "names": [], "mappings": "AAoIA,2BAA2B;AAC3B,MAAM,CAAN,IAAY,oBAcX;AAdD,WAAY,oBAAoB;IAC9B;;OAEG;IACH,yCAAiB,CAAA;IACjB;;OAEG;IACH,2CAAmB,CAAA;IACnB;;OAEG;IACH,yCAAiB,CAAA;IACjB,yCAAiB,CAAA;AACnB,CAAC,EAdW,oBAAoB,KAApB,oBAAoB,QAc/B;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAN,IAAY,2BAoCX;AApCD,WAAY,2BAA2B;IACrC;;OAEG;IACH,yDAA0B,CAAA;IAC1B;;OAEG;IACH,uDAAwB,CAAA;IACxB;;OAEG;IACH,uDAAwB,CAAA;IACxB;;OAEG;IACH,iEAAkC,CAAA;IAClC;;OAEG;IACH,kEAAmC,CAAA;IACnC;;OAEG;IACH,0EAA2C,CAAA;IAC3C;;OAEG;IACH,kDAAmB,CAAA;IACnB;;;;;OAKG;IACH,sDAAuB,CAAA;AACzB,CAAC,EApCW,2BAA2B,KAA3B,2BAA2B,QAoCtC", "sourcesContent": ["export type RedirectEvent = {\n  url: string;\n};\n\n// @needsAudit @docsMissing\nexport type WebBrowserWindowFeatures = Record<string, number | boolean | string>;\n\n// @needsAudit\nexport type WebBrowserOpenOptions = {\n  /**\n   * Color of the toolbar. Supports React Native [color formats](https://reactnative.dev/docs/colors).\n   */\n  toolbarColor?: string;\n  /**\n   * Package name of a browser to be used to handle Custom Tabs. List of\n   * available packages is to be queried by [`getCustomTabsSupportingBrowsers`](#webbrowsergetcustomtabssupportingbrowsersasync) method.\n   * @platform android\n   */\n  browserPackage?: string;\n  /**\n   * A boolean determining whether the toolbar should be hiding when a user scrolls the website.\n   */\n  enableBarCollapsing?: boolean;\n  /**\n   * Color of the secondary toolbar. Supports React Native [color formats](https://reactnative.dev/docs/colors).\n   * @platform android\n   */\n  secondaryToolbarColor?: string;\n  /**\n   * A boolean determining whether the browser should show the title of website on the toolbar.\n   * @platform android\n   */\n  showTitle?: boolean;\n  /**\n   * A boolean determining whether a default share item should be added to the menu.\n   * @platform android\n   */\n  enableDefaultShareMenuItem?: boolean;\n  /**\n   * A boolean determining whether browsed website should be shown as separate\n   * entry in Android recents/multitasking view. Requires `createTask` to be `true` (default).\n   * @default false\n   * @platform android\n   */\n  showInRecents?: boolean;\n  /**\n   * A boolean determining whether the browser should open in a new task or in\n   * the same task as your app.\n   * @default true\n   * @platform android\n   */\n  createTask?: boolean;\n  /**\n   * Tint color for controls in SKSafariViewController. Supports React Native [color formats](https://reactnative.dev/docs/colors).\n   * @platform ios\n   */\n  controlsColor?: string;\n  /**\n   * The style of the dismiss button. Should be one of: `done`, `close`, or `cancel`.\n   * @platform ios\n   */\n  dismissButtonStyle?: 'done' | 'close' | 'cancel';\n  /**\n   * A boolean determining whether Safari should enter Reader mode, if it is available.\n   * @platform ios\n   */\n  readerMode?: boolean;\n  /**\n   * The [presentation style](https://developer.apple.com/documentation/uikit/uiviewcontroller/1621355-modalpresentationstyle)\n   * of the browser window.\n   * @default WebBrowser.WebBrowserPresentationStyle.OverFullScreen\n   * @platform ios\n   */\n  presentationStyle?: WebBrowserPresentationStyle;\n  /**\n   * Name to assign to the popup window.\n   * @platform web\n   */\n  windowName?: string;\n  /**\n   * Features to use with `window.open()`.\n   * @platform web\n   */\n  windowFeatures?: string | WebBrowserWindowFeatures;\n};\n\n/**\n * If there is no native AuthSession implementation available (which is the case on Android) the params inherited from\n * [`WebBrowserOpenOptions`](#webbrowseropenoptions) will be used in the browser polyfill. Otherwise, the browser parameters will be ignored.\n */\nexport type AuthSessionOpenOptions = WebBrowserOpenOptions & {\n  /**\n   * Determines whether the session should ask the browser for a private authentication session.\n   * Set this to `true` to request that the browser doesn’t share cookies or other browsing data between the authentication session and the user’s normal browser session.\n   * Whether the request is honored depends on the user’s default web browser.\n   *\n   * @default false\n   * @platform ios 13+\n   */\n  preferEphemeralSession?: boolean;\n};\n\nexport type WebBrowserAuthSessionResult = WebBrowserRedirectResult | WebBrowserResult;\n\n// @needsAudit\nexport type WebBrowserCustomTabsResults = {\n  /**\n   * Default package chosen by user, `null` if there is no such packages. Also `null` usually means,\n   * that user will be prompted to choose from available packages.\n   */\n  defaultBrowserPackage?: string;\n  /**\n   * Package preferred by `CustomTabsClient` to be used to handle Custom Tabs. It favors browser\n   * chosen by user as default, as long as it is present on both `browserPackages` and\n   * `servicePackages` lists. Only such browsers are considered as fully supporting Custom Tabs.\n   * It might be `null` when there is no such browser installed or when default browser is not in\n   * `servicePackages` list.\n   */\n  preferredBrowserPackage?: string;\n  /**\n   * All packages recognized by `PackageManager` as capable of handling Custom Tabs. Empty array\n   * means there is no supporting browsers on device.\n   */\n  browserPackages: string[];\n  /**\n   * All packages recognized by `PackageManager` as capable of handling Custom Tabs Service.\n   * This service is used by [`warmUpAsync`](#webbrowserwarmupasyncbrowserpackage), [`mayInitWithUrlAsync`](#webbrowsermayinitwithurlasyncurl-browserpackage)\n   * and [`coolDownAsync`](#webbrowsercooldownasyncbrowserpackage).\n   */\n  servicePackages: string[];\n};\n\n// @needsAudit @docsMissing\nexport enum WebBrowserResultType {\n  /**\n   * @platform ios\n   */\n  CANCEL = 'cancel',\n  /**\n   * @platform ios\n   */\n  DISMISS = 'dismiss',\n  /**\n   * @platform android\n   */\n  OPENED = 'opened',\n  LOCKED = 'locked',\n}\n\n// @needsAudit\n/**\n * A browser presentation style. Its values are directly mapped to the [`UIModalPresentationStyle`](https://developer.apple.com/documentation/uikit/uiviewcontroller/1621355-modalpresentationstyle).\n *\n * @platform ios\n */\nexport enum WebBrowserPresentationStyle {\n  /**\n   * A presentation style in which the presented browser covers the screen.\n   */\n  FULL_SCREEN = 'fullScreen',\n  /**\n   * A presentation style that partially covers the underlying content.\n   */\n  PAGE_SHEET = 'pageSheet',\n  /**\n   * A presentation style that displays the browser centered in the screen.\n   */\n  FORM_SHEET = 'formSheet',\n  /**\n   * A presentation style where the browser is displayed over the app's content.\n   */\n  CURRENT_CONTEXT = 'currentContext',\n  /**\n   * A presentation style in which the browser view covers the screen.\n   */\n  OVER_FULL_SCREEN = 'overFullScreen',\n  /**\n   * A presentation style where the browser is displayed over the app's content.\n   */\n  OVER_CURRENT_CONTEXT = 'overCurrentContext',\n  /**\n   * A presentation style where the browser is displayed in a popover view.\n   */\n  POPOVER = 'popover',\n  /**\n   * The default presentation style chosen by the system.\n   * On older iOS versions, falls back to `WebBrowserPresentationStyle.FullScreen`.\n   *\n   * @platform ios 13+\n   */\n  AUTOMATIC = 'automatic',\n}\n\n// @needsAudit\nexport type WebBrowserResult = {\n  /**\n   * Type of the result.\n   */\n  type: WebBrowserResultType;\n};\n\n// @needsAudit @docsMissing\nexport type WebBrowserRedirectResult = {\n  /**\n   * Type of the result.\n   */\n  type: 'success';\n  url: string;\n};\n\nexport type ServiceActionResult = {\n  servicePackage?: string;\n};\n\nexport type WebBrowserMayInitWithUrlResult = ServiceActionResult;\nexport type WebBrowserWarmUpResult = ServiceActionResult;\nexport type WebBrowserCoolDownResult = ServiceActionResult;\n\n// @needsAudit\nexport type WebBrowserCompleteAuthSessionOptions = {\n  /**\n   * Attempt to close the window without checking to see if the auth redirect matches the cached redirect URL.\n   */\n  skipRedirectCheck?: boolean;\n};\n\n// @needsAudit\nexport type WebBrowserCompleteAuthSessionResult = {\n  /**\n   * Type of the result.\n   */\n  type: 'success' | 'failed';\n  /**\n   * Additional description or reasoning of the result.\n   */\n  message: string;\n};\n"]}