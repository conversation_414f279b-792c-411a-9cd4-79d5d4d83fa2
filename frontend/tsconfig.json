{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "target": "es2020", "lib": ["es2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/screens/*": ["./screens/*"], "@/navigation/*": ["./navigation/*"], "@/services/*": ["./services/*"], "@/store/*": ["./store/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"], "@/constants/*": ["./constants/*"], "@/hooks/*": ["./hooks/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules"]}