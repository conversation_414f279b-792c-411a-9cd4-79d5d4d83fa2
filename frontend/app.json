{"expo": {"name": "Tourna", "slug": "tourna", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.tourna.app", "infoPlist": {"NSCameraUsageDescription": "This app uses camera to take photos for profile and service listings.", "NSLocationWhenInUseUsageDescription": "This app uses location to show nearby guides and services.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses location to show nearby guides and services."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.tourna.app", "permissions": ["CAMERA", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "WRITE_EXTERNAL_STORAGE", "READ_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-camera", "expo-location", "expo-notifications"], "extra": {"eas": {"projectId": "your-project-id"}}}}