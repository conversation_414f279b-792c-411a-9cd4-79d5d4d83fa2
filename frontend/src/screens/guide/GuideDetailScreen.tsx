import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Avatar,
  Surface,
  IconButton,
  Chip,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

import { RootStackParamList } from '@/navigation/AppNavigator';
import { theme, spacing } from '@/constants/theme';
import { guideDiscoveryService, GuideDiscoveryResponse } from '@/services/guideDiscoveryService';

type GuideDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'GuideDetail'>;
type GuideDetailScreenRouteProp = RouteProp<RootStackParamList, 'GuideDetail'>;

interface Props {
  navigation: GuideDetailScreenNavigationProp;
  route: GuideDetailScreenRouteProp;
}

export function GuideDetailScreen({ navigation, route }: Props) {
  const { t } = useTranslation();
  const { guideId } = route.params;
  
  const [guide, setGuide] = useState<GuideDiscoveryResponse | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadGuideDetails();
  }, [guideId]);

  const loadGuideDetails = async () => {
    try {
      setLoading(true);
      // 模拟导游详情数据
      const mockGuide: GuideDiscoveryResponse = {
        id: guideId,
        firstName: 'Maria',
        lastName: 'Rodriguez',
        fullName: 'Maria Rodriguez',
        username: 'maria_guide',
        email: '<EMAIL>',
        phone: '+34 123 456 789',
        avatarUrl: 'https://via.placeholder.com/150',
        bio: 'Passionate local guide with 8 years of experience showing visitors the hidden gems of Barcelona. I love sharing stories about our rich history, culture, and the best local food spots!',
        languagePreference: 'en',
        languages: ['English', 'Spanish', 'Catalan'],
        location: 'Barcelona, Spain',
        latitude: 41.3851,
        longitude: 2.1734,
        distance: 0.5,
        rating: 4.8,
        reviewCount: 127,
        responseTime: 'Within 1 hour',
        responseRate: 98,
        isOnline: true,
        isVerified: true,
        specialties: ['CULTURAL', 'FOOD_WINE', 'HISTORICAL'],
        services: [
          {
            id: 1,
            title: 'Historic Barcelona Walking Tour',
            description: 'Explore the Gothic Quarter and learn about Barcelona\'s fascinating history',
            price: 45.00,
            currency: 'EUR',
            duration: '3 hours',
            category: 'Cultural Tours',
            rating: 4.9,
            bookingCount: 89
          },
          {
            id: 2,
            title: 'Tapas & Wine Experience',
            description: 'Discover authentic tapas bars and local wines with a true Barcelonian',
            price: 65.00,
            currency: 'EUR',
            duration: '4 hours',
            category: 'Food & Wine',
            rating: 4.7,
            bookingCount: 56
          }
        ],
        joinedDate: '2019-03-15',
        totalBookings: 234,
        isActive: true
      };
      
      setGuide(mockGuide);
    } catch (error) {
      Alert.alert('Error', 'Failed to load guide details');
    } finally {
      setLoading(false);
    }
  };

  const handleBookService = (serviceId: number) => {
    navigation.navigate('Booking', { productId: serviceId });
  };

  const handleSendMessage = () => {
    if (guide) {
      navigation.navigate('Chat', { 
        sessionId: guide.id, 
        recipientName: guide.fullName 
      });
    }
  };

  if (loading || !guide) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="bodyLarge">Loading guide details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 导游头部信息 */}
        <Surface style={styles.header} elevation={2}>
          <View style={styles.guideInfo}>
            <Avatar.Image
              size={80}
              source={{ uri: guide.avatarUrl }}
              style={styles.avatar}
            />
            <View style={styles.guideDetails}>
              <View style={styles.nameContainer}>
                <Text variant="headlineSmall" style={styles.guideName}>
                  {guide.fullName}
                </Text>
                {guide.isVerified && (
                  <IconButton 
                    icon="check-decagram" 
                    size={20} 
                    iconColor={theme.colors.primary}
                    style={styles.verifiedIcon}
                  />
                )}
              </View>
              <Text variant="bodyMedium" style={styles.location}>
                📍 {guide.location}
              </Text>
              <View style={styles.statsContainer}>
                <View style={styles.stat}>
                  <Text variant="titleMedium" style={styles.statValue}>
                    {guide.rating}
                  </Text>
                  <Text variant="bodySmall" style={styles.statLabel}>
                    ⭐ ({guide.reviewCount} reviews)
                  </Text>
                </View>
                <View style={styles.stat}>
                  <Text variant="titleMedium" style={styles.statValue}>
                    {guide.responseRate}%
                  </Text>
                  <Text variant="bodySmall" style={styles.statLabel}>
                    Response rate
                  </Text>
                </View>
              </View>
            </View>
          </View>
          
          <View style={styles.actionButtons}>
            <Button 
              mode="contained" 
              onPress={handleSendMessage}
              style={styles.messageButton}
              icon="message"
            >
              Message
            </Button>
          </View>
        </Surface>

        {/* 关于导游 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            About {guide.firstName}
          </Text>
          <Text variant="bodyMedium" style={styles.bio}>
            {guide.bio}
          </Text>
          
          <View style={styles.languagesContainer}>
            <Text variant="titleMedium" style={styles.subsectionTitle}>
              Languages
            </Text>
            <View style={styles.chipContainer}>
              {guide.languages.map((language, index) => (
                <Chip key={index} style={styles.languageChip}>
                  {language}
                </Chip>
              ))}
            </View>
          </View>
        </Surface>

        {/* 服务列表 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Services Offered
          </Text>
          {guide.services.map((service) => (
            <Card key={service.id} style={styles.serviceCard} mode="outlined">
              <Card.Content>
                <Text variant="titleMedium" style={styles.serviceTitle}>
                  {service.title}
                </Text>
                <Text variant="bodyMedium" style={styles.serviceDescription}>
                  {service.description}
                </Text>
                <View style={styles.serviceDetails}>
                  <Text variant="bodySmall" style={styles.serviceDuration}>
                    ⏱️ {service.duration}
                  </Text>
                  <Text variant="bodySmall" style={styles.serviceCategory}>
                    📂 {service.category}
                  </Text>
                </View>
                <View style={styles.serviceFooter}>
                  <View style={styles.priceContainer}>
                    <Text variant="titleLarge" style={styles.price}>
                      €{service.price}
                    </Text>
                    <Text variant="bodySmall" style={styles.priceLabel}>
                      per person
                    </Text>
                  </View>
                  <Button 
                    mode="contained" 
                    onPress={() => handleBookService(service.id)}
                    style={styles.bookButton}
                  >
                    Book Now
                  </Button>
                </View>
              </Card.Content>
            </Card>
          ))}
        </Surface>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: spacing.lg,
    margin: spacing.md,
    borderRadius: 12,
  },
  guideInfo: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  avatar: {
    marginRight: spacing.md,
  },
  guideDetails: {
    flex: 1,
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  guideName: {
    fontWeight: 'bold',
  },
  verifiedIcon: {
    margin: 0,
    marginLeft: spacing.xs,
  },
  location: {
    color: theme.colors.onSurfaceVariant,
    marginBottom: spacing.sm,
  },
  statsContainer: {
    flexDirection: 'row',
    gap: spacing.lg,
  },
  stat: {
    alignItems: 'center',
  },
  statValue: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    color: theme.colors.onSurfaceVariant,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  messageButton: {
    flex: 1,
  },
  section: {
    margin: spacing.md,
    padding: spacing.lg,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  bio: {
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  languagesContainer: {
    marginTop: spacing.md,
  },
  subsectionTitle: {
    fontWeight: '600',
    marginBottom: spacing.sm,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  languageChip: {
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  serviceCard: {
    marginBottom: spacing.md,
  },
  serviceTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  serviceDescription: {
    marginBottom: spacing.sm,
    lineHeight: 20,
  },
  serviceDetails: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  serviceDuration: {
    color: theme.colors.onSurfaceVariant,
  },
  serviceCategory: {
    color: theme.colors.onSurfaceVariant,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    alignItems: 'flex-start',
  },
  price: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  priceLabel: {
    color: theme.colors.onSurfaceVariant,
  },
  bookButton: {
    minWidth: 100,
  },
});
