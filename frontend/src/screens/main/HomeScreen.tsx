import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, FlatList, RefreshControl } from 'react-native';
import { 
  Text, 
  Searchbar, 
  Card, 
  Button, 
  Chip, 
  Avatar,
  Surface,
  IconButton,
  Badge
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';
import { guideDiscoveryService, GuideDiscoveryResponse, PopularService } from '@/services/guideDiscoveryService';

interface TourService {
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  rating: number;
  reviewCount: number;
  city: string;
  country: string;
  guide: {
    id: number;
    name: string;
    avatar?: string;
    isVerified: boolean;
  };
  images: string[];
  category: string;
  duration: string;
  isPopular: boolean;
  isRecommended: boolean;
}

const MOCK_SERVICES: TourService[] = [
  {
    id: 1,
    title: 'Historic City Walking Tour',
    description: 'Explore the ancient streets and hidden gems of the old city with a local expert.',
    price: 45,
    currency: 'USD',
    rating: 4.8,
    reviewCount: 127,
    city: 'Prague',
    country: 'Czech Republic',
    guide: {
      id: 1,
      name: 'Anna Novak',
      avatar: 'https://via.placeholder.com/50',
      isVerified: true,
    },
    images: ['https://via.placeholder.com/300x200'],
    category: 'Cultural',
    duration: '3 hours',
    isPopular: true,
    isRecommended: true,
  },
  {
    id: 2,
    title: 'Food & Wine Experience',
    description: 'Taste authentic local cuisine and wines in traditional restaurants.',
    price: 75,
    currency: 'USD',
    rating: 4.9,
    reviewCount: 89,
    city: 'Barcelona',
    country: 'Spain',
    guide: {
      id: 2,
      name: 'Carlos Rodriguez',
      avatar: 'https://via.placeholder.com/50',
      isVerified: true,
    },
    images: ['https://via.placeholder.com/300x200'],
    category: 'Food',
    duration: '4 hours',
    isPopular: true,
    isRecommended: false,
  },
  {
    id: 3,
    title: 'Mountain Hiking Adventure',
    description: 'Discover breathtaking mountain trails and scenic viewpoints.',
    price: 60,
    currency: 'USD',
    rating: 4.7,
    reviewCount: 156,
    city: 'Interlaken',
    country: 'Switzerland',
    guide: {
      id: 3,
      name: 'Hans Mueller',
      avatar: 'https://via.placeholder.com/50',
      isVerified: true,
    },
    images: ['https://via.placeholder.com/300x200'],
    category: 'Adventure',
    duration: '6 hours',
    isPopular: false,
    isRecommended: true,
  },
];

const CITIES = ['All Cities', 'Prague', 'Barcelona', 'Interlaken', 'Paris', 'Tokyo', 'New York'];

export function HomeScreen() {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCity, setSelectedCity] = useState('All Cities');
  const [services, setServices] = useState<TourService[]>(MOCK_SERVICES);
  const [recommendedGuides, setRecommendedGuides] = useState<GuideDiscoveryResponse[]>([]);
  const [popularServices, setPopularServices] = useState<PopularService[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  // 初始化数据
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [guides, services] = await Promise.all([
        guideDiscoveryService.getRecommendedGuides(5),
        guideDiscoveryService.getPopularServices(4)
      ]);
      setRecommendedGuides(guides);
      setPopularServices(services);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = service.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCity = selectedCity === 'All Cities' || service.city === selectedCity;
    return matchesSearch && matchesCity;
  });

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await loadInitialData();
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleSearch = async () => {
    if (searchQuery.trim()) {
      try {
        setLoading(true);
        const results = await guideDiscoveryService.searchServices(
          searchQuery,
          selectedCity === 'All Cities' ? undefined : selectedCity
        );
        setRecommendedGuides(results);
      } catch (error) {
        console.error('Search failed:', error);
      } finally {
        setLoading(false);
      }
    } else {
      loadInitialData();
    }
  };

  const renderServiceCard = ({ item }: { item: TourService }) => (
    <Card style={styles.serviceCard} mode="elevated">
      <Card.Cover source={{ uri: item.images[0] }} style={styles.cardImage} />
      <Card.Content style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <Text variant="titleMedium" style={styles.serviceTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <View style={styles.priceContainer}>
            <Text variant="titleSmall" style={styles.price}>
              ${item.price}
            </Text>
            <Text variant="bodySmall" style={styles.currency}>
              /{item.duration}
            </Text>
          </View>
        </View>

        <Text variant="bodySmall" style={styles.description} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.locationContainer}>
          <IconButton icon="map-marker" size={16} style={styles.locationIcon} />
          <Text variant="bodySmall" style={styles.location}>
            {item.city}, {item.country}
          </Text>
        </View>

        <View style={styles.cardFooter}>
          <View style={styles.guideInfo}>
            <Avatar.Image
              size={24}
              source={{ uri: item.guide.avatar }}
              style={styles.guideAvatar}
            />
            <Text variant="bodySmall" style={styles.guideName}>
              {item.guide.name}
            </Text>
            {item.guide.isVerified && (
              <IconButton icon="check-decagram" size={16} iconColor={theme.colors.primary} />
            )}
          </View>

          <View style={styles.ratingContainer}>
            <IconButton icon="star" size={16} iconColor="#FFD700" />
            <Text variant="bodySmall" style={styles.rating}>
              {item.rating} ({item.reviewCount})
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderGuideCard = ({ item }: { item: GuideDiscoveryResponse }) => (
    <Card style={styles.guideCard} mode="elevated">
      <Card.Content style={styles.cardContent}>
        <View style={styles.guideHeader}>
          <Avatar.Image
            size={50}
            source={{ uri: item.avatarUrl }}
            style={styles.guideAvatar}
          />
          <View style={styles.guideInfo}>
            <View style={styles.guideNameContainer}>
              <Text variant="titleMedium" style={styles.guideName}>
                {item.name}
              </Text>
              {item.isVerified && (
                <IconButton icon="check-decagram" size={16} iconColor={theme.colors.primary} />
              )}
            </View>
            <View style={styles.ratingContainer}>
              <IconButton icon="star" size={16} iconColor="#FFD700" />
              <Text variant="bodySmall" style={styles.rating}>
                {item.rating} ({item.reviewCount} reviews)
              </Text>
            </View>
            <Text variant="bodySmall" style={styles.distance}>
              {item.distance}km away • ${item.hourlyRate}/hour
            </Text>
          </View>
        </View>

        <Text variant="bodySmall" style={styles.bio} numberOfLines={3}>
          {item.bio}
        </Text>

        <View style={styles.specialtiesContainer}>
          {item.specialties.slice(0, 3).map((specialty, index) => (
            <Chip key={index} style={styles.specialtyChip} textStyle={styles.specialtyText}>
              {specialty}
            </Chip>
          ))}
        </View>

        <View style={styles.locationContainer}>
          <IconButton icon="map-marker" size={16} style={styles.locationIcon} />
          <Text variant="bodySmall" style={styles.location}>
            {item.location.city}, {item.location.country}
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  const renderPopularServiceCard = ({ item }: { item: PopularService }) => (
    <Card style={styles.popularServiceCard} mode="elevated">
      <Card.Content style={styles.cardContent}>
        <Text variant="titleMedium" style={styles.serviceTitle} numberOfLines={2}>
          {item.title}
        </Text>
        <View style={styles.serviceDetails}>
          <Text variant="bodySmall" style={styles.category}>
            {item.category}
          </Text>
          <Text variant="bodySmall" style={styles.duration}>
            {item.duration}
          </Text>
        </View>
        <View style={styles.serviceFooter}>
          <View style={styles.ratingContainer}>
            <IconButton icon="star" size={16} iconColor="#FFD700" />
            <Text variant="bodySmall" style={styles.rating}>
              {item.rating}
            </Text>
          </View>
          <Text variant="titleSmall" style={styles.price}>
            ${item.price}
          </Text>
        </View>
        <Text variant="bodySmall" style={styles.bookingCount}>
          {item.bookingCount} bookings
        </Text>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* 头部问候 */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={styles.greeting}>
            {t('home.greeting', 'Hello')}, {user?.firstName || 'Traveler'}! 👋
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {t('home.subtitle', 'Discover amazing experiences with local guides')}
          </Text>
        </View>

        {/* 搜索框 */}
        <Surface style={styles.searchContainer} elevation={1}>
          <Searchbar
            placeholder={t('home.searchPlaceholder', 'Search tours, activities, guides...')}
            onChangeText={setSearchQuery}
            value={searchQuery}
            onSubmitEditing={handleSearch}
            style={styles.searchBar}
            inputStyle={styles.searchInput}
            icon="magnify"
            clearIcon="close"
            loading={loading}
          />
        </Surface>

        {/* 城市筛选 */}
        <View style={styles.filterSection}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('home.filterByCity', 'Filter by City')}
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.cityFilter}>
            {CITIES.map((city) => (
              <Chip
                key={city}
                selected={selectedCity === city}
                onPress={() => setSelectedCity(city)}
                style={[
                  styles.cityChip,
                  selectedCity === city && styles.selectedCityChip
                ]}
                textStyle={[
                  styles.cityChipText,
                  selectedCity === city && styles.selectedCityChipText
                ]}
              >
                {city}
              </Chip>
            ))}
          </ScrollView>
        </View>

        {/* 推荐导游 */}
        {recommendedGuides.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                {t('home.recommended', 'Recommended Guides')}
              </Text>
              <Button mode="text" onPress={() => {}}>
                {t('common.seeAll', 'See All')}
              </Button>
            </View>
            <FlatList
              data={recommendedGuides}
              renderItem={renderGuideCard}
              keyExtractor={(item) => `guide-${item.id}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.horizontalList}
            />
          </View>
        )}

        {/* 热门服务 */}
        {popularServices.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text variant="titleLarge" style={styles.sectionTitle}>
                {t('home.popular', 'Popular Services')}
              </Text>
              <Button mode="text" onPress={() => {}}>
                {t('common.seeAll', 'See All')}
              </Button>
            </View>
            <FlatList
              data={popularServices}
              renderItem={renderPopularServiceCard}
              keyExtractor={(item) => `service-${item.id}`}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.horizontalList}
            />
          </View>
        )}

        {/* 所有服务 */}
        <View style={styles.section}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            {t('home.allServices', 'All Services')}
          </Text>
          {filteredServices.map((service) => (
            <View key={`all-${service.id}`} style={styles.verticalCard}>
              {renderServiceCard({ item: service })}
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  greeting: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: spacing.xs,
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
  },
  searchContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  searchBar: {
    backgroundColor: 'transparent',
    elevation: 0,
  },
  searchInput: {
    fontSize: 16,
  },
  filterSection: {
    marginBottom: spacing.lg,
  },
  cityFilter: {
    paddingHorizontal: spacing.lg,
  },
  cityChip: {
    marginRight: spacing.sm,
    backgroundColor: theme.colors.surface,
  },
  selectedCityChip: {
    backgroundColor: theme.colors.primary,
  },
  cityChipText: {
    color: theme.colors.onSurface,
  },
  selectedCityChipText: {
    color: theme.colors.onPrimary,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
  },
  horizontalList: {
    paddingHorizontal: spacing.lg,
  },
  verticalCard: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  serviceCard: {
    width: 280,
    marginRight: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  cardImage: {
    height: 160,
  },
  cardContent: {
    padding: spacing.md,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  serviceTitle: {
    flex: 1,
    fontWeight: 'bold',
    marginRight: spacing.sm,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  currency: {
    color: theme.custom.colors.textSecondary,
  },
  description: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.sm,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  locationIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  location: {
    color: theme.custom.colors.textSecondary,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  guideInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  guideAvatar: {
    marginRight: spacing.xs,
  },
  guideName: {
    color: theme.custom.colors.textSecondary,
    marginRight: spacing.xs,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    color: theme.custom.colors.textSecondary,
    marginLeft: -spacing.xs,
  },
  // 新增样式
  guideCard: {
    width: 300,
    marginRight: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  guideHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  },
  guideNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bio: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.sm,
    lineHeight: 20,
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  specialtyChip: {
    marginRight: spacing.xs,
    marginBottom: spacing.xs,
    backgroundColor: theme.colors.primaryContainer,
  },
  specialtyText: {
    fontSize: 12,
    color: theme.colors.onPrimaryContainer,
  },
  distance: {
    color: theme.custom.colors.textSecondary,
    fontSize: 12,
  },
  popularServiceCard: {
    width: 200,
    marginRight: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing.xs,
  },
  category: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  duration: {
    color: theme.custom.colors.textSecondary,
  },
  serviceFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  bookingCount: {
    color: theme.custom.colors.textSecondary,
    fontSize: 12,
  },
});
