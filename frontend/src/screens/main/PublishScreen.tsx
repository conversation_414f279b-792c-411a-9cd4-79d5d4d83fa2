import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  TextInput,
  Button, 
  Card,
  Chip,
  Surface,
  IconButton,
  Switch,
  SegmentedButtons,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';

interface ServiceForm {
  title: string;
  description: string;
  category: string;
  duration: string;
  price: number;
  currency: string;
  maxGroupSize: number;
  languages: string[];
  includesTransport: boolean;
  includesMeals: boolean;
  includesTickets: boolean;
  meetingPoint: string;
  cancellationPolicy: string;
  isActive: boolean;
}

const CATEGORIES = [
  'Cultural Tours',
  'Food & Wine',
  'Historical Sites',
  'Art & Museums',
  'Adventure',
  'Photography',
  'Shopping',
  'Nightlife',
  'Nature',
  'Sports'
];

const LANGUAGES = [
  'English',
  'Spanish',
  'French',
  'German',
  'Italian',
  'Portuguese',
  'Chinese',
  'Japanese',
  'Arabic',
  'Russian'
];

const DURATION_OPTIONS = [
  { value: '1h', label: '1 Hour' },
  { value: '2h', label: '2 Hours' },
  { value: '3h', label: '3 Hours' },
  { value: '4h', label: '4 Hours' },
  { value: '6h', label: '6 Hours' },
  { value: '8h', label: 'Full Day' },
  { value: 'multi', label: 'Multi-day' },
];

const CANCELLATION_POLICIES = [
  { value: 'flexible', label: 'Flexible - Free cancellation up to 24h before' },
  { value: 'moderate', label: 'Moderate - Free cancellation up to 48h before' },
  { value: 'strict', label: 'Strict - Free cancellation up to 7 days before' },
];

export function PublishScreen() {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);
  
  const [form, setForm] = useState<ServiceForm>({
    title: '',
    description: '',
    category: '',
    duration: '3h',
    price: 0,
    currency: 'USD',
    maxGroupSize: 6,
    languages: ['English'],
    includesTransport: false,
    includesMeals: false,
    includesTickets: false,
    meetingPoint: '',
    cancellationPolicy: 'flexible',
    isActive: true,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateForm = (field: keyof ServiceForm, value: any) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const toggleLanguage = (language: string) => {
    const currentLanguages = form.languages;
    if (currentLanguages.includes(language)) {
      updateForm('languages', currentLanguages.filter(l => l !== language));
    } else {
      updateForm('languages', [...currentLanguages, language]);
    }
  };

  const handleSubmit = async () => {
    // 验证表单
    if (!form.title.trim()) {
      Alert.alert('Error', 'Please enter a service title');
      return;
    }
    if (!form.description.trim()) {
      Alert.alert('Error', 'Please enter a description');
      return;
    }
    if (!form.category) {
      Alert.alert('Error', 'Please select a category');
      return;
    }
    if (form.price <= 0) {
      Alert.alert('Error', 'Please enter a valid price');
      return;
    }
    if (!form.meetingPoint.trim()) {
      Alert.alert('Error', 'Please enter a meeting point');
      return;
    }

    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        'Success!',
        'Your service has been published successfully.',
        [{ text: 'OK', onPress: () => {
          // 重置表单
          setForm({
            title: '',
            description: '',
            category: '',
            duration: '3h',
            price: 0,
            currency: 'USD',
            maxGroupSize: 6,
            languages: ['English'],
            includesTransport: false,
            includesMeals: false,
            includesTickets: false,
            meetingPoint: '',
            cancellationPolicy: 'flexible',
            isActive: true,
          });
        }}]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to publish service. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text variant="headlineSmall" style={styles.title}>
            {t('publish.title', 'Publish Your Service')}
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {t('publish.subtitle', 'Share your expertise with travelers')}
          </Text>
        </View>

        {/* 基本信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('publish.basicInfo', 'Basic Information')}
          </Text>
          
          <TextInput
            label={t('publish.serviceTitle', 'Service Title')}
            value={form.title}
            onChangeText={(text) => updateForm('title', text)}
            style={styles.input}
            placeholder="e.g., Historic City Walking Tour"
            maxLength={100}
          />

          <TextInput
            label={t('publish.description', 'Description')}
            value={form.description}
            onChangeText={(text) => updateForm('description', text)}
            style={styles.textArea}
            multiline
            numberOfLines={4}
            placeholder="Describe what makes your tour special..."
            maxLength={500}
          />

          <View style={styles.categorySection}>
            <Text variant="titleSmall" style={styles.fieldLabel}>
              {t('publish.category', 'Category')}
            </Text>
            <View style={styles.categoryGrid}>
              {CATEGORIES.map((category) => (
                <Chip
                  key={category}
                  selected={form.category === category}
                  onPress={() => updateForm('category', category)}
                  style={[
                    styles.categoryChip,
                    form.category === category && styles.selectedCategoryChip
                  ]}
                  textStyle={[
                    styles.categoryChipText,
                    form.category === category && styles.selectedCategoryChipText
                  ]}
                >
                  {category}
                </Chip>
              ))}
            </View>
          </View>
        </Surface>

        {/* 定价和时长 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('publish.pricingDuration', 'Pricing & Duration')}
          </Text>

          <View style={styles.row}>
            <TextInput
              label={t('publish.price', 'Price')}
              value={form.price.toString()}
              onChangeText={(text) => updateForm('price', parseFloat(text) || 0)}
              style={[styles.input, styles.priceInput]}
              keyboardType="numeric"
              left={<TextInput.Icon icon="currency-usd" />}
            />
            <TextInput
              label={t('publish.maxGroupSize', 'Max Group Size')}
              value={form.maxGroupSize.toString()}
              onChangeText={(text) => updateForm('maxGroupSize', parseInt(text) || 1)}
              style={[styles.input, styles.groupSizeInput]}
              keyboardType="numeric"
              left={<TextInput.Icon icon="account-group" />}
            />
          </View>

          <View style={styles.durationSection}>
            <Text variant="titleSmall" style={styles.fieldLabel}>
              {t('publish.duration', 'Duration')}
            </Text>
            <SegmentedButtons
              value={form.duration}
              onValueChange={(value) => updateForm('duration', value)}
              buttons={DURATION_OPTIONS}
              style={styles.segmentedButtons}
            />
          </View>
        </Surface>

        {/* 语言 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('publish.languages', 'Languages')}
          </Text>
          <Text variant="bodySmall" style={styles.fieldDescription}>
            {t('publish.languagesDescription', 'Select languages you can conduct tours in')}
          </Text>
          
          <View style={styles.languageGrid}>
            {LANGUAGES.map((language) => (
              <Chip
                key={language}
                selected={form.languages.includes(language)}
                onPress={() => toggleLanguage(language)}
                style={[
                  styles.languageChip,
                  form.languages.includes(language) && styles.selectedLanguageChip
                ]}
                textStyle={[
                  styles.languageChipText,
                  form.languages.includes(language) && styles.selectedLanguageChipText
                ]}
              >
                {language}
              </Chip>
            ))}
          </View>
        </Surface>

        {/* 包含内容 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('publish.includes', 'What\'s Included')}
          </Text>
          
          <View style={styles.includesSection}>
            <View style={styles.includeItem}>
              <Text variant="bodyMedium">{t('publish.transport', 'Transportation')}</Text>
              <Switch
                value={form.includesTransport}
                onValueChange={(value) => updateForm('includesTransport', value)}
              />
            </View>
            <View style={styles.includeItem}>
              <Text variant="bodyMedium">{t('publish.meals', 'Meals/Refreshments')}</Text>
              <Switch
                value={form.includesMeals}
                onValueChange={(value) => updateForm('includesMeals', value)}
              />
            </View>
            <View style={styles.includeItem}>
              <Text variant="bodyMedium">{t('publish.tickets', 'Entry Tickets')}</Text>
              <Switch
                value={form.includesTickets}
                onValueChange={(value) => updateForm('includesTickets', value)}
              />
            </View>
          </View>
        </Surface>

        {/* 会面地点和政策 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('publish.meetingPolicy', 'Meeting Point & Policies')}
          </Text>
          
          <TextInput
            label={t('publish.meetingPoint', 'Meeting Point')}
            value={form.meetingPoint}
            onChangeText={(text) => updateForm('meetingPoint', text)}
            style={styles.input}
            placeholder="e.g., Main entrance of City Hall"
            left={<TextInput.Icon icon="map-marker" />}
          />

          <View style={styles.policySection}>
            <Text variant="titleSmall" style={styles.fieldLabel}>
              {t('publish.cancellationPolicy', 'Cancellation Policy')}
            </Text>
            {CANCELLATION_POLICIES.map((policy) => (
              <View key={policy.value} style={styles.policyOption}>
                <Button
                  mode={form.cancellationPolicy === policy.value ? 'contained' : 'outlined'}
                  onPress={() => updateForm('cancellationPolicy', policy.value)}
                  style={styles.policyButton}
                  contentStyle={styles.policyButtonContent}
                >
                  {policy.label}
                </Button>
              </View>
            ))}
          </View>
        </Surface>

        {/* 发布设置 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('publish.publishSettings', 'Publish Settings')}
          </Text>
          
          <View style={styles.publishSetting}>
            <View style={styles.settingInfo}>
              <Text variant="bodyMedium" style={styles.settingTitle}>
                {t('publish.activeService', 'Active Service')}
              </Text>
              <Text variant="bodySmall" style={styles.settingDescription}>
                {t('publish.activeDescription', 'Make this service visible to travelers')}
              </Text>
            </View>
            <Switch
              value={form.isActive}
              onValueChange={(value) => updateForm('isActive', value)}
            />
          </View>
        </Surface>

        {/* 提交按钮 */}
        <View style={styles.submitSection}>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={isSubmitting}
            disabled={isSubmitting}
            style={styles.submitButton}
            contentStyle={styles.submitButtonContent}
          >
            {t('publish.publishService', 'Publish Service')}
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  title: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: spacing.xs,
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
  },
  section: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: theme.colors.onSurface,
  },
  input: {
    marginBottom: spacing.md,
    backgroundColor: 'transparent',
  },
  textArea: {
    marginBottom: spacing.md,
    backgroundColor: 'transparent',
    minHeight: 100,
  },
  row: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  priceInput: {
    flex: 1,
  },
  groupSizeInput: {
    flex: 1,
  },
  fieldLabel: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
    color: theme.colors.onSurface,
  },
  fieldDescription: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.md,
  },
  categorySection: {
    marginTop: spacing.md,
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  categoryChip: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.outline,
  },
  selectedCategoryChip: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  categoryChipText: {
    color: theme.colors.onSurface,
  },
  selectedCategoryChipText: {
    color: theme.colors.onPrimary,
  },
  durationSection: {
    marginTop: spacing.md,
  },
  segmentedButtons: {
    marginTop: spacing.sm,
  },
  languageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  languageChip: {
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.outline,
  },
  selectedLanguageChip: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  languageChipText: {
    color: theme.colors.onSurface,
  },
  selectedLanguageChipText: {
    color: theme.colors.onPrimary,
  },
  includesSection: {
    gap: spacing.md,
  },
  includeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  policySection: {
    marginTop: spacing.md,
  },
  policyOption: {
    marginBottom: spacing.sm,
  },
  policyButton: {
    justifyContent: 'flex-start',
  },
  policyButtonContent: {
    justifyContent: 'flex-start',
    paddingVertical: spacing.sm,
  },
  publishSetting: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  settingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  settingTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  settingDescription: {
    color: theme.custom.colors.textSecondary,
  },
  submitSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  submitButton: {
    borderRadius: theme.custom.borderRadius.md,
  },
  submitButtonContent: {
    paddingVertical: spacing.sm,
  },
});
