import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, FlatList, KeyboardAvoidingView, Platform } from 'react-native';
import { 
  Text, 
  TextInput,
  IconButton,
  Avatar,
  Surface,
  Card,
  Chip,
  Button
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';

interface ChatMessage {
  id: number;
  senderId: number;
  senderName: string;
  senderAvatar?: string;
  message: string;
  timestamp: Date;
  type: 'text' | 'service' | 'booking';
  serviceData?: {
    id: number;
    title: string;
    price: number;
    duration: string;
  };
}

interface ChatSession {
  id: number;
  participantId: number;
  participantName: string;
  participantAvatar?: string;
  isGuide: boolean;
  isOnline: boolean;
  lastMessage?: string;
  lastMessageTime?: Date;
  unreadCount: number;
}

const MOCK_SESSIONS: ChatSession[] = [
  {
    id: 1,
    participantId: 101,
    participantName: '<PERSON>',
    participantAvatar: 'https://via.placeholder.com/50',
    isGuide: true,
    isOnline: true,
    lastMessage: 'Looking forward to showing you around Barcelona!',
    lastMessageTime: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    unreadCount: 2,
  },
  {
    id: 2,
    participantId: 102,
    participantName: 'Ahmed Hassan',
    participantAvatar: 'https://via.placeholder.com/50',
    isGuide: true,
    isOnline: false,
    lastMessage: 'The Sagrada Familia tour includes skip-the-line tickets',
    lastMessageTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    unreadCount: 0,
  },
  {
    id: 3,
    participantId: 103,
    participantName: 'John Smith',
    participantAvatar: 'https://via.placeholder.com/50',
    isGuide: false,
    isOnline: true,
    lastMessage: 'Thank you for the amazing tour yesterday!',
    lastMessageTime: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    unreadCount: 1,
  },
];

const MOCK_MESSAGES: ChatMessage[] = [
  {
    id: 1,
    senderId: 101,
    senderName: 'Maria Santos',
    senderAvatar: 'https://via.placeholder.com/50',
    message: 'Hi! Thank you for your interest in my Barcelona food tour.',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    type: 'text',
  },
  {
    id: 2,
    senderId: 1, // Current user
    senderName: 'You',
    message: 'Hello! I\'m very excited about the tour. What time should we meet?',
    timestamp: new Date(Date.now() - 50 * 60 * 1000),
    type: 'text',
  },
  {
    id: 3,
    senderId: 101,
    senderName: 'Maria Santos',
    senderAvatar: 'https://via.placeholder.com/50',
    message: 'Perfect! Here\'s the service details:',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    type: 'text',
  },
  {
    id: 4,
    senderId: 101,
    senderName: 'Maria Santos',
    senderAvatar: 'https://via.placeholder.com/50',
    message: '',
    timestamp: new Date(Date.now() - 44 * 60 * 1000),
    type: 'service',
    serviceData: {
      id: 1,
      title: 'Barcelona Tapas & Wine Experience',
      price: 65,
      duration: '4 hours',
    },
  },
  {
    id: 5,
    senderId: 101,
    senderName: 'Maria Santos',
    senderAvatar: 'https://via.placeholder.com/50',
    message: 'We can meet at 6 PM at Plaça de Catalunya. Looking forward to showing you around Barcelona!',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    type: 'text',
  },
];

export function ChatScreen() {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);
  
  const [sessions, setSessions] = useState<ChatSession[]>(MOCK_SESSIONS);
  const [activeSession, setActiveSession] = useState<ChatSession | null>(sessions[0]);
  const [messages, setMessages] = useState<ChatMessage[]>(MOCK_MESSAGES);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // 滚动到最新消息
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  const sendMessage = () => {
    if (!newMessage.trim() || !activeSession) return;

    const message: ChatMessage = {
      id: Date.now(),
      senderId: user?.id || 1,
      senderName: 'You',
      message: newMessage.trim(),
      timestamp: new Date(),
      type: 'text',
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // 模拟对方回复
    setTimeout(() => {
      setIsTyping(true);
      setTimeout(() => {
        const reply: ChatMessage = {
          id: Date.now() + 1,
          senderId: activeSession.participantId,
          senderName: activeSession.participantName,
          senderAvatar: activeSession.participantAvatar,
          message: 'Thanks for your message! I\'ll get back to you soon.',
          timestamp: new Date(),
          type: 'text',
        };
        setMessages(prev => [...prev, reply]);
        setIsTyping(false);
      }, 2000);
    }, 1000);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatLastMessageTime = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return `${Math.floor(diffInHours * 60)}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => {
    const isOwnMessage = item.senderId === (user?.id || 1);
    
    if (item.type === 'service' && item.serviceData) {
      return (
        <View style={[styles.messageContainer, isOwnMessage && styles.ownMessageContainer]}>
          <Card style={styles.serviceCard} mode="elevated">
            <Card.Content style={styles.serviceCardContent}>
              <Text variant="titleMedium" style={styles.serviceTitle}>
                {item.serviceData.title}
              </Text>
              <View style={styles.serviceDetails}>
                <Text variant="bodyMedium" style={styles.servicePrice}>
                  ${item.serviceData.price}
                </Text>
                <Text variant="bodySmall" style={styles.serviceDuration}>
                  {item.serviceData.duration}
                </Text>
              </View>
              <Button mode="contained" style={styles.bookButton}>
                {t('chat.bookNow', 'Book Now')}
              </Button>
            </Card.Content>
          </Card>
          <Text variant="bodySmall" style={styles.messageTime}>
            {formatTime(item.timestamp)}
          </Text>
        </View>
      );
    }

    return (
      <View style={[styles.messageContainer, isOwnMessage && styles.ownMessageContainer]}>
        {!isOwnMessage && (
          <Avatar.Image 
            size={32} 
            source={{ uri: item.senderAvatar }} 
            style={styles.messageAvatar}
          />
        )}
        <View style={[styles.messageBubble, isOwnMessage && styles.ownMessageBubble]}>
          <Text variant="bodyMedium" style={[styles.messageText, isOwnMessage && styles.ownMessageText]}>
            {item.message}
          </Text>
        </View>
        <Text variant="bodySmall" style={styles.messageTime}>
          {formatTime(item.timestamp)}
        </Text>
      </View>
    );
  };

  const renderSessionItem = ({ item }: { item: ChatSession }) => (
    <Surface 
      style={[
        styles.sessionItem,
        activeSession?.id === item.id && styles.activeSessionItem
      ]} 
      elevation={activeSession?.id === item.id ? 2 : 0}
    >
      <Button
        mode="text"
        onPress={() => setActiveSession(item)}
        style={styles.sessionButton}
        contentStyle={styles.sessionButtonContent}
      >
        <View style={styles.sessionInfo}>
          <View style={styles.sessionHeader}>
            <Avatar.Image size={40} source={{ uri: item.participantAvatar }} />
            <View style={styles.sessionDetails}>
              <View style={styles.sessionNameRow}>
                <Text variant="titleSmall" style={styles.sessionName}>
                  {item.participantName}
                </Text>
                {item.isOnline && <View style={styles.onlineIndicator} />}
                {item.isGuide && (
                  <Chip style={styles.guideChip} textStyle={styles.guideChipText}>
                    Guide
                  </Chip>
                )}
              </View>
              <Text variant="bodySmall" style={styles.lastMessage} numberOfLines={1}>
                {item.lastMessage}
              </Text>
            </View>
          </View>
          <View style={styles.sessionMeta}>
            {item.lastMessageTime && (
              <Text variant="bodySmall" style={styles.lastMessageTime}>
                {formatLastMessageTime(item.lastMessageTime)}
              </Text>
            )}
            {item.unreadCount > 0 && (
              <Surface style={styles.unreadBadge} elevation={1}>
                <Text variant="bodySmall" style={styles.unreadCount}>
                  {item.unreadCount}
                </Text>
              </Surface>
            )}
          </View>
        </View>
      </Button>
    </Surface>
  );

  if (!activeSession) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.sessionsContainer}>
          <Text variant="headlineSmall" style={styles.sessionsTitle}>
            {t('chat.conversations', 'Conversations')}
          </Text>
          <FlatList
            data={sessions}
            renderItem={renderSessionItem}
            keyExtractor={(item) => item.id.toString()}
            style={styles.sessionsList}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.chatContainer} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* 聊天头部 */}
        <Surface style={styles.chatHeader} elevation={1}>
          <IconButton 
            icon="arrow-left" 
            onPress={() => setActiveSession(null)}
            style={styles.backButton}
          />
          <Avatar.Image size={40} source={{ uri: activeSession.participantAvatar }} />
          <View style={styles.headerInfo}>
            <Text variant="titleMedium" style={styles.headerName}>
              {activeSession.participantName}
            </Text>
            <Text variant="bodySmall" style={styles.headerStatus}>
              {activeSession.isOnline ? t('chat.online', 'Online') : t('chat.offline', 'Offline')}
            </Text>
          </View>
          <IconButton icon="phone" onPress={() => {}} />
          <IconButton icon="video" onPress={() => {}} />
        </Surface>

        {/* 消息列表 */}
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id.toString()}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
        />

        {/* 正在输入指示器 */}
        {isTyping && (
          <View style={styles.typingIndicator}>
            <Text variant="bodySmall" style={styles.typingText}>
              {activeSession.participantName} {t('chat.isTyping', 'is typing...')}
            </Text>
          </View>
        )}

        {/* 输入框 */}
        <Surface style={styles.inputContainer} elevation={2}>
          <TextInput
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder={t('chat.typeMessage', 'Type a message...')}
            style={styles.messageInput}
            multiline
            maxLength={500}
            right={
              <TextInput.Icon 
                icon="send" 
                onPress={sendMessage}
                disabled={!newMessage.trim()}
              />
            }
          />
        </Surface>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  sessionsContainer: {
    flex: 1,
    padding: spacing.lg,
  },
  sessionsTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.lg,
    color: theme.colors.onBackground,
  },
  sessionsList: {
    flex: 1,
  },
  sessionItem: {
    marginBottom: spacing.sm,
    borderRadius: theme.custom.borderRadius.md,
  },
  activeSessionItem: {
    backgroundColor: theme.colors.primaryContainer,
  },
  sessionButton: {
    justifyContent: 'flex-start',
  },
  sessionButtonContent: {
    justifyContent: 'flex-start',
    paddingVertical: spacing.sm,
  },
  sessionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sessionDetails: {
    flex: 1,
    marginLeft: spacing.md,
  },
  sessionNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  sessionName: {
    fontWeight: 'bold',
    marginRight: spacing.sm,
  },
  onlineIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
    marginRight: spacing.sm,
  },
  guideChip: {
    backgroundColor: theme.colors.primary,
    height: 20,
  },
  guideChipText: {
    color: theme.colors.onPrimary,
    fontSize: 10,
  },
  lastMessage: {
    color: theme.custom.colors.textSecondary,
  },
  sessionMeta: {
    alignItems: 'flex-end',
  },
  lastMessageTime: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
  },
  unreadBadge: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xs,
  },
  unreadCount: {
    color: theme.colors.onPrimary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  chatContainer: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  backButton: {
    marginRight: spacing.sm,
  },
  headerInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  headerName: {
    fontWeight: 'bold',
  },
  headerStatus: {
    color: theme.custom.colors.textSecondary,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: spacing.md,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: spacing.md,
  },
  ownMessageContainer: {
    flexDirection: 'row-reverse',
  },
  messageAvatar: {
    marginRight: spacing.sm,
  },
  messageBubble: {
    backgroundColor: theme.colors.surface,
    padding: spacing.md,
    borderRadius: theme.custom.borderRadius.md,
    maxWidth: '80%',
  },
  ownMessageBubble: {
    backgroundColor: theme.colors.primary,
    marginLeft: spacing.sm,
  },
  messageText: {
    color: theme.colors.onSurface,
  },
  ownMessageText: {
    color: theme.colors.onPrimary,
  },
  messageTime: {
    color: theme.custom.colors.textSecondary,
    marginTop: spacing.xs,
    marginHorizontal: spacing.sm,
  },
  serviceCard: {
    maxWidth: '80%',
    backgroundColor: theme.colors.surface,
  },
  serviceCardContent: {
    padding: spacing.md,
  },
  serviceTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  serviceDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  servicePrice: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  serviceDuration: {
    color: theme.custom.colors.textSecondary,
  },
  bookButton: {
    borderRadius: theme.custom.borderRadius.sm,
  },
  typingIndicator: {
    padding: spacing.md,
    paddingBottom: spacing.sm,
  },
  typingText: {
    color: theme.custom.colors.textSecondary,
    fontStyle: 'italic',
  },
  inputContainer: {
    padding: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  messageInput: {
    backgroundColor: 'transparent',
    maxHeight: 100,
  },
});
