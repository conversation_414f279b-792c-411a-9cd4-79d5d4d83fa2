import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, Alert, RefreshControl, Dimensions } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Chip, 
  Avatar,
  Surface,
  IconButton,
  Slider,
  Switch,
  FAB
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { guideDiscoveryService, GuideDiscoveryRequest, GuideDiscoveryResponse } from '@/services/guideDiscoveryService';

// 使用从服务导入的接口
type NearbyGuide = GuideDiscoveryResponse;

// 移除模拟数据，使用真实API

const SPECIALTIES = ['All', 'Cultural Tours', 'Food & Wine', 'Historical Sites', 'Art & Museums', 'Adventure', 'Photography'];

export function DiscoverScreen() {
  const { t } = useTranslation();

  const [guides, setGuides] = useState<NearbyGuide[]>([]);
  const [selectedSpecialty, setSelectedSpecialty] = useState('All');
  const [maxDistance, setMaxDistance] = useState(5); // km
  const [onlineOnly, setOnlineOnly] = useState(false);
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [currentLocation, setCurrentLocation] = useState('Barcelona, Spain');
  const [userCoordinates, setUserCoordinates] = useState<{latitude: number; longitude: number} | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  // 初始化数据
  useEffect(() => {
    initializeLocation();
  }, []);

  // 当筛选条件改变时重新搜索
  useEffect(() => {
    if (userCoordinates) {
      searchGuides(true);
    }
  }, [selectedSpecialty, maxDistance, onlineOnly, verifiedOnly, userCoordinates]);

  const initializeLocation = async () => {
    try {
      const coordinates = await guideDiscoveryService.getCurrentLocation();
      setUserCoordinates(coordinates);
      setCurrentLocation('Your Current Location');
    } catch (error) {
      console.error('Failed to get location:', error);
      // 使用默认位置
      const defaultCoords = { latitude: 41.3851, longitude: 2.1734 };
      setUserCoordinates(defaultCoords);
      setCurrentLocation('Barcelona, Spain');
    }
  };

  const searchGuides = async (reset = false) => {
    if (!userCoordinates) return;

    try {
      if (reset) {
        setInitialLoading(true);
        setPage(0);
        setGuides([]);
      } else {
        setLoading(true);
      }

      const request: GuideDiscoveryRequest = {
        latitude: userCoordinates.latitude,
        longitude: userCoordinates.longitude,
        radiusKm: maxDistance,
        specialties: selectedSpecialty === 'All' ? undefined : [selectedSpecialty],
        onlineOnly,
        verifiedOnly,
        sortBy: 'DISTANCE',
        sortDirection: 'ASC',
        page: reset ? 0 : page,
        size: 20,
      };

      const response = await guideDiscoveryService.discoverGuides(request);

      if (reset) {
        setGuides(response.content);
        setPage(1);
      } else {
        setGuides(prev => [...prev, ...response.content]);
        setPage(prev => prev + 1);
      }

      setHasMore(!response.last);
    } catch (error) {
      console.error('Failed to search guides:', error);
      Alert.alert('Error', 'Failed to load guides. Please try again.');
    } finally {
      setInitialLoading(false);
      setLoading(false);
    }
  };

  const filteredGuides = guides.filter((guide) => {
    const matchesSpecialty = selectedSpecialty === 'All' ||
                            guide.specialties.includes(selectedSpecialty);
    const matchesDistance = guide.distance <= maxDistance;
    const matchesOnline = !onlineOnly || guide.isOnline;
    const matchesVerified = !verifiedOnly || guide.isVerified;

    return matchesSpecialty && matchesDistance && matchesOnline && matchesVerified;
  });

  const requestLocation = () => {
    Alert.alert(
      t('discover.locationPermission', 'Location Permission'),
      t('discover.locationMessage', 'Allow access to your location to find nearby guides?'),
      [
        { text: t('common.cancel', 'Cancel'), style: 'cancel' },
        { text: t('common.allow', 'Allow'), onPress: initializeLocation },
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await searchGuides(true);
    setRefreshing(false);
  };

  const loadMoreGuides = () => {
    if (loading || !hasMore) return;
    searchGuides(false);
  };

  const renderGuideGridCard = ({ item }: { item: NearbyGuide }) => (
    <Card style={styles.gridCard} mode="elevated">
      <Card.Content style={styles.gridCardContent}>
        <View style={styles.gridAvatarContainer}>
          <Avatar.Image size={60} source={{ uri: item.avatarUrl }} />
          {item.isOnline && <View style={styles.gridOnlineIndicator} />}
        </View>

        <View style={styles.gridGuideInfo}>
          <View style={styles.gridNameRow}>
            <Text variant="titleSmall" style={styles.gridGuideName} numberOfLines={1}>
              {item.name}
            </Text>
            {item.isVerified && (
              <IconButton
                icon="check-decagram"
                size={16}
                iconColor={theme.colors.primary}
                style={styles.gridVerifiedIcon}
              />
            )}
          </View>

          <View style={styles.gridRatingRow}>
            <IconButton icon="star" size={14} iconColor="#FFD700" style={styles.gridStarIcon} />
            <Text variant="bodySmall" style={styles.gridRating}>
              {item.rating}
            </Text>
          </View>

          <Text variant="bodySmall" style={styles.gridDistance}>
            {item.distance} km
          </Text>

          <Text variant="bodySmall" style={styles.gridPrice}>
            ${item.hourlyRate}/hr
          </Text>
        </View>
      </Card.Content>
    </Card>
  );

  const renderGuideCard = ({ item }: { item: NearbyGuide }) => (
    <Card style={styles.guideCard} mode="elevated">
      <Card.Content style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <View style={styles.guideInfo}>
            <View style={styles.avatarContainer}>
              <Avatar.Image size={50} source={{ uri: item.avatarUrl }} />
              {item.isOnline && <View style={styles.onlineIndicator} />}
            </View>
            <View style={styles.guideDetails}>
              <View style={styles.nameRow}>
                <Text variant="titleMedium" style={styles.guideName}>
                  {item.name}
                </Text>
                {item.isVerified && (
                  <IconButton 
                    icon="check-decagram" 
                    size={20} 
                    iconColor={theme.colors.primary}
                    style={styles.verifiedIcon}
                  />
                )}
              </View>
              <View style={styles.ratingRow}>
                <IconButton icon="star" size={16} iconColor="#FFD700" style={styles.starIcon} />
                <Text variant="bodySmall" style={styles.rating}>
                  {item.rating} ({item.reviewCount} reviews)
                </Text>
              </View>
              <View style={styles.distanceRow}>
                <IconButton icon="map-marker" size={16} iconColor={theme.colors.primary} style={styles.locationIcon} />
                <Text variant="bodySmall" style={styles.distance}>
                  {item.distance} km away
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.priceInfo}>
            <Text variant="titleSmall" style={styles.hourlyRate}>
              ${item.hourlyRate}/hr
            </Text>
            <Text variant="bodySmall" style={styles.responseTime}>
              {item.responseTime}
            </Text>
          </View>
        </View>

        <View style={styles.specialtiesContainer}>
          {item.specialties.slice(0, 3).map((specialty, index) => (
            <Chip key={index} style={styles.specialtyChip} textStyle={styles.specialtyText}>
              {specialty}
            </Chip>
          ))}
        </View>

        <View style={styles.languagesContainer}>
          <Text variant="bodySmall" style={styles.languagesLabel}>
            {t('discover.languages', 'Languages')}: 
          </Text>
          <Text variant="bodySmall" style={styles.languages}>
            {item.languages.join(', ')}
          </Text>
        </View>

        <View style={styles.servicesContainer}>
          <Text variant="bodySmall" style={styles.servicesLabel}>
            {t('discover.popularServices', 'Popular Services')}:
          </Text>
          {item.popularServices.slice(0, 2).map((service) => (
            <View key={service.id} style={styles.serviceItem}>
              <Text variant="bodySmall" style={styles.serviceTitle}>
                • {service.title}
              </Text>
              <Text variant="bodySmall" style={styles.servicePrice}>
                ${service.price} ({service.duration})
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.cardActions}>
          <Button 
            mode="outlined" 
            style={styles.actionButton}
            onPress={() => {}}
          >
            {t('discover.viewProfile', 'View Profile')}
          </Button>
          <Button 
            mode="contained" 
            style={styles.actionButton}
            onPress={() => {}}
          >
            {t('discover.contact', 'Contact')}
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <View style={styles.locationHeader}>
          <IconButton icon="map-marker" size={20} iconColor={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.currentLocation}>
            {currentLocation}
          </Text>
          <View style={styles.headerActions}>
            <IconButton
              icon={viewMode === 'list' ? 'view-grid' : 'view-list'}
              size={20}
              onPress={() => setViewMode(viewMode === 'list' ? 'grid' : 'list')}
              iconColor={theme.colors.primary}
            />
            <IconButton
              icon="crosshairs-gps"
              size={20}
              onPress={requestLocation}
              iconColor={theme.colors.primary}
            />
          </View>
        </View>
        <View style={styles.subtitleRow}>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {t('discover.subtitle', 'Find local guides near you')}
          </Text>
          <Text variant="bodySmall" style={styles.resultsCount}>
            {filteredGuides.length} {t('discover.guidesFound', 'guides found')}
          </Text>
        </View>
      </View>

      {/* 筛选器 */}
      <Surface style={styles.filtersContainer} elevation={1}>
        {/* 专业领域筛选 */}
        <View style={styles.filterSection}>
          <Text variant="titleSmall" style={styles.filterTitle}>
            {t('discover.specialties', 'Specialties')}
          </Text>
          <FlatList
            data={SPECIALTIES}
            horizontal
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <Chip
                selected={selectedSpecialty === item}
                onPress={() => setSelectedSpecialty(item)}
                style={[
                  styles.filterChip,
                  selectedSpecialty === item && styles.selectedFilterChip
                ]}
                textStyle={[
                  styles.filterChipText,
                  selectedSpecialty === item && styles.selectedFilterChipText
                ]}
              >
                {item}
              </Chip>
            )}
            keyExtractor={(item) => item}
            contentContainerStyle={styles.chipsList}
          />
        </View>

        {/* 距离筛选 */}
        <View style={styles.filterSection}>
          <Text variant="titleSmall" style={styles.filterTitle}>
            {t('discover.maxDistance', 'Max Distance')}: {maxDistance} km
          </Text>
          <Slider
            style={styles.distanceSlider}
            minimumValue={0.5}
            maximumValue={10}
            value={maxDistance}
            onValueChange={setMaxDistance}
            step={0.5}
            thumbColor={theme.colors.primary}
            minimumTrackTintColor={theme.colors.primary}
            maximumTrackTintColor={theme.custom.colors.textSecondary}
          />
        </View>

        {/* 开关筛选 */}
        <View style={styles.switchFilters}>
          <View style={styles.switchFilter}>
            <Text variant="bodyMedium">{t('discover.onlineOnly', 'Online Only')}</Text>
            <Switch value={onlineOnly} onValueChange={setOnlineOnly} />
          </View>
          <View style={styles.switchFilter}>
            <Text variant="bodyMedium">{t('discover.verifiedOnly', 'Verified Only')}</Text>
            <Switch value={verifiedOnly} onValueChange={setVerifiedOnly} />
          </View>
        </View>
      </Surface>

      {/* 初始加载状态 */}
      {initialLoading ? (
        <View style={styles.initialLoadingContainer}>
          <Text variant="bodyMedium" style={styles.loadingText}>
            {t('discover.searchingGuides', 'Searching for guides near you...')}
          </Text>
        </View>
      ) : (
        /* 导游列表 */
        <FlatList
        data={filteredGuides}
        renderItem={viewMode === 'grid' ? renderGuideGridCard : renderGuideCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.guidesList}
        showsVerticalScrollIndicator={false}
        numColumns={viewMode === 'grid' ? 2 : 1}
        key={viewMode} // 强制重新渲染当视图模式改变时
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        onEndReached={loadMoreGuides}
        onEndReachedThreshold={0.1}
        ListFooterComponent={
          loading ? (
            <View style={styles.loadingFooter}>
              <Text variant="bodySmall" style={styles.loadingText}>
                {t('common.loading', 'Loading...')}
              </Text>
            </View>
          ) : null
        }
        />
      )}

      {/* 浮动操作按钮 */}
      <FAB
        icon="map"
        style={styles.fab}
        onPress={() => {
          Alert.alert(t('discover.mapView', 'Map View'), t('discover.mapViewSoon', 'Map view coming soon!'));
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  currentLocation: {
    flex: 1,
    fontWeight: 'bold',
    marginLeft: -spacing.xs,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subtitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginLeft: spacing.lg,
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
  },
  resultsCount: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  filtersContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.md,
    borderRadius: theme.custom.borderRadius.lg,
  },
  filterSection: {
    marginBottom: spacing.md,
  },
  filterTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  chipsList: {
    paddingRight: spacing.lg,
  },
  filterChip: {
    marginRight: spacing.sm,
    backgroundColor: theme.colors.surface,
  },
  selectedFilterChip: {
    backgroundColor: theme.colors.primary,
  },
  filterChipText: {
    color: theme.colors.onSurface,
  },
  selectedFilterChipText: {
    color: theme.colors.onPrimary,
  },
  distanceSlider: {
    width: '100%',
    height: 40,
  },
  switchFilters: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  switchFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  guidesList: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  guideCard: {
    marginBottom: spacing.lg,
    backgroundColor: theme.colors.surface,
  },
  cardContent: {
    padding: spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  guideInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: spacing.md,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  guideDetails: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  guideName: {
    fontWeight: 'bold',
  },
  verifiedIcon: {
    margin: 0,
    marginLeft: -spacing.xs,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  starIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  rating: {
    color: theme.custom.colors.textSecondary,
  },
  distanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  distance: {
    color: theme.custom.colors.textSecondary,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  hourlyRate: {
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: spacing.xs,
  },
  responseTime: {
    color: theme.custom.colors.textSecondary,
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.md,
    gap: spacing.xs,
  },
  specialtyChip: {
    backgroundColor: theme.colors.primaryContainer,
  },
  specialtyText: {
    color: theme.colors.onPrimaryContainer,
    fontSize: 12,
  },
  languagesContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  languagesLabel: {
    fontWeight: 'bold',
    marginRight: spacing.xs,
  },
  languages: {
    color: theme.custom.colors.textSecondary,
    flex: 1,
  },
  servicesContainer: {
    marginBottom: spacing.md,
  },
  servicesLabel: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  serviceTitle: {
    flex: 1,
    color: theme.custom.colors.textSecondary,
  },
  servicePrice: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  cardActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  // 网格视图样式
  gridCard: {
    flex: 1,
    margin: spacing.xs,
    backgroundColor: theme.colors.surface,
    maxWidth: (Dimensions.get('window').width - spacing.lg * 3) / 2,
  },
  gridCardContent: {
    padding: spacing.md,
    alignItems: 'center',
  },
  gridAvatarContainer: {
    position: 'relative',
    marginBottom: spacing.sm,
  },
  gridOnlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  gridGuideInfo: {
    alignItems: 'center',
    width: '100%',
  },
  gridNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  gridGuideName: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  gridVerifiedIcon: {
    margin: 0,
    marginLeft: -spacing.xs,
  },
  gridRatingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  gridStarIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  gridRating: {
    color: theme.custom.colors.textSecondary,
  },
  gridDistance: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
  },
  gridPrice: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  // 加载样式
  initialLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  loadingFooter: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  loadingText: {
    color: theme.custom.colors.textSecondary,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});
