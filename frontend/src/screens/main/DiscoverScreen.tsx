import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Chip, 
  Avatar,
  Surface,
  IconButton,
  Slider,
  Switch,
  FAB
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';

interface NearbyGuide {
  id: number;
  name: string;
  avatar?: string;
  rating: number;
  reviewCount: number;
  distance: number; // in kilometers
  isOnline: boolean;
  isVerified: boolean;
  specialties: string[];
  hourlyRate: number;
  currency: string;
  languages: string[];
  responseTime: string;
  location: {
    city: string;
    country: string;
  };
  services: {
    id: number;
    title: string;
    price: number;
    duration: string;
  }[];
}

const MOCK_GUIDES: NearbyGuide[] = [
  {
    id: 1,
    name: '<PERSON>',
    avatar: 'https://via.placeholder.com/50',
    rating: 4.9,
    reviewCount: 234,
    distance: 0.8,
    isOnline: true,
    isVerified: true,
    specialties: ['Cultural Tours', 'Food & Wine', 'Photography'],
    hourlyRate: 35,
    currency: 'USD',
    languages: ['English', 'Spanish', 'Portuguese'],
    responseTime: '< 1 hour',
    location: {
      city: 'Barcelona',
      country: 'Spain',
    },
    services: [
      { id: 1, title: 'Gothic Quarter Walking Tour', price: 45, duration: '3h' },
      { id: 2, title: 'Tapas & Wine Experience', price: 65, duration: '4h' },
    ],
  },
  {
    id: 2,
    name: 'Ahmed Hassan',
    avatar: 'https://via.placeholder.com/50',
    rating: 4.7,
    reviewCount: 156,
    distance: 1.2,
    isOnline: false,
    isVerified: true,
    specialties: ['Historical Sites', 'Architecture', 'Local Culture'],
    hourlyRate: 40,
    currency: 'USD',
    languages: ['English', 'Arabic', 'French'],
    responseTime: '< 2 hours',
    location: {
      city: 'Barcelona',
      country: 'Spain',
    },
    services: [
      { id: 3, title: 'Sagrada Familia Deep Dive', price: 55, duration: '2.5h' },
      { id: 4, title: 'Modernist Architecture Tour', price: 70, duration: '4h' },
    ],
  },
  {
    id: 3,
    name: 'Elena Rossi',
    avatar: 'https://via.placeholder.com/50',
    rating: 4.8,
    reviewCount: 189,
    distance: 2.1,
    isOnline: true,
    isVerified: false,
    specialties: ['Art & Museums', 'Shopping', 'Nightlife'],
    hourlyRate: 30,
    currency: 'USD',
    languages: ['English', 'Italian', 'Catalan'],
    responseTime: '< 30 min',
    location: {
      city: 'Barcelona',
      country: 'Spain',
    },
    services: [
      { id: 5, title: 'Picasso Museum & Art Walk', price: 50, duration: '3h' },
      { id: 6, title: 'Local Shopping Experience', price: 35, duration: '2h' },
    ],
  },
];

const SPECIALTIES = ['All', 'Cultural Tours', 'Food & Wine', 'Historical Sites', 'Art & Museums', 'Adventure', 'Photography'];

export function DiscoverScreen() {
  const { t } = useTranslation();
  
  const [guides, setGuides] = useState<NearbyGuide[]>(MOCK_GUIDES);
  const [selectedSpecialty, setSelectedSpecialty] = useState('All');
  const [maxDistance, setMaxDistance] = useState(5); // km
  const [onlineOnly, setOnlineOnly] = useState(false);
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [currentLocation, setCurrentLocation] = useState('Barcelona, Spain');

  const filteredGuides = guides.filter(guide => {
    const matchesSpecialty = selectedSpecialty === 'All' || 
                            guide.specialties.includes(selectedSpecialty);
    const matchesDistance = guide.distance <= maxDistance;
    const matchesOnline = !onlineOnly || guide.isOnline;
    const matchesVerified = !verifiedOnly || guide.isVerified;
    
    return matchesSpecialty && matchesDistance && matchesOnline && matchesVerified;
  });

  const requestLocation = () => {
    Alert.alert(
      t('discover.locationPermission', 'Location Permission'),
      t('discover.locationMessage', 'Allow access to your location to find nearby guides?'),
      [
        { text: t('common.cancel', 'Cancel'), style: 'cancel' },
        { text: t('common.allow', 'Allow'), onPress: () => {
          // 模拟获取位置
          setCurrentLocation('Your Current Location');
        }},
      ]
    );
  };

  const renderGuideCard = ({ item }: { item: NearbyGuide }) => (
    <Card style={styles.guideCard} mode="elevated">
      <Card.Content style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <View style={styles.guideInfo}>
            <View style={styles.avatarContainer}>
              <Avatar.Image size={50} source={{ uri: item.avatar }} />
              {item.isOnline && <View style={styles.onlineIndicator} />}
            </View>
            <View style={styles.guideDetails}>
              <View style={styles.nameRow}>
                <Text variant="titleMedium" style={styles.guideName}>
                  {item.name}
                </Text>
                {item.isVerified && (
                  <IconButton 
                    icon="check-decagram" 
                    size={20} 
                    iconColor={theme.colors.primary}
                    style={styles.verifiedIcon}
                  />
                )}
              </View>
              <View style={styles.ratingRow}>
                <IconButton icon="star" size={16} iconColor="#FFD700" style={styles.starIcon} />
                <Text variant="bodySmall" style={styles.rating}>
                  {item.rating} ({item.reviewCount} reviews)
                </Text>
              </View>
              <View style={styles.distanceRow}>
                <IconButton icon="map-marker" size={16} iconColor={theme.colors.primary} style={styles.locationIcon} />
                <Text variant="bodySmall" style={styles.distance}>
                  {item.distance} km away
                </Text>
              </View>
            </View>
          </View>
          <View style={styles.priceInfo}>
            <Text variant="titleSmall" style={styles.hourlyRate}>
              ${item.hourlyRate}/hr
            </Text>
            <Text variant="bodySmall" style={styles.responseTime}>
              {item.responseTime}
            </Text>
          </View>
        </View>

        <View style={styles.specialtiesContainer}>
          {item.specialties.slice(0, 3).map((specialty, index) => (
            <Chip key={index} style={styles.specialtyChip} textStyle={styles.specialtyText}>
              {specialty}
            </Chip>
          ))}
        </View>

        <View style={styles.languagesContainer}>
          <Text variant="bodySmall" style={styles.languagesLabel}>
            {t('discover.languages', 'Languages')}: 
          </Text>
          <Text variant="bodySmall" style={styles.languages}>
            {item.languages.join(', ')}
          </Text>
        </View>

        <View style={styles.servicesContainer}>
          <Text variant="bodySmall" style={styles.servicesLabel}>
            {t('discover.popularServices', 'Popular Services')}:
          </Text>
          {item.services.slice(0, 2).map((service) => (
            <View key={service.id} style={styles.serviceItem}>
              <Text variant="bodySmall" style={styles.serviceTitle}>
                • {service.title}
              </Text>
              <Text variant="bodySmall" style={styles.servicePrice}>
                ${service.price} ({service.duration})
              </Text>
            </View>
          ))}
        </View>

        <View style={styles.cardActions}>
          <Button 
            mode="outlined" 
            style={styles.actionButton}
            onPress={() => {}}
          >
            {t('discover.viewProfile', 'View Profile')}
          </Button>
          <Button 
            mode="contained" 
            style={styles.actionButton}
            onPress={() => {}}
          >
            {t('discover.contact', 'Contact')}
          </Button>
        </View>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <View style={styles.locationHeader}>
          <IconButton icon="map-marker" size={20} iconColor={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.currentLocation}>
            {currentLocation}
          </Text>
          <IconButton 
            icon="crosshairs-gps" 
            size={20} 
            onPress={requestLocation}
            iconColor={theme.colors.primary}
          />
        </View>
        <Text variant="bodyMedium" style={styles.subtitle}>
          {t('discover.subtitle', 'Find local guides near you')}
        </Text>
      </View>

      {/* 筛选器 */}
      <Surface style={styles.filtersContainer} elevation={1}>
        {/* 专业领域筛选 */}
        <View style={styles.filterSection}>
          <Text variant="titleSmall" style={styles.filterTitle}>
            {t('discover.specialties', 'Specialties')}
          </Text>
          <FlatList
            data={SPECIALTIES}
            horizontal
            showsHorizontalScrollIndicator={false}
            renderItem={({ item }) => (
              <Chip
                selected={selectedSpecialty === item}
                onPress={() => setSelectedSpecialty(item)}
                style={[
                  styles.filterChip,
                  selectedSpecialty === item && styles.selectedFilterChip
                ]}
                textStyle={[
                  styles.filterChipText,
                  selectedSpecialty === item && styles.selectedFilterChipText
                ]}
              >
                {item}
              </Chip>
            )}
            keyExtractor={(item) => item}
            contentContainerStyle={styles.chipsList}
          />
        </View>

        {/* 距离筛选 */}
        <View style={styles.filterSection}>
          <Text variant="titleSmall" style={styles.filterTitle}>
            {t('discover.maxDistance', 'Max Distance')}: {maxDistance} km
          </Text>
          <Slider
            style={styles.distanceSlider}
            minimumValue={0.5}
            maximumValue={10}
            value={maxDistance}
            onValueChange={setMaxDistance}
            step={0.5}
            thumbColor={theme.colors.primary}
            minimumTrackTintColor={theme.colors.primary}
            maximumTrackTintColor={theme.custom.colors.textSecondary}
          />
        </View>

        {/* 开关筛选 */}
        <View style={styles.switchFilters}>
          <View style={styles.switchFilter}>
            <Text variant="bodyMedium">{t('discover.onlineOnly', 'Online Only')}</Text>
            <Switch value={onlineOnly} onValueChange={setOnlineOnly} />
          </View>
          <View style={styles.switchFilter}>
            <Text variant="bodyMedium">{t('discover.verifiedOnly', 'Verified Only')}</Text>
            <Switch value={verifiedOnly} onValueChange={setVerifiedOnly} />
          </View>
        </View>
      </Surface>

      {/* 导游列表 */}
      <FlatList
        data={filteredGuides}
        renderItem={renderGuideCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.guidesList}
        showsVerticalScrollIndicator={false}
      />

      {/* 浮动操作按钮 */}
      <FAB
        icon="map"
        style={styles.fab}
        onPress={() => {
          Alert.alert(t('discover.mapView', 'Map View'), t('discover.mapViewSoon', 'Map view coming soon!'));
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  currentLocation: {
    flex: 1,
    fontWeight: 'bold',
    marginLeft: -spacing.xs,
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
    marginLeft: spacing.lg,
  },
  filtersContainer: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.md,
    borderRadius: theme.custom.borderRadius.lg,
  },
  filterSection: {
    marginBottom: spacing.md,
  },
  filterTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  chipsList: {
    paddingRight: spacing.lg,
  },
  filterChip: {
    marginRight: spacing.sm,
    backgroundColor: theme.colors.surface,
  },
  selectedFilterChip: {
    backgroundColor: theme.colors.primary,
  },
  filterChipText: {
    color: theme.colors.onSurface,
  },
  selectedFilterChipText: {
    color: theme.colors.onPrimary,
  },
  distanceSlider: {
    width: '100%',
    height: 40,
  },
  switchFilters: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  switchFilter: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  guidesList: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
  },
  guideCard: {
    marginBottom: spacing.lg,
    backgroundColor: theme.colors.surface,
  },
  cardContent: {
    padding: spacing.lg,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  guideInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: spacing.md,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  guideDetails: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  guideName: {
    fontWeight: 'bold',
  },
  verifiedIcon: {
    margin: 0,
    marginLeft: -spacing.xs,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  starIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  rating: {
    color: theme.custom.colors.textSecondary,
  },
  distanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  distance: {
    color: theme.custom.colors.textSecondary,
  },
  priceInfo: {
    alignItems: 'flex-end',
  },
  hourlyRate: {
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: spacing.xs,
  },
  responseTime: {
    color: theme.custom.colors.textSecondary,
  },
  specialtiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.md,
    gap: spacing.xs,
  },
  specialtyChip: {
    backgroundColor: theme.colors.primaryContainer,
  },
  specialtyText: {
    color: theme.colors.onPrimaryContainer,
    fontSize: 12,
  },
  languagesContainer: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  languagesLabel: {
    fontWeight: 'bold',
    marginRight: spacing.xs,
  },
  languages: {
    color: theme.custom.colors.textSecondary,
    flex: 1,
  },
  servicesContainer: {
    marginBottom: spacing.md,
  },
  servicesLabel: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  serviceTitle: {
    flex: 1,
    color: theme.custom.colors.textSecondary,
  },
  servicePrice: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  cardActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});
