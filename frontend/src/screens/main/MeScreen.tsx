import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { 
  Text, 
  Avatar,
  Button, 
  Surface
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector, useAppDispatch } from '@/store';
import { logout } from '@/store/slices/authSlice';

export function MeScreen() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.auth.user);

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 用户信息头部 */}
        <Surface style={styles.profileHeader} elevation={1}>
          <View style={styles.profileInfo}>
            <Avatar.Image
              size={80}
              source={{ uri: user?.avatarUrl || 'https://via.placeholder.com/80' }}
              style={styles.avatar}
            />
            <View style={styles.userDetails}>
              <Text variant="headlineSmall" style={styles.userName}>
                {user?.firstName} {user?.lastName}
              </Text>
              <Text variant="bodyMedium" style={styles.userEmail}>
                {user?.email}
              </Text>
            </View>
          </View>
        </Surface>

        {/* 简单的设置选项 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('me.settings', 'Settings')}
          </Text>
          <Button 
            mode="outlined" 
            onPress={handleLogout}
            style={styles.logoutButton}
          >
            {t('common.logout', 'Logout')}
          </Button>
        </Surface>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    margin: spacing.lg,
    padding: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: spacing.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  userEmail: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
  },
  section: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: theme.colors.onSurface,
  },
  logoutButton: {
    borderColor: theme.colors.error,
    borderRadius: theme.custom.borderRadius.md,
  },
});
