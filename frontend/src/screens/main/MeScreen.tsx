import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Avatar,
  Button, 
  Card,
  Surface,
  IconButton,
  Switch,
  Divider,
  List,
  Badge,
  ProgressBar
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector, useAppDispatch } from '@/store';
import { logout } from '@/store/slices/authSlice';
import { userProfileService, UserStats, UserProfile, WithdrawalHistory, EarningsHistory } from '@/services/userProfileService';

// 使用从服务导入的接口

export function MeScreen() {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const user = useAppSelector(state => state.auth.user);
  
  const [isGuideMode, setIsGuideMode] = useState(true);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [withdrawalHistory, setWithdrawalHistory] = useState<WithdrawalHistory[]>([]);
  const [earningsHistory, setEarningsHistory] = useState<EarningsHistory[]>([]);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');

  // 加载用户数据
  React.useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      const [statsData, profileData, withdrawalsData, earningsData] = await Promise.all([
        userProfileService.getUserStats(),
        userProfileService.getUserProfile(),
        userProfileService.getWithdrawalHistory(),
        userProfileService.getEarningsHistory()
      ]);

      setStats(statsData);
      setUserProfile(profileData);
      setWithdrawalHistory(withdrawalsData);
      setEarningsHistory(earningsData);
    } catch (error) {
      console.error('Failed to load user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      t('me.logoutConfirm', 'Logout'),
      t('me.logoutMessage', 'Are you sure you want to logout?'),
      [
        { text: t('common.cancel', 'Cancel'), style: 'cancel' },
        { 
          text: t('common.logout', 'Logout'), 
          style: 'destructive',
          onPress: () => dispatch(logout())
        },
      ]
    );
  };

  const handleWithdraw = () => {
    Alert.alert(
      t('me.withdraw', 'Withdraw Funds'),
      t('me.withdrawMessage', `Available balance: $${stats.availableBalance.toFixed(2)}`),
      [
        { text: t('common.cancel', 'Cancel'), style: 'cancel' },
        { 
          text: t('me.withdrawConfirm', 'Withdraw'), 
          onPress: () => {
            // 模拟提现
            Alert.alert(t('common.success', 'Success'), t('me.withdrawSuccess', 'Withdrawal request submitted'));
          }
        },
      ]
    );
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Bronze': return '#CD7F32';
      case 'Silver': return '#C0C0C0';
      case 'Gold': return '#FFD700';
      case 'Platinum': return '#E5E4E2';
      default: return theme.colors.primary;
    }
  };

  const getLevelProgress = (level: string) => {
    switch (level) {
      case 'Bronze': return 0.25;
      case 'Silver': return 0.5;
      case 'Gold': return 0.75;
      case 'Platinum': return 1.0;
      default: return 0;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 用户信息头部 */}
        <Surface style={styles.profileHeader} elevation={1}>
          <View style={styles.profileInfo}>
            <Avatar.Image 
              size={80} 
              source={{ uri: user?.avatar || 'https://via.placeholder.com/80' }}
              style={styles.avatar}
            />
            <View style={styles.userDetails}>
              <Text variant="headlineSmall" style={styles.userName}>
                {user?.firstName} {user?.lastName}
              </Text>
              <Text variant="bodyMedium" style={styles.userEmail}>
                {user?.email}
              </Text>
              {stats.isGuideVerified && (
                <View style={styles.verificationBadge}>
                  <IconButton 
                    icon="check-decagram" 
                    size={20} 
                    iconColor={theme.colors.primary}
                    style={styles.verifiedIcon}
                  />
                  <Text variant="bodySmall" style={styles.verifiedText}>
                    {t('me.verifiedGuide', 'Verified Guide')}
                  </Text>
                </View>
              )}
            </View>
            <IconButton 
              icon="pencil" 
              onPress={() => {}}
              style={styles.editButton}
            />
          </View>
        </Surface>

        {/* 导游模式切换 */}
        <Surface style={styles.guideModeSection} elevation={1}>
          <View style={styles.guideModeToggle}>
            <View style={styles.guideModeInfo}>
              <Text variant="titleMedium" style={styles.guideModeTitle}>
                {t('me.guideMode', 'Guide Mode')}
              </Text>
              <Text variant="bodySmall" style={styles.guideModeDescription}>
                {t('me.guideModeDescription', 'Switch between traveler and guide features')}
              </Text>
            </View>
            <Switch
              value={isGuideMode}
              onValueChange={setIsGuideMode}
            />
          </View>
        </Surface>

        {/* 导游统计信息 */}
        {isGuideMode && (
          <>
            {/* 财务概览 */}
            <Surface style={styles.section} elevation={1}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                {t('me.financialOverview', 'Financial Overview')}
              </Text>
              
              <View style={styles.balanceCards}>
                <Card style={styles.balanceCard} mode="elevated">
                  <Card.Content style={styles.balanceCardContent}>
                    <Text variant="bodySmall" style={styles.balanceLabel}>
                      {t('me.availableBalance', 'Available Balance')}
                    </Text>
                    <Text variant="headlineSmall" style={styles.balanceAmount}>
                      ${stats.availableBalance.toFixed(2)}
                    </Text>
                  </Card.Content>
                </Card>
                
                <Card style={styles.balanceCard} mode="elevated">
                  <Card.Content style={styles.balanceCardContent}>
                    <Text variant="bodySmall" style={styles.balanceLabel}>
                      {t('me.pendingBalance', 'Pending')}
                    </Text>
                    <Text variant="headlineSmall" style={styles.pendingAmount}>
                      ${stats.pendingBalance.toFixed(2)}
                    </Text>
                  </Card.Content>
                </Card>
              </View>

              <View style={styles.totalEarnings}>
                <Text variant="bodyMedium" style={styles.totalEarningsLabel}>
                  {t('me.totalEarnings', 'Total Earnings')}
                </Text>
                <Text variant="titleLarge" style={styles.totalEarningsAmount}>
                  ${stats.totalEarnings.toFixed(2)}
                </Text>
              </View>

              <Button 
                mode="contained" 
                onPress={handleWithdraw}
                style={styles.withdrawButton}
                disabled={stats.availableBalance <= 0}
              >
                {t('me.withdraw', 'Withdraw Funds')}
              </Button>
            </Surface>

            {/* 导游等级 */}
            <Surface style={styles.section} elevation={1}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                {t('me.guideLevel', 'Guide Level')}
              </Text>
              
              <View style={styles.levelInfo}>
                <View style={styles.levelBadge}>
                  <IconButton 
                    icon="star" 
                    size={24} 
                    iconColor={getLevelColor(stats.guideLevel)}
                    style={styles.levelIcon}
                  />
                  <Text variant="titleMedium" style={[styles.levelText, { color: getLevelColor(stats.guideLevel) }]}>
                    {stats.guideLevel}
                  </Text>
                </View>
                <ProgressBar 
                  progress={getLevelProgress(stats.guideLevel)} 
                  color={getLevelColor(stats.guideLevel)}
                  style={styles.levelProgress}
                />
                <Text variant="bodySmall" style={styles.levelDescription}>
                  {t('me.levelDescription', 'Complete more tours to reach the next level')}
                </Text>
              </View>
            </Surface>

            {/* 导游统计 */}
            <Surface style={styles.section} elevation={1}>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                {t('me.guideStats', 'Guide Statistics')}
              </Text>
              
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text variant="headlineSmall" style={styles.statValue}>
                    {stats.completedTours}
                  </Text>
                  <Text variant="bodySmall" style={styles.statLabel}>
                    {t('me.completedTours', 'Completed Tours')}
                  </Text>
                </View>
                
                <View style={styles.statItem}>
                  <View style={styles.ratingContainer}>
                    <Text variant="headlineSmall" style={styles.statValue}>
                      {stats.rating}
                    </Text>
                    <IconButton icon="star" size={20} iconColor="#FFD700" style={styles.ratingIcon} />
                  </View>
                  <Text variant="bodySmall" style={styles.statLabel}>
                    {t('me.averageRating', 'Average Rating')} ({stats.reviewCount})
                  </Text>
                </View>
                
                <View style={styles.statItem}>
                  <Text variant="headlineSmall" style={styles.statValue}>
                    {stats.responseRate}%
                  </Text>
                  <Text variant="bodySmall" style={styles.statLabel}>
                    {t('me.responseRate', 'Response Rate')}
                  </Text>
                </View>
                
                <View style={styles.statItem}>
                  <Text variant="headlineSmall" style={styles.statValue}>
                    {stats.totalBookings}
                  </Text>
                  <Text variant="bodySmall" style={styles.statLabel}>
                    {t('me.totalBookings', 'Total Bookings')}
                  </Text>
                </View>
              </View>
            </Surface>
          </>
        )}

        {/* 设置和选项 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleMedium" style={styles.sectionTitle}>
            {t('me.settings', 'Settings')}
          </Text>
          
          <List.Item
            title={t('me.notifications', 'Notifications')}
            description={t('me.notificationsDescription', 'Receive booking and message alerts')}
            left={props => <List.Icon {...props} icon="bell" />}
            right={() => (
              <Switch
                value={notificationsEnabled}
                onValueChange={setNotificationsEnabled}
              />
            )}
          />
          
          <Divider />
          
          <List.Item
            title={t('me.paymentMethods', 'Payment Methods')}
            description={t('me.paymentDescription', 'Manage your payment options')}
            left={props => <List.Icon {...props} icon="credit-card" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          
          <Divider />
          
          <List.Item
            title={t('me.language', 'Language')}
            description={t('me.languageDescription', 'Change app language')}
            left={props => <List.Icon {...props} icon="translate" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          
          <Divider />
          
          <List.Item
            title={t('me.help', 'Help & Support')}
            description={t('me.helpDescription', 'Get help and contact support')}
            left={props => <List.Icon {...props} icon="help-circle" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          
          <Divider />
          
          <List.Item
            title={t('me.privacy', 'Privacy Policy')}
            left={props => <List.Icon {...props} icon="shield-account" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
          
          <Divider />
          
          <List.Item
            title={t('me.terms', 'Terms of Service')}
            left={props => <List.Icon {...props} icon="file-document" />}
            right={props => <List.Icon {...props} icon="chevron-right" />}
            onPress={() => {}}
          />
        </Surface>

        {/* 登出按钮 */}
        <View style={styles.logoutSection}>
          <Button 
            mode="outlined" 
            onPress={handleLogout}
            style={styles.logoutButton}
            textColor={theme.colors.error}
          >
            {t('common.logout', 'Logout')}
          </Button>
        </View>

        {/* 版本信息 */}
        <View style={styles.versionInfo}>
          <Text variant="bodySmall" style={styles.versionText}>
            Tourna v1.0.0
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    margin: spacing.lg,
    padding: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    marginRight: spacing.md,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  userEmail: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  verifiedIcon: {
    margin: 0,
    marginRight: -spacing.xs,
  },
  verifiedText: {
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  editButton: {
    margin: 0,
  },
  guideModeSection: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  guideModeToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  guideModeInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  guideModeTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.xs,
  },
  guideModeDescription: {
    color: theme.custom.colors.textSecondary,
  },
  section: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    padding: spacing.lg,
    borderRadius: theme.custom.borderRadius.lg,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: theme.colors.onSurface,
  },
  balanceCards: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  balanceCard: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  balanceCardContent: {
    alignItems: 'center',
    padding: spacing.md,
  },
  balanceLabel: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
  },
  balanceAmount: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  pendingAmount: {
    fontWeight: 'bold',
    color: '#FF9800',
  },
  totalEarnings: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  totalEarningsLabel: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
  },
  totalEarningsAmount: {
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  withdrawButton: {
    borderRadius: theme.custom.borderRadius.md,
  },
  levelInfo: {
    alignItems: 'center',
  },
  levelBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  levelIcon: {
    margin: 0,
    marginRight: spacing.xs,
  },
  levelText: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  levelProgress: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: spacing.sm,
  },
  levelDescription: {
    color: theme.custom.colors.textSecondary,
    textAlign: 'center',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  statItem: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.custom.borderRadius.md,
  },
  statValue: {
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    color: theme.custom.colors.textSecondary,
    textAlign: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingIcon: {
    margin: 0,
    marginLeft: -spacing.xs,
  },
  logoutSection: {
    padding: spacing.lg,
  },
  logoutButton: {
    borderColor: theme.colors.error,
    borderRadius: theme.custom.borderRadius.md,
  },
  versionInfo: {
    alignItems: 'center',
    paddingBottom: spacing.xl,
  },
  versionText: {
    color: theme.custom.colors.textSecondary,
  },
});
