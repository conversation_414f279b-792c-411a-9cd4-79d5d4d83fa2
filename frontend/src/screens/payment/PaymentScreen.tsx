import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert, Platform } from 'react-native';
import { 
  Text, 
  Button, 
  Card,
  Surface,
  RadioButton,
  Divider,
  IconButton,
  Chip
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';
import { paymentService, PaymentMethod, PaymentRequest, PaymentResult } from '@/services/paymentService';
import { bookingService, BookingResponse } from '@/services/bookingService';

type PaymentScreenNavigationProp = StackNavigationProp<any, 'Payment'>;
type PaymentScreenRouteProp = RouteProp<any, 'Payment'>;

interface Props {
  navigation: PaymentScreenNavigationProp;
  route: PaymentScreenRouteProp;
}

export function PaymentScreen({ navigation, route }: Props) {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);
  
  // 从路由参数获取预订信息
  const { bookingId, amount, currency } = route.params as any;
  
  const [booking, setBooking] = useState<BookingResponse | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [applePayAvailable, setApplePayAvailable] = useState(false);
  const [googlePayAvailable, setGooglePayAvailable] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      const [bookingData, paymentMethodsData, applePaySupport, googlePaySupport] = await Promise.all([
        bookingService.getBookingDetails(bookingId),
        paymentService.getPaymentMethods(),
        paymentService.isApplePayAvailable(),
        paymentService.isGooglePayAvailable(),
      ]);
      
      setBooking(bookingData);
      setPaymentMethods(paymentMethodsData);
      setApplePayAvailable(applePaySupport && Platform.OS === 'ios');
      setGooglePayAvailable(googlePaySupport && Platform.OS === 'android');
      
      // 设置默认支付方式
      const defaultMethod = paymentMethodsData.find(method => method.isDefault);
      if (defaultMethod) {
        setSelectedPaymentMethod(defaultMethod.id);
      }
    } catch (error) {
      console.error('Failed to load payment data:', error);
      Alert.alert('Error', 'Failed to load payment information');
    } finally {
      setLoading(false);
    }
  };

  const handlePayment = async () => {
    if (!selectedPaymentMethod && !applePayAvailable && !googlePayAvailable) {
      Alert.alert('Error', 'Please select a payment method');
      return;
    }

    try {
      setProcessing(true);
      
      const paymentRequest: PaymentRequest = {
        bookingId,
        amount,
        currency,
        paymentMethodId: selectedPaymentMethod || undefined,
      };

      let result: PaymentResult;

      // 处理不同的支付方式
      if (selectedPaymentMethod === 'apple_pay') {
        result = await paymentService.processApplePayPayment(paymentRequest);
      } else if (selectedPaymentMethod === 'google_pay') {
        result = await paymentService.processGooglePayPayment(paymentRequest);
      } else {
        result = await paymentService.processPayment(paymentRequest);
      }

      if (result.success) {
        Alert.alert(
          'Payment Successful!',
          'Your payment has been processed successfully.',
          [
            {
              text: 'View Booking',
              onPress: () => navigation.navigate('BookingDetails', { bookingId }),
            },
          ]
        );
      } else {
        Alert.alert(
          'Payment Failed',
          result.error?.message || 'Payment could not be processed. Please try again.'
        );
      }
    } catch (error) {
      console.error('Payment failed:', error);
      Alert.alert('Payment Failed', 'An error occurred while processing your payment. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const handleAddPaymentMethod = () => {
    navigation.navigate('AddPaymentMethod', {
      onSuccess: () => {
        loadData(); // 重新加载支付方式
      },
    });
  };

  const getPaymentMethodIcon = (type: string) => {
    switch (type) {
      case 'card':
        return 'credit-card';
      case 'paypal':
        return 'paypal';
      case 'apple_pay':
        return 'apple';
      case 'google_pay':
        return 'google';
      default:
        return 'credit-card';
    }
  };

  const getCardBrandIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case 'visa':
        return 'credit-card';
      case 'mastercard':
        return 'credit-card';
      case 'amex':
        return 'credit-card';
      default:
        return 'credit-card';
    }
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <Card key={method.id} style={styles.paymentMethodCard} mode="outlined">
      <Card.Content>
        <View style={styles.paymentMethodContent}>
          <RadioButton
            value={method.id}
            status={selectedPaymentMethod === method.id ? 'checked' : 'unchecked'}
            onPress={() => setSelectedPaymentMethod(method.id)}
          />
          
          <IconButton
            icon={getPaymentMethodIcon(method.type)}
            size={24}
            style={styles.paymentIcon}
          />
          
          <View style={styles.paymentMethodInfo}>
            {method.type === 'card' && method.card && (
              <>
                <Text variant="bodyMedium" style={styles.paymentMethodTitle}>
                  {method.card.brand.toUpperCase()} •••• {method.card.last4}
                </Text>
                <Text variant="bodySmall" style={styles.paymentMethodSubtitle}>
                  {method.card.holderName} • {method.card.expiryMonth}/{method.card.expiryYear}
                </Text>
              </>
            )}
            
            {method.type === 'paypal' && method.paypal && (
              <>
                <Text variant="bodyMedium" style={styles.paymentMethodTitle}>
                  PayPal
                </Text>
                <Text variant="bodySmall" style={styles.paymentMethodSubtitle}>
                  {method.paypal.email}
                </Text>
              </>
            )}
          </View>
          
          {method.isDefault && (
            <Chip style={styles.defaultChip} textStyle={styles.defaultChipText}>
              Default
            </Chip>
          )}
        </View>
      </Card.Content>
    </Card>
  );

  const renderQuickPayOptions = () => (
    <View style={styles.quickPaySection}>
      <Text variant="titleMedium" style={styles.sectionTitle}>
        {t('payment.quickPay', 'Quick Pay')}
      </Text>
      
      <View style={styles.quickPayButtons}>
        {applePayAvailable && (
          <Button
            mode="outlined"
            onPress={() => setSelectedPaymentMethod('apple_pay')}
            style={[
              styles.quickPayButton,
              selectedPaymentMethod === 'apple_pay' && styles.selectedQuickPay
            ]}
            icon="apple"
          >
            Apple Pay
          </Button>
        )}
        
        {googlePayAvailable && (
          <Button
            mode="outlined"
            onPress={() => setSelectedPaymentMethod('google_pay')}
            style={[
              styles.quickPayButton,
              selectedPaymentMethod === 'google_pay' && styles.selectedQuickPay
            ]}
            icon="google"
          >
            Google Pay
          </Button>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading payment information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 预订摘要 */}
        {booking && (
          <Card style={styles.summaryCard} mode="elevated">
            <Card.Content>
              <Text variant="titleMedium" style={styles.summaryTitle}>
                {t('payment.orderSummary', 'Order Summary')}
              </Text>
              
              <View style={styles.summaryRow}>
                <Text variant="bodyMedium">{booking.serviceName}</Text>
                <Text variant="bodyMedium" style={styles.summaryValue}>
                  ${booking.totalPrice.toFixed(2)}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text variant="bodySmall" style={styles.summaryDetail}>
                  {new Date(booking.date).toLocaleDateString()} • {booking.startTime}
                </Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text variant="bodySmall" style={styles.summaryDetail}>
                  {booking.groupSize} {booking.groupSize === 1 ? 'person' : 'people'}
                </Text>
              </View>
              
              <Divider style={styles.divider} />
              
              <View style={styles.totalRow}>
                <Text variant="titleMedium">Total:</Text>
                <Text variant="titleMedium" style={styles.totalAmount}>
                  ${amount.toFixed(2)} {currency}
                </Text>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* 快速支付选项 */}
        {(applePayAvailable || googlePayAvailable) && renderQuickPayOptions()}

        {/* 支付方式 */}
        <View style={styles.paymentMethodsSection}>
          <View style={styles.sectionHeader}>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              {t('payment.paymentMethods', 'Payment Methods')}
            </Text>
            <Button mode="text" onPress={handleAddPaymentMethod}>
              {t('payment.addNew', 'Add New')}
            </Button>
          </View>
          
          {paymentMethods.map(renderPaymentMethod)}
          
          {paymentMethods.length === 0 && (
            <Card style={styles.emptyCard} mode="outlined">
              <Card.Content>
                <Text variant="bodyMedium" style={styles.emptyText}>
                  {t('payment.noPaymentMethods', 'No payment methods added yet')}
                </Text>
                <Button mode="contained" onPress={handleAddPaymentMethod} style={styles.addButton}>
                  {t('payment.addPaymentMethod', 'Add Payment Method')}
                </Button>
              </Card.Content>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* 底部支付按钮 */}
      <Surface style={styles.bottomBar} elevation={2}>
        <Button
          mode="contained"
          onPress={handlePayment}
          disabled={!selectedPaymentMethod || processing}
          loading={processing}
          style={styles.payButton}
        >
          {processing 
            ? t('payment.processing', 'Processing...')
            : t('payment.payNow', `Pay ${currency} ${amount.toFixed(2)}`)
          }
        </Button>
      </Surface>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  summaryCard: {
    margin: spacing.lg,
    backgroundColor: theme.colors.surface,
  },
  summaryTitle: {
    marginBottom: spacing.md,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  summaryValue: {
    fontWeight: '500',
  },
  summaryDetail: {
    color: theme.custom.colors.textSecondary,
  },
  divider: {
    marginVertical: spacing.md,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  totalAmount: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  quickPaySection: {
    margin: spacing.lg,
    marginTop: 0,
  },
  sectionTitle: {
    marginBottom: spacing.md,
    fontWeight: '600',
    color: theme.colors.onBackground,
  },
  quickPayButtons: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  quickPayButton: {
    flex: 1,
  },
  selectedQuickPay: {
    backgroundColor: theme.colors.primaryContainer,
  },
  paymentMethodsSection: {
    margin: spacing.lg,
    marginTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  paymentMethodCard: {
    marginBottom: spacing.md,
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    margin: 0,
    marginRight: spacing.sm,
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodTitle: {
    fontWeight: '500',
    color: theme.colors.onSurface,
  },
  paymentMethodSubtitle: {
    color: theme.custom.colors.textSecondary,
    marginTop: spacing.xs,
  },
  defaultChip: {
    backgroundColor: theme.colors.primaryContainer,
  },
  defaultChipText: {
    color: theme.colors.onPrimaryContainer,
    fontSize: 12,
  },
  emptyCard: {
    padding: spacing.lg,
    alignItems: 'center',
  },
  emptyText: {
    textAlign: 'center',
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.lg,
  },
  addButton: {
    borderRadius: theme.custom.borderRadius.md,
  },
  bottomBar: {
    backgroundColor: theme.colors.surface,
    padding: spacing.lg,
  },
  payButton: {
    borderRadius: theme.custom.borderRadius.md,
    paddingVertical: spacing.sm,
  },
});
