import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Button, Text, TextInput, Surface, IconButton } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

import { AuthStackParamList } from '@/navigation/AuthNavigator';
import { useAppDispatch, useAppSelector } from '@/store';
import { register } from '@/store/slices/authSlice';
import { theme, spacing } from '@/constants/theme';
import { SocialLoginButtons } from '@/components/auth/SocialLoginButtons';

type RegisterScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Register'>;

interface Props {
  navigation: RegisterScreenNavigationProp;
}

export function RegisterScreen({ navigation }: Props) {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector(state => state.auth);
  
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');

  const onSubmit = async () => {
    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    if (!username || !email || !password) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      const registerData = {
        username,
        email,
        password,
        firstName,
        lastName,
        phone,
      };

      await dispatch(register(registerData)).unwrap();
      // 注册成功后会自动导航到主应用
    } catch (error: any) {
      Alert.alert(
        t('auth.registerFailed', 'Registration Failed'),
        error.message || t('auth.registerError', 'An error occurred during registration')
      );
    }
  };

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* 头部 */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={handleGoBack}
            style={styles.backButton}
          />
          <Text variant="headlineMedium" style={styles.title}>
            {t('auth.createAccount', 'Create Account')}
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {t('auth.registerSubtitle', 'Join the global travel community')}
          </Text>
        </View>

        {/* 注册表单 */}
        <Surface style={styles.formContainer} elevation={1}>
          <View style={styles.form}>
            {/* 用户名输入 */}
            <TextInput
              label={t('auth.username', 'Username')}
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
              style={styles.input}
              left={<TextInput.Icon icon="account" />}
            />

            {/* 邮箱输入 */}
            <TextInput
              label={t('auth.email', 'Email')}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              style={styles.input}
              left={<TextInput.Icon icon="email" />}
            />

            {/* 姓名输入 */}
            <View style={styles.nameRow}>
              <TextInput
                label={t('auth.firstName', 'First Name')}
                value={firstName}
                onChangeText={setFirstName}
                style={[styles.input, styles.nameInput]}
                left={<TextInput.Icon icon="account-outline" />}
              />
              <TextInput
                label={t('auth.lastName', 'Last Name')}
                value={lastName}
                onChangeText={setLastName}
                style={[styles.input, styles.nameInput]}
              />
            </View>

            {/* 手机号输入 */}
            <TextInput
              label={t('auth.phone', 'Phone (Optional)')}
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              style={styles.input}
              left={<TextInput.Icon icon="phone" />}
            />

            {/* 密码输入 */}
            <TextInput
              label={t('auth.password', 'Password')}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoComplete="password"
              style={styles.input}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
            />

            {/* 确认密码输入 */}
            <TextInput
              label={t('auth.confirmPassword', 'Confirm Password')}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
              style={styles.input}
              left={<TextInput.Icon icon="lock-check" />}
              right={
                <TextInput.Icon
                  icon={showConfirmPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                />
              }
            />

            {/* 错误信息 */}
            {error && (
              <Text variant="bodySmall" style={styles.errorText}>
                {error}
              </Text>
            )}

            {/* 注册按钮 */}
            <Button
              mode="contained"
              onPress={onSubmit}
              loading={isLoading}
              disabled={!username || !email || !password || isLoading}
              style={styles.registerButton}
              contentStyle={styles.buttonContent}
            >
              {t('auth.register', 'Sign Up')}
            </Button>
          </View>
        </Surface>

        {/* 社交登录 */}
        <SocialLoginButtons
          onSuccess={() => {
            // 注册成功后会自动导航到主应用
          }}
          onError={(error) => {
            Alert.alert(
              t('auth.registerFailed', 'Registration Failed'),
              error
            );
          }}
        />

        {/* 登录链接 */}
        <View style={styles.loginSection}>
          <Text variant="bodyMedium" style={styles.loginText}>
            {t('auth.hasAccount', 'Already have an account?')}
          </Text>
          <Button mode="text" onPress={handleLogin}>
            {t('auth.login', 'Login')}
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing.lg,
  },
  header: {
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginLeft: -spacing.sm,
    marginBottom: spacing.md,
  },
  title: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: spacing.xs,
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
  },
  formContainer: {
    borderRadius: theme.custom.borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  form: {
    gap: spacing.md,
  },
  input: {
    backgroundColor: 'transparent',
  },
  nameRow: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  nameInput: {
    flex: 1,
  },
  errorText: {
    color: theme.colors.error,
    marginTop: -spacing.sm,
  },
  registerButton: {
    marginTop: spacing.md,
    borderRadius: theme.custom.borderRadius.md,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  loginSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: spacing.xl,
  },
  loginText: {
    color: theme.custom.colors.textSecondary,
  },
});
