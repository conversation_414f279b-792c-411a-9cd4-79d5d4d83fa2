import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Button, Text, Surface } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

import { AuthStackParamList } from '@/navigation/AuthNavigator';
import { theme, spacing, fontSizes } from '@/constants/theme';

type WelcomeScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Welcome'>;

interface Props {
  navigation: WelcomeScreenNavigationProp;
}

const { width, height } = Dimensions.get('window');

export function WelcomeScreen({ navigation }: Props) {
  const { t } = useTranslation();

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        {/* Logo和标题区域 */}
        <View style={styles.logoSection}>
          <Surface style={styles.logoContainer} elevation={2}>
            <Text variant="displayMedium" style={styles.logoText}>
              T
            </Text>
          </Surface>
          
          <Text variant="headlineLarge" style={styles.title}>
            {t('app.name', 'Tourna')}
          </Text>
          
          <Text variant="bodyLarge" style={styles.subtitle}>
            {t('welcome.subtitle', 'Connect with local guides worldwide')}
          </Text>
        </View>

        {/* 特性介绍 */}
        <View style={styles.featuresSection}>
          <View style={styles.feature}>
            <Text variant="titleMedium" style={styles.featureTitle}>
              🌍 {t('welcome.feature1.title', 'Global Network')}
            </Text>
            <Text variant="bodyMedium" style={styles.featureText}>
              {t('welcome.feature1.description', 'Find local guides in cities around the world')}
            </Text>
          </View>

          <View style={styles.feature}>
            <Text variant="titleMedium" style={styles.featureTitle}>
              💬 {t('welcome.feature2.title', 'Real-time Chat')}
            </Text>
            <Text variant="bodyMedium" style={styles.featureText}>
              {t('welcome.feature2.description', 'Communicate with guides using AI translation')}
            </Text>
          </View>

          <View style={styles.feature}>
            <Text variant="titleMedium" style={styles.featureTitle}>
              💰 {t('welcome.feature3.title', 'Secure Payments')}
            </Text>
            <Text variant="bodyMedium" style={styles.featureText}>
              {t('welcome.feature3.description', 'Pay securely with USDC cryptocurrency')}
            </Text>
          </View>
        </View>

        {/* 按钮区域 */}
        <View style={styles.buttonSection}>
          <Button
            mode="contained"
            onPress={handleLogin}
            style={styles.primaryButton}
            contentStyle={styles.buttonContent}
          >
            {t('auth.login', 'Login')}
          </Button>

          <Button
            mode="outlined"
            onPress={handleRegister}
            style={styles.secondaryButton}
            contentStyle={styles.buttonContent}
          >
            {t('auth.register', 'Sign Up')}
          </Button>

          <Text variant="bodySmall" style={styles.termsText}>
            {t('welcome.terms', 'By continuing, you agree to our Terms of Service and Privacy Policy')}
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    justifyContent: 'space-between',
  },
  logoSection: {
    alignItems: 'center',
    paddingTop: spacing.xxxl,
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.lg,
    backgroundColor: theme.colors.surface,
  },
  logoText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  title: {
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
    textAlign: 'center',
    paddingHorizontal: spacing.lg,
  },
  featuresSection: {
    paddingVertical: spacing.xl,
  },
  feature: {
    marginBottom: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  featureTitle: {
    fontWeight: '600',
    marginBottom: spacing.xs,
    color: theme.colors.onBackground,
  },
  featureText: {
    color: theme.custom.colors.textSecondary,
    lineHeight: 20,
  },
  buttonSection: {
    paddingBottom: spacing.xl,
  },
  primaryButton: {
    marginBottom: spacing.md,
    borderRadius: theme.custom.borderRadius.md,
  },
  secondaryButton: {
    marginBottom: spacing.lg,
    borderRadius: theme.custom.borderRadius.md,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  termsText: {
    textAlign: 'center',
    color: theme.custom.colors.textSecondary,
    paddingHorizontal: spacing.md,
    lineHeight: 18,
  },
});
