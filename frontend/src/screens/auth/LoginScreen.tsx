import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Button, Text, TextInput, Surface, IconButton } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
// import { useForm, Controller } from 'react-hook-form';

import { AuthStackParamList } from '@/navigation/AuthNavigator';
import { useAppDispatch, useAppSelector } from '@/store';
import { login } from '@/store/slices/authSlice';
import { theme, spacing } from '@/constants/theme';

type LoginScreenNavigationProp = StackNavigationProp<AuthStackParamList, 'Login'>;

interface Props {
  navigation: LoginScreenNavigationProp;
}

interface LoginFormData {
  email: string;
  password: string;
}

export function LoginScreen({ navigation }: Props) {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector(state => state.auth);

  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const onSubmit = async () => {
    try {
      await dispatch(login({ email, password })).unwrap();
      // 登录成功后会自动导航到主应用
    } catch (error: any) {
      Alert.alert(
        t('auth.loginFailed', 'Login Failed'),
        error.message || t('auth.loginError', 'An error occurred during login')
      );
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* 头部 */}
        <View style={styles.header}>
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={handleGoBack}
            style={styles.backButton}
          />
          <Text variant="headlineMedium" style={styles.title}>
            {t('auth.welcomeBack', 'Welcome Back')}
          </Text>
          <Text variant="bodyMedium" style={styles.subtitle}>
            {t('auth.loginSubtitle', 'Sign in to your account')}
          </Text>
        </View>

        {/* 登录表单 */}
        <Surface style={styles.formContainer} elevation={1}>
          <View style={styles.form}>
            {/* 邮箱输入 */}
            <TextInput
              label={t('auth.email', 'Email')}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              style={styles.input}
              left={<TextInput.Icon icon="email" />}
            />

            {/* 密码输入 */}
            <TextInput
              label={t('auth.password', 'Password')}
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoComplete="password"
              style={styles.input}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
            />

            {/* 忘记密码链接 */}
            <Button
              mode="text"
              onPress={handleForgotPassword}
              style={styles.forgotPasswordButton}
            >
              {t('auth.forgotPassword', 'Forgot Password?')}
            </Button>

            {/* 错误信息 */}
            {error && (
              <Text variant="bodySmall" style={styles.errorText}>
                {error}
              </Text>
            )}

            {/* 登录按钮 */}
            <Button
              mode="contained"
              onPress={onSubmit}
              loading={isLoading}
              disabled={!email || !password || isLoading}
              style={styles.loginButton}
              contentStyle={styles.buttonContent}
            >
              {t('auth.login', 'Login')}
            </Button>
          </View>
        </Surface>

        {/* 注册链接 */}
        <View style={styles.registerSection}>
          <Text variant="bodyMedium" style={styles.registerText}>
            {t('auth.noAccount', "Don't have an account?")}
          </Text>
          <Button mode="text" onPress={handleRegister}>
            {t('auth.signUp', 'Sign Up')}
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing.lg,
  },
  header: {
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  backButton: {
    alignSelf: 'flex-start',
    marginLeft: -spacing.sm,
    marginBottom: spacing.md,
  },
  title: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
    marginBottom: spacing.xs,
  },
  subtitle: {
    color: theme.custom.colors.textSecondary,
  },
  formContainer: {
    borderRadius: theme.custom.borderRadius.lg,
    padding: spacing.lg,
    marginBottom: spacing.xl,
  },
  form: {
    gap: spacing.md,
  },
  input: {
    backgroundColor: 'transparent',
  },
  errorText: {
    color: theme.colors.error,
    marginTop: -spacing.sm,
  },
  forgotPasswordButton: {
    alignSelf: 'flex-end',
    marginTop: -spacing.sm,
  },
  loginButton: {
    marginTop: spacing.md,
    borderRadius: theme.custom.borderRadius.md,
  },
  buttonContent: {
    paddingVertical: spacing.sm,
  },
  registerSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: spacing.xl,
  },
  registerText: {
    color: theme.custom.colors.textSecondary,
  },
});
