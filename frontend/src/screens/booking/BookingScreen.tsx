import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Button, 
  Card,
  Surface,
  TextInput,
  Chip,
  Divider,
  IconButton,
  SegmentedButtons
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';
import { bookingService, TimeSlot, BookingRequest, PriceCalculation } from '@/services/bookingService';
import { CachedImage } from '@/components/common/CachedImage';

type BookingScreenNavigationProp = StackNavigationProp<any, 'Booking'>;
type BookingScreenRouteProp = RouteProp<any, 'Booking'>;

interface Props {
  navigation: BookingScreenNavigationProp;
  route: BookingScreenRouteProp;
}

interface BookingStep {
  id: string;
  title: string;
  completed: boolean;
}

export function BookingScreen({ navigation, route }: Props) {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);
  
  // 从路由参数获取服务信息
  const { service, guide } = route.params as any;
  
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);
  const [groupSize, setGroupSize] = useState(1);
  const [availableSlots, setAvailableSlots] = useState<TimeSlot[]>([]);
  const [priceCalculation, setPriceCalculation] = useState<PriceCalculation | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // 表单数据
  const [contactInfo, setContactInfo] = useState({
    name: user?.firstName + ' ' + user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || '',
  });
  const [specialRequests, setSpecialRequests] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('English');
  const [dietaryRestrictions, setDietaryRestrictions] = useState<string[]>([]);

  const steps: BookingStep[] = [
    { id: 'datetime', title: t('booking.selectDateTime', 'Select Date & Time'), completed: false },
    { id: 'details', title: t('booking.bookingDetails', 'Booking Details'), completed: false },
    { id: 'contact', title: t('booking.contactInfo', 'Contact Information'), completed: false },
    { id: 'review', title: t('booking.reviewBooking', 'Review & Confirm'), completed: false },
  ];

  // 生成接下来7天的日期选项
  const getDateOptions = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        value: date.toISOString().split('T')[0],
        label: date.toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        }),
      });
    }
    
    return dates;
  };

  // 加载可用时间段
  const loadAvailableSlots = async (date: string) => {
    try {
      setLoading(true);
      const slots = await bookingService.getAvailableTimeSlots({
        serviceId: service.id,
        date,
      });
      setAvailableSlots(slots.filter(slot => slot.isAvailable));
    } catch (error) {
      Alert.alert('Error', 'Failed to load available time slots');
    } finally {
      setLoading(false);
    }
  };

  // 计算价格
  const calculatePrice = async () => {
    if (!selectedTimeSlot || !selectedDate) return;
    
    try {
      const calculation = await bookingService.calculatePrice(
        service.id,
        selectedTimeSlot.id,
        groupSize,
        selectedDate
      );
      setPriceCalculation(calculation);
    } catch (error) {
      console.error('Failed to calculate price:', error);
    }
  };

  // 监听日期变化
  useEffect(() => {
    if (selectedDate) {
      loadAvailableSlots(selectedDate);
    }
  }, [selectedDate]);

  // 监听时间段和人数变化
  useEffect(() => {
    if (selectedTimeSlot && selectedDate) {
      calculatePrice();
    }
  }, [selectedTimeSlot, groupSize, selectedDate]);

  // 提交预订
  const handleSubmitBooking = async () => {
    if (!selectedTimeSlot || !selectedDate || !priceCalculation) {
      Alert.alert('Error', 'Please complete all required fields');
      return;
    }

    try {
      setSubmitting(true);
      
      const bookingRequest: BookingRequest = {
        serviceId: service.id,
        guideId: guide.id,
        timeSlotId: selectedTimeSlot.id,
        date: selectedDate,
        groupSize,
        specialRequests: specialRequests || undefined,
        contactInfo,
        preferences: {
          language: selectedLanguage,
          dietaryRestrictions: dietaryRestrictions.length > 0 ? dietaryRestrictions : undefined,
        },
      };

      const booking = await bookingService.createBooking(bookingRequest);
      
      Alert.alert(
        'Booking Confirmed!',
        `Your booking ${booking.bookingNumber} has been created successfully.`,
        [
          {
            text: 'View Booking',
            onPress: () => navigation.navigate('BookingDetails', { bookingId: booking.id }),
          },
          {
            text: 'Go to My Bookings',
            onPress: () => navigation.navigate('MyBookings'),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Booking Failed', 'Failed to create booking. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStepIndicator = () => (
    <Surface style={styles.stepIndicator} elevation={1}>
      <View style={styles.stepsContainer}>
        {steps.map((step, index) => (
          <View key={step.id} style={styles.stepItem}>
            <View style={[
              styles.stepCircle,
              index <= currentStep && styles.stepCircleActive,
              index < currentStep && styles.stepCircleCompleted,
            ]}>
              <Text style={[
                styles.stepNumber,
                index <= currentStep && styles.stepNumberActive,
              ]}>
                {index + 1}
              </Text>
            </View>
            <Text style={[
              styles.stepTitle,
              index <= currentStep && styles.stepTitleActive,
            ]}>
              {step.title}
            </Text>
          </View>
        ))}
      </View>
    </Surface>
  );

  const renderDateTimeStep = () => (
    <View style={styles.stepContent}>
      <Text variant="titleMedium" style={styles.stepHeading}>
        {t('booking.selectDate', 'Select Date')}
      </Text>
      
      <View style={styles.dateGrid}>
        {getDateOptions().map((date) => (
          <Chip
            key={date.value}
            selected={selectedDate === date.value}
            onPress={() => setSelectedDate(date.value)}
            style={styles.dateChip}
          >
            {date.label}
          </Chip>
        ))}
      </View>

      {selectedDate && (
        <>
          <Text variant="titleMedium" style={styles.stepHeading}>
            {t('booking.selectTime', 'Select Time')}
          </Text>
          
          {loading ? (
            <Text>Loading available times...</Text>
          ) : availableSlots.length === 0 ? (
            <Text>No available times for this date</Text>
          ) : (
            <View style={styles.timeGrid}>
              {availableSlots.map((slot) => (
                <Chip
                  key={slot.id}
                  selected={selectedTimeSlot?.id === slot.id}
                  onPress={() => setSelectedTimeSlot(slot)}
                  style={styles.timeChip}
                >
                  {new Date(slot.startTime).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })} - ${slot.price}
                </Chip>
              ))}
            </View>
          )}
        </>
      )}

      {selectedTimeSlot && (
        <>
          <Text variant="titleMedium" style={styles.stepHeading}>
            {t('booking.groupSize', 'Group Size')}
          </Text>
          
          <View style={styles.groupSizeContainer}>
            <IconButton
              icon="minus"
              onPress={() => setGroupSize(Math.max(1, groupSize - 1))}
              disabled={groupSize <= 1}
            />
            <Text variant="titleLarge" style={styles.groupSizeText}>
              {groupSize}
            </Text>
            <IconButton
              icon="plus"
              onPress={() => setGroupSize(Math.min(selectedTimeSlot.maxGroupSize, groupSize + 1))}
              disabled={groupSize >= selectedTimeSlot.maxGroupSize}
            />
          </View>
          
          <Text variant="bodySmall" style={styles.maxGroupText}>
            Maximum {selectedTimeSlot.maxGroupSize} people
          </Text>
        </>
      )}
    </View>
  );

  const renderDetailsStep = () => (
    <View style={styles.stepContent}>
      <Text variant="titleMedium" style={styles.stepHeading}>
        {t('booking.language', 'Preferred Language')}
      </Text>
      
      <SegmentedButtons
        value={selectedLanguage}
        onValueChange={setSelectedLanguage}
        buttons={[
          { value: 'English', label: 'English' },
          { value: 'Spanish', label: 'Español' },
          { value: 'French', label: 'Français' },
        ]}
        style={styles.languageButtons}
      />

      <Text variant="titleMedium" style={styles.stepHeading}>
        {t('booking.specialRequests', 'Special Requests')}
      </Text>
      
      <TextInput
        mode="outlined"
        multiline
        numberOfLines={3}
        value={specialRequests}
        onChangeText={setSpecialRequests}
        placeholder={t('booking.specialRequestsPlaceholder', 'Any special requests or requirements...')}
        style={styles.textInput}
      />

      <Text variant="titleMedium" style={styles.stepHeading}>
        {t('booking.dietaryRestrictions', 'Dietary Restrictions')}
      </Text>
      
      <View style={styles.dietaryGrid}>
        {['Vegetarian', 'Vegan', 'Gluten-free', 'Halal', 'Kosher', 'No restrictions'].map((restriction) => (
          <Chip
            key={restriction}
            selected={dietaryRestrictions.includes(restriction)}
            onPress={() => {
              if (restriction === 'No restrictions') {
                setDietaryRestrictions([]);
              } else {
                setDietaryRestrictions(prev => 
                  prev.includes(restriction)
                    ? prev.filter(r => r !== restriction)
                    : [...prev.filter(r => r !== 'No restrictions'), restriction]
                );
              }
            }}
            style={styles.dietaryChip}
          >
            {restriction}
          </Chip>
        ))}
      </View>
    </View>
  );

  const renderContactStep = () => (
    <View style={styles.stepContent}>
      <Text variant="titleMedium" style={styles.stepHeading}>
        {t('booking.contactInformation', 'Contact Information')}
      </Text>

      <TextInput
        mode="outlined"
        label={t('booking.fullName', 'Full Name')}
        value={contactInfo.name}
        onChangeText={(text) => setContactInfo(prev => ({ ...prev, name: text }))}
        style={styles.textInput}
      />

      <TextInput
        mode="outlined"
        label={t('booking.email', 'Email')}
        value={contactInfo.email}
        onChangeText={(text) => setContactInfo(prev => ({ ...prev, email: text }))}
        keyboardType="email-address"
        style={styles.textInput}
      />

      <TextInput
        mode="outlined"
        label={t('booking.phone', 'Phone Number')}
        value={contactInfo.phone}
        onChangeText={(text) => setContactInfo(prev => ({ ...prev, phone: text }))}
        keyboardType="phone-pad"
        style={styles.textInput}
      />
    </View>
  );

  const renderReviewStep = () => (
    <View style={styles.stepContent}>
      <Text variant="titleMedium" style={styles.stepHeading}>
        {t('booking.bookingSummary', 'Booking Summary')}
      </Text>

      <Card style={styles.summaryCard} mode="elevated">
        <Card.Content>
          <View style={styles.serviceHeader}>
            <CachedImage
              source={{ uri: service.image }}
              style={styles.serviceImage}
            />
            <View style={styles.serviceInfo}>
              <Text variant="titleMedium">{service.title}</Text>
              <Text variant="bodyMedium" style={styles.guideName}>
                with {guide.name}
              </Text>
            </View>
          </View>

          <Divider style={styles.divider} />

          <View style={styles.summaryRow}>
            <Text variant="bodyMedium">Date:</Text>
            <Text variant="bodyMedium" style={styles.summaryValue}>
              {selectedDate && new Date(selectedDate).toLocaleDateString()}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text variant="bodyMedium">Time:</Text>
            <Text variant="bodyMedium" style={styles.summaryValue}>
              {selectedTimeSlot && new Date(selectedTimeSlot.startTime).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
              })} - {selectedTimeSlot && new Date(selectedTimeSlot.endTime).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text variant="bodyMedium">Group Size:</Text>
            <Text variant="bodyMedium" style={styles.summaryValue}>
              {groupSize} {groupSize === 1 ? 'person' : 'people'}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text variant="bodyMedium">Language:</Text>
            <Text variant="bodyMedium" style={styles.summaryValue}>
              {selectedLanguage}
            </Text>
          </View>

          {specialRequests && (
            <View style={styles.summaryRow}>
              <Text variant="bodyMedium">Special Requests:</Text>
              <Text variant="bodyMedium" style={styles.summaryValue}>
                {specialRequests}
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {priceCalculation && (
        <Card style={styles.priceCard} mode="elevated">
          <Card.Content>
            <Text variant="titleMedium" style={styles.priceTitle}>
              {t('booking.priceBreakdown', 'Price Breakdown')}
            </Text>

            {priceCalculation.breakdown.map((item, index) => (
              <View key={index} style={styles.priceRow}>
                <Text variant="bodyMedium">{item.label}:</Text>
                <Text variant="bodyMedium" style={styles.priceValue}>
                  ${item.amount.toFixed(2)}
                </Text>
              </View>
            ))}

            <Divider style={styles.divider} />

            <View style={styles.totalRow}>
              <Text variant="titleMedium">Total:</Text>
              <Text variant="titleMedium" style={styles.totalPrice}>
                ${priceCalculation.totalPrice.toFixed(2)} {priceCalculation.currency}
              </Text>
            </View>
          </Card.Content>
        </Card>
      )}
    </View>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderDateTimeStep();
      case 1:
        return renderDetailsStep();
      case 2:
        return renderContactStep();
      case 3:
        return renderReviewStep();
      default:
        return null;
    }
  };

  const canProceedToNextStep = () => {
    switch (currentStep) {
      case 0:
        return selectedDate && selectedTimeSlot && groupSize > 0;
      case 1:
        return selectedLanguage;
      case 2:
        return contactInfo.name && contactInfo.email && contactInfo.phone;
      case 3:
        return true;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSubmitBooking();
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {renderStepIndicator()}

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderStepContent()}
      </ScrollView>

      <Surface style={styles.bottomBar} elevation={2}>
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={handleBack}
            style={styles.backButton}
          >
            {currentStep === 0 ? t('common.cancel', 'Cancel') : t('common.back', 'Back')}
          </Button>

          <Button
            mode="contained"
            onPress={handleNext}
            disabled={!canProceedToNextStep()}
            loading={submitting}
            style={styles.nextButton}
          >
            {currentStep === steps.length - 1
              ? t('booking.confirmBooking', 'Confirm Booking')
              : t('common.next', 'Next')
            }
          </Button>
        </View>
      </Surface>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  stepIndicator: {
    backgroundColor: theme.colors.surface,
    paddingVertical: spacing.md,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
  },
  stepItem: {
    alignItems: 'center',
    flex: 1,
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  stepCircleActive: {
    backgroundColor: theme.colors.primary,
  },
  stepCircleCompleted: {
    backgroundColor: theme.colors.primary,
  },
  stepNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: theme.colors.onSurfaceVariant,
  },
  stepNumberActive: {
    color: theme.colors.onPrimary,
  },
  stepTitle: {
    fontSize: 12,
    textAlign: 'center',
    color: theme.colors.onSurfaceVariant,
  },
  stepTitleActive: {
    color: theme.colors.primary,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  stepContent: {
    padding: spacing.lg,
  },
  stepHeading: {
    marginBottom: spacing.md,
    marginTop: spacing.lg,
    fontWeight: '600',
    color: theme.colors.onBackground,
  },
  dateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.lg,
  },
  dateChip: {
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
    marginBottom: spacing.lg,
  },
  timeChip: {
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  groupSizeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: spacing.md,
  },
  groupSizeText: {
    marginHorizontal: spacing.lg,
    fontWeight: 'bold',
  },
  maxGroupText: {
    textAlign: 'center',
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.lg,
  },
  languageButtons: {
    marginBottom: spacing.lg,
  },
  textInput: {
    marginBottom: spacing.md,
  },
  dietaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  dietaryChip: {
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
  },
  summaryCard: {
    marginBottom: spacing.lg,
    backgroundColor: theme.colors.surface,
  },
  serviceHeader: {
    flexDirection: 'row',
    marginBottom: spacing.md,
  },
  serviceImage: {
    width: 60,
    height: 60,
    borderRadius: theme.custom.borderRadius.sm,
    marginRight: spacing.md,
  },
  serviceInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  guideName: {
    color: theme.custom.colors.textSecondary,
    marginTop: spacing.xs,
  },
  divider: {
    marginVertical: spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  summaryValue: {
    fontWeight: '500',
  },
  priceCard: {
    backgroundColor: theme.colors.surface,
  },
  priceTitle: {
    marginBottom: spacing.md,
    fontWeight: '600',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  priceValue: {
    fontWeight: '500',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.sm,
  },
  totalPrice: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  bottomBar: {
    backgroundColor: theme.colors.surface,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  backButton: {
    flex: 1,
  },
  nextButton: {
    flex: 2,
  },
});
