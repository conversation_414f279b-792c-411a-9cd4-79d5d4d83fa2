import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, RefreshControl, Alert } from 'react-native';
import { 
  Text, 
  Card,
  Button,
  Chip,
  IconButton,
  Searchbar,
  SegmentedButtons,
  FAB
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';
import { bookingService, BookingResponse, BookingFilters } from '@/services/bookingService';
import { CachedImage } from '@/components/common/CachedImage';

type MyBookingsScreenNavigationProp = StackNavigationProp<any, 'MyBookings'>;

interface Props {
  navigation: MyBookingsScreenNavigationProp;
}

export function MyBookingsScreen({ navigation }: Props) {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);
  
  const [bookings, setBookings] = useState<BookingResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const filterOptions = [
    { value: 'all', label: t('bookings.all', 'All') },
    { value: 'upcoming', label: t('bookings.upcoming', 'Upcoming') },
    { value: 'completed', label: t('bookings.completed', 'Completed') },
    { value: 'cancelled', label: t('bookings.cancelled', 'Cancelled') },
  ];

  useEffect(() => {
    loadBookings(true);
  }, [activeFilter]);

  const loadBookings = async (reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
        setPage(0);
      }

      const filters: BookingFilters = {
        page: reset ? 0 : page,
        size: 20,
      };

      // 根据过滤器设置状态
      switch (activeFilter) {
        case 'upcoming':
          filters.status = ['confirmed', 'pending'];
          filters.dateFrom = new Date().toISOString().split('T')[0];
          break;
        case 'completed':
          filters.status = ['completed'];
          break;
        case 'cancelled':
          filters.status = ['cancelled'];
          break;
      }

      const response = await bookingService.getUserBookings(filters);
      
      if (reset) {
        setBookings(response.bookings);
      } else {
        setBookings(prev => [...prev, ...response.bookings]);
      }
      
      setHasMore(response.bookings.length === filters.size);
      setPage(prev => reset ? 1 : prev + 1);
    } catch (error) {
      console.error('Failed to load bookings:', error);
      Alert.alert('Error', 'Failed to load bookings');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadBookings(true);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadBookings(false);
    }
  };

  const handleCancelBooking = (booking: BookingResponse) => {
    Alert.alert(
      t('bookings.cancelBooking', 'Cancel Booking'),
      t('bookings.cancelConfirmation', 'Are you sure you want to cancel this booking?'),
      [
        { text: t('common.no', 'No'), style: 'cancel' },
        {
          text: t('common.yes', 'Yes'),
          style: 'destructive',
          onPress: async () => {
            try {
              await bookingService.cancelBooking(booking.id);
              Alert.alert('Success', 'Booking cancelled successfully');
              loadBookings(true);
            } catch (error) {
              Alert.alert('Error', 'Failed to cancel booking');
            }
          },
        },
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.primary;
      case 'pending':
        return '#FFA500';
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return theme.colors.error;
      case 'in_progress':
        return '#2196F3';
      default:
        return theme.custom.colors.textSecondary;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed':
        return t('bookings.confirmed', 'Confirmed');
      case 'pending':
        return t('bookings.pending', 'Pending');
      case 'completed':
        return t('bookings.completed', 'Completed');
      case 'cancelled':
        return t('bookings.cancelled', 'Cancelled');
      case 'in_progress':
        return t('bookings.inProgress', 'In Progress');
      default:
        return status;
    }
  };

  const canCancelBooking = (booking: BookingResponse) => {
    const bookingDate = new Date(booking.date);
    const now = new Date();
    const hoursDiff = (bookingDate.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    return booking.status === 'confirmed' && hoursDiff > 24;
  };

  const filteredBookings = bookings.filter(booking =>
    booking.serviceName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    booking.guideName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    booking.bookingNumber.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderBookingCard = ({ item }: { item: BookingResponse }) => (
    <Card style={styles.bookingCard} mode="elevated">
      <Card.Content>
        <View style={styles.cardHeader}>
          <View style={styles.bookingInfo}>
            <Text variant="titleMedium" style={styles.serviceName}>
              {item.serviceName}
            </Text>
            <Text variant="bodySmall" style={styles.bookingNumber}>
              {item.bookingNumber}
            </Text>
          </View>
          <Chip 
            style={[styles.statusChip, { backgroundColor: getStatusColor(item.status) }]}
            textStyle={styles.statusText}
          >
            {getStatusLabel(item.status)}
          </Chip>
        </View>

        <View style={styles.cardContent}>
          <CachedImage
            source={{ uri: item.serviceImage }}
            style={styles.serviceImage}
          />
          
          <View style={styles.detailsContainer}>
            <View style={styles.detailRow}>
              <IconButton icon="account" size={16} style={styles.detailIcon} />
              <Text variant="bodyMedium">{item.guideName}</Text>
            </View>
            
            <View style={styles.detailRow}>
              <IconButton icon="calendar" size={16} style={styles.detailIcon} />
              <Text variant="bodyMedium">
                {new Date(item.date).toLocaleDateString()} at {item.startTime}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <IconButton icon="account-group" size={16} style={styles.detailIcon} />
              <Text variant="bodyMedium">
                {item.groupSize} {item.groupSize === 1 ? 'person' : 'people'}
              </Text>
            </View>
            
            <View style={styles.detailRow}>
              <IconButton icon="currency-usd" size={16} style={styles.detailIcon} />
              <Text variant="bodyMedium" style={styles.price}>
                ${item.totalPrice.toFixed(2)} {item.currency}
              </Text>
            </View>
          </View>
        </View>
      </Card.Content>
      
      <Card.Actions>
        <Button
          mode="outlined"
          onPress={() => navigation.navigate('BookingDetails', { bookingId: item.id })}
        >
          {t('bookings.viewDetails', 'View Details')}
        </Button>
        
        {canCancelBooking(item) && (
          <Button
            mode="text"
            textColor={theme.colors.error}
            onPress={() => handleCancelBooking(item)}
          >
            {t('bookings.cancel', 'Cancel')}
          </Button>
        )}
        
        {item.status === 'confirmed' && (
          <Button
            mode="contained"
            onPress={() => navigation.navigate('Chat', { 
              sessionId: item.guideId,
              guideName: item.guideName 
            })}
          >
            {t('bookings.contactGuide', 'Contact Guide')}
          </Button>
        )}
      </Card.Actions>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text variant="titleMedium" style={styles.emptyTitle}>
        {t('bookings.noBookings', 'No bookings found')}
      </Text>
      <Text variant="bodyMedium" style={styles.emptySubtitle}>
        {activeFilter === 'all' 
          ? t('bookings.noBookingsDescription', 'You haven\'t made any bookings yet')
          : t('bookings.noBookingsForFilter', `No ${activeFilter} bookings found`)
        }
      </Text>
      <Button
        mode="contained"
        onPress={() => navigation.navigate('Home')}
        style={styles.exploreButton}
      >
        {t('bookings.exploreServices', 'Explore Services')}
      </Button>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineMedium" style={styles.title}>
          {t('bookings.myBookings', 'My Bookings')}
        </Text>
        
        <Searchbar
          placeholder={t('bookings.searchPlaceholder', 'Search bookings...')}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />
        
        <SegmentedButtons
          value={activeFilter}
          onValueChange={setActiveFilter}
          buttons={filterOptions}
          style={styles.filterButtons}
        />
      </View>

      <FlatList
        data={filteredBookings}
        renderItem={renderBookingCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('Home')}
        label={t('bookings.newBooking', 'New Booking')}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: spacing.lg,
    backgroundColor: theme.colors.surface,
  },
  title: {
    marginBottom: spacing.md,
    fontWeight: 'bold',
    color: theme.colors.onBackground,
  },
  searchBar: {
    marginBottom: spacing.md,
  },
  filterButtons: {
    marginBottom: spacing.sm,
  },
  listContainer: {
    padding: spacing.lg,
    paddingBottom: 100, // Space for FAB
  },
  bookingCard: {
    marginBottom: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  bookingInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  serviceName: {
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  bookingNumber: {
    color: theme.custom.colors.textSecondary,
    marginTop: spacing.xs,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  statusText: {
    color: theme.colors.onPrimary,
    fontSize: 12,
    fontWeight: '500',
  },
  cardContent: {
    flexDirection: 'row',
  },
  serviceImage: {
    width: 80,
    height: 80,
    borderRadius: theme.custom.borderRadius.sm,
    marginRight: spacing.md,
  },
  detailsContainer: {
    flex: 1,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  detailIcon: {
    margin: 0,
    marginRight: spacing.xs,
  },
  price: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: spacing.sm,
    color: theme.colors.onBackground,
  },
  emptySubtitle: {
    textAlign: 'center',
    marginBottom: spacing.lg,
    color: theme.custom.colors.textSecondary,
  },
  exploreButton: {
    borderRadius: theme.custom.borderRadius.md,
  },
  fab: {
    position: 'absolute',
    margin: spacing.lg,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.primary,
  },
});
