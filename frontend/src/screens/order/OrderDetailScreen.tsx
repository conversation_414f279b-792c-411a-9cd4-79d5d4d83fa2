import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Avatar,
  Surface,
  IconButton,
  Chip,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

import { RootStackParamList } from '@/navigation/AppNavigator';
import { theme, spacing } from '@/constants/theme';

type OrderDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'OrderDetail'>;
type OrderDetailScreenRouteProp = RouteProp<RootStackParamList, 'OrderDetail'>;

interface Props {
  navigation: OrderDetailScreenNavigationProp;
  route: OrderDetailScreenRouteProp;
}

interface OrderDetail {
  id: number;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  bookingDate: string;
  serviceDate: string;
  serviceTime: string;
  guests: number;
  totalAmount: number;
  currency: string;
  service: {
    id: number;
    title: string;
    duration: string;
    category: string;
  };
  guide: {
    id: number;
    name: string;
    avatar: string;
    phone: string;
    email: string;
  };
  customer: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  meetingPoint: string;
  specialRequests?: string;
  cancellationPolicy: string;
}

export function OrderDetailScreen({ navigation, route }: Props) {
  const { t } = useTranslation();
  const { orderId } = route.params;
  
  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrderDetails();
  }, [orderId]);

  const loadOrderDetails = async () => {
    try {
      setLoading(true);
      // 模拟订单详情数据
      const mockOrder: OrderDetail = {
        id: orderId,
        status: 'confirmed',
        bookingDate: '2025-07-10T14:30:00Z',
        serviceDate: '2025-07-20',
        serviceTime: '10:00',
        guests: 2,
        totalAmount: 90.00,
        currency: 'EUR',
        service: {
          id: 1,
          title: 'Historic Barcelona Walking Tour',
          duration: '3 hours',
          category: 'Cultural Tours'
        },
        guide: {
          id: 1,
          name: 'Maria Rodriguez',
          avatar: 'https://via.placeholder.com/100',
          phone: '+34 123 456 789',
          email: '<EMAIL>'
        },
        customer: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '****** 567 8900'
        },
        meetingPoint: 'Plaça de Catalunya, next to the fountain',
        specialRequests: 'Please speak slowly, we are learning Spanish',
        cancellationPolicy: 'Free cancellation up to 24 hours before the tour starts'
      };
      
      setOrder(mockOrder);
    } catch (error) {
      Alert.alert('Error', 'Failed to load order details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.primary;
      case 'completed':
        return '#4CAF50';
      case 'cancelled':
        return theme.colors.error;
      case 'pending':
        return '#FF9800';
      default:
        return theme.colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      case 'pending':
        return 'Pending';
      default:
        return status;
    }
  };

  const handleContactGuide = () => {
    if (order) {
      navigation.navigate('Chat', { 
        sessionId: order.guide.id, 
        recipientName: order.guide.name 
      });
    }
  };

  const handleCancelOrder = () => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes, Cancel', 
          style: 'destructive',
          onPress: () => {
            // 实现取消逻辑
            Alert.alert('Booking Cancelled', 'Your booking has been cancelled successfully.');
          }
        }
      ]
    );
  };

  if (loading || !order) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="bodyLarge">Loading order details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 订单状态 */}
        <Surface style={styles.statusSection} elevation={2}>
          <View style={styles.statusHeader}>
            <Text variant="headlineSmall" style={styles.orderTitle}>
              Order #{order.id}
            </Text>
            <Chip 
              style={[styles.statusChip, { backgroundColor: getStatusColor(order.status) }]}
              textStyle={styles.statusText}
            >
              {getStatusText(order.status)}
            </Chip>
          </View>
          <Text variant="bodyMedium" style={styles.bookingDate}>
            Booked on {new Date(order.bookingDate).toLocaleDateString()}
          </Text>
        </Surface>

        {/* 服务信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Service Details
          </Text>
          <Text variant="titleMedium" style={styles.serviceTitle}>
            {order.service.title}
          </Text>
          <View style={styles.serviceDetails}>
            <View style={styles.detailRow}>
              <IconButton icon="calendar" size={20} />
              <Text variant="bodyMedium">
                {new Date(order.serviceDate).toLocaleDateString()} at {order.serviceTime}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <IconButton icon="clock-outline" size={20} />
              <Text variant="bodyMedium">Duration: {order.service.duration}</Text>
            </View>
            <View style={styles.detailRow}>
              <IconButton icon="account-group" size={20} />
              <Text variant="bodyMedium">{order.guests} guest{order.guests > 1 ? 's' : ''}</Text>
            </View>
            <View style={styles.detailRow}>
              <IconButton icon="map-marker" size={20} />
              <Text variant="bodyMedium">Meeting point: {order.meetingPoint}</Text>
            </View>
          </View>
        </Surface>

        {/* 导游信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Your Guide
          </Text>
          <View style={styles.guideContainer}>
            <Avatar.Image
              size={60}
              source={{ uri: order.guide.avatar }}
              style={styles.guideAvatar}
            />
            <View style={styles.guideInfo}>
              <Text variant="titleMedium" style={styles.guideName}>
                {order.guide.name}
              </Text>
              <Text variant="bodySmall" style={styles.guideContact}>
                📞 {order.guide.phone}
              </Text>
              <Text variant="bodySmall" style={styles.guideContact}>
                ✉️ {order.guide.email}
              </Text>
            </View>
            <Button 
              mode="outlined" 
              onPress={handleContactGuide}
              style={styles.contactButton}
            >
              Message
            </Button>
          </View>
        </Surface>

        {/* 客户信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Customer Information
          </Text>
          <Text variant="bodyMedium" style={styles.customerInfo}>
            👤 {order.customer.firstName} {order.customer.lastName}
          </Text>
          <Text variant="bodyMedium" style={styles.customerInfo}>
            ✉️ {order.customer.email}
          </Text>
          <Text variant="bodyMedium" style={styles.customerInfo}>
            📞 {order.customer.phone}
          </Text>
          {order.specialRequests && (
            <>
              <Text variant="titleMedium" style={styles.requestsTitle}>
                Special Requests
              </Text>
              <Text variant="bodyMedium" style={styles.specialRequests}>
                {order.specialRequests}
              </Text>
            </>
          )}
        </Surface>

        {/* 价格信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Payment Summary
          </Text>
          <View style={styles.priceRow}>
            <Text variant="bodyMedium">
              Service ({order.guests} guest{order.guests > 1 ? 's' : ''})
            </Text>
            <Text variant="bodyMedium">
              €{order.totalAmount.toFixed(2)}
            </Text>
          </View>
          <Divider style={styles.divider} />
          <View style={styles.totalRow}>
            <Text variant="titleLarge" style={styles.totalLabel}>
              Total Paid
            </Text>
            <Text variant="titleLarge" style={styles.totalAmount}>
              €{order.totalAmount.toFixed(2)}
            </Text>
          </View>
        </Surface>

        {/* 取消政策 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Cancellation Policy
          </Text>
          <Text variant="bodyMedium" style={styles.policy}>
            {order.cancellationPolicy}
          </Text>
        </Surface>

        {/* 操作按钮 */}
        {order.status === 'confirmed' && (
          <View style={styles.actionButtons}>
            <Button 
              mode="outlined" 
              onPress={handleCancelOrder}
              style={styles.cancelButton}
              textColor={theme.colors.error}
            >
              Cancel Booking
            </Button>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusSection: {
    padding: spacing.lg,
    margin: spacing.md,
    borderRadius: 12,
  },
  statusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  orderTitle: {
    fontWeight: 'bold',
  },
  statusChip: {
    borderRadius: 16,
  },
  statusText: {
    color: 'white',
    fontWeight: 'bold',
  },
  bookingDate: {
    color: theme.colors.onSurfaceVariant,
  },
  section: {
    margin: spacing.md,
    padding: spacing.lg,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  serviceTitle: {
    fontWeight: '600',
    marginBottom: spacing.md,
  },
  serviceDetails: {
    gap: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guideContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guideAvatar: {
    marginRight: spacing.md,
  },
  guideInfo: {
    flex: 1,
  },
  guideName: {
    fontWeight: '600',
    marginBottom: spacing.xs,
  },
  guideContact: {
    color: theme.colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  contactButton: {
    minWidth: 80,
  },
  customerInfo: {
    marginBottom: spacing.sm,
  },
  requestsTitle: {
    fontWeight: '600',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
  },
  specialRequests: {
    fontStyle: 'italic',
    color: theme.colors.onSurfaceVariant,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.sm,
  },
  divider: {
    marginVertical: spacing.md,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  totalLabel: {
    fontWeight: 'bold',
  },
  totalAmount: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  policy: {
    lineHeight: 20,
  },
  actionButtons: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  cancelButton: {
    width: '100%',
    borderColor: theme.colors.error,
  },
});
