import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, RefreshControl } from 'react-native';
import { 
  Text, 
  Card,
  IconButton,
  Button,
  Badge,
  Surface,
  Menu,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppSelector } from '@/store';
import { notificationService, NotificationData } from '@/services/notificationService';

type NotificationsScreenNavigationProp = StackNavigationProp<any, 'Notifications'>;

interface Props {
  navigation: NotificationsScreenNavigationProp;
}

export function NotificationsScreen({ navigation }: Props) {
  const { t } = useTranslation();
  const user = useAppSelector(state => state.auth.user);
  
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [menuVisible, setMenuVisible] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadNotifications(true);
  }, []);

  const loadNotifications = async (reset: boolean = false) => {
    try {
      if (reset) {
        setLoading(true);
        setPage(0);
      }

      const response = await notificationService.getNotificationHistory(
        reset ? 0 : page,
        20
      );
      
      if (reset) {
        setNotifications(response.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.notifications]);
      }
      
      setUnreadCount(response.unreadCount);
      setHasMore(response.notifications.length === 20);
      setPage(prev => reset ? 1 : prev + 1);
    } catch (error) {
      console.error('Failed to load notifications:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadNotifications(true);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadNotifications(false);
    }
  };

  const handleNotificationPress = async (notification: NotificationData) => {
    // 标记为已读
    if (!notification.isRead) {
      await notificationService.markNotificationAsRead(notification.id);
      setNotifications(prev => 
        prev.map(n => n.id === notification.id ? { ...n, isRead: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    }

    // 根据通知类型导航
    switch (notification.type) {
      case 'booking_confirmed':
      case 'booking_cancelled':
        if (notification.data?.bookingId) {
          navigation.navigate('BookingDetails', { 
            bookingId: notification.data.bookingId 
          });
        }
        break;
      case 'message_received':
        if (notification.data?.sessionId) {
          navigation.navigate('Chat', { 
            sessionId: notification.data.sessionId 
          });
        }
        break;
      case 'payment_success':
      case 'payment_failed':
        if (notification.data?.bookingId) {
          navigation.navigate('BookingDetails', { 
            bookingId: notification.data.bookingId 
          });
        }
        break;
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await notificationService.markAllNotificationsAsRead();
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);
      setMenuVisible(false);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'booking_confirmed':
        return 'check-circle';
      case 'booking_cancelled':
        return 'cancel';
      case 'message_received':
        return 'message';
      case 'payment_success':
        return 'credit-card-check';
      case 'payment_failed':
        return 'credit-card-remove';
      case 'guide_update':
        return 'account-circle';
      case 'reminder':
        return 'bell';
      default:
        return 'bell';
    }
  };

  const getNotificationIconColor = (type: string) => {
    switch (type) {
      case 'booking_confirmed':
      case 'payment_success':
        return theme.colors.primary;
      case 'booking_cancelled':
      case 'payment_failed':
        return theme.colors.error;
      case 'message_received':
        return '#2196F3';
      case 'guide_update':
        return '#FF9800';
      case 'reminder':
        return '#9C27B0';
      default:
        return theme.custom.colors.textSecondary;
    }
  };

  const formatNotificationTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const renderNotificationItem = ({ item }: { item: NotificationData }) => (
    <Card 
      style={[
        styles.notificationCard,
        !item.isRead && styles.unreadCard
      ]} 
      mode="elevated"
      onPress={() => handleNotificationPress(item)}
    >
      <Card.Content>
        <View style={styles.notificationContent}>
          <View style={styles.iconContainer}>
            <IconButton
              icon={getNotificationIcon(item.type)}
              iconColor={getNotificationIconColor(item.type)}
              size={24}
              style={styles.notificationIcon}
            />
            {!item.isRead && <Badge style={styles.unreadBadge} />}
          </View>
          
          <View style={styles.contentContainer}>
            <Text 
              variant="titleSmall" 
              style={[
                styles.notificationTitle,
                !item.isRead && styles.unreadTitle
              ]}
            >
              {item.title}
            </Text>
            <Text 
              variant="bodyMedium" 
              style={styles.notificationBody}
              numberOfLines={2}
            >
              {item.body}
            </Text>
            <Text variant="bodySmall" style={styles.notificationTime}>
              {formatNotificationTime(item.createdAt)}
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <IconButton
        icon="bell-outline"
        size={64}
        iconColor={theme.custom.colors.textSecondary}
      />
      <Text variant="titleMedium" style={styles.emptyTitle}>
        {t('notifications.noNotifications', 'No notifications')}
      </Text>
      <Text variant="bodyMedium" style={styles.emptySubtitle}>
        {t('notifications.noNotificationsDescription', 'You\'ll see notifications about your bookings and messages here')}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Surface style={styles.header} elevation={1}>
        <View style={styles.headerContent}>
          <Text variant="headlineMedium" style={styles.title}>
            {t('notifications.title', 'Notifications')}
          </Text>
          
          <View style={styles.headerActions}>
            {unreadCount > 0 && (
              <Badge style={styles.headerBadge}>{unreadCount}</Badge>
            )}
            
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  onPress={() => setMenuVisible(true)}
                />
              }
            >
              <Menu.Item
                onPress={handleMarkAllAsRead}
                title={t('notifications.markAllRead', 'Mark all as read')}
                leadingIcon="check-all"
                disabled={unreadCount === 0}
              />
              <Divider />
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  navigation.navigate('NotificationSettings');
                }}
                title={t('notifications.settings', 'Notification settings')}
                leadingIcon="cog"
              />
            </Menu>
          </View>
        </View>
      </Surface>

      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    backgroundColor: theme.colors.surface,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontWeight: 'bold',
    color: theme.colors.onBackground,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerBadge: {
    backgroundColor: theme.colors.error,
    color: theme.colors.onError,
    marginRight: spacing.sm,
  },
  listContainer: {
    padding: spacing.lg,
  },
  notificationCard: {
    marginBottom: spacing.md,
    backgroundColor: theme.colors.surface,
  },
  unreadCard: {
    backgroundColor: theme.colors.primaryContainer,
  },
  notificationContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    position: 'relative',
    marginRight: spacing.md,
  },
  notificationIcon: {
    margin: 0,
  },
  unreadBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: theme.colors.error,
    minWidth: 8,
    height: 8,
  },
  contentContainer: {
    flex: 1,
  },
  notificationTitle: {
    fontWeight: '500',
    color: theme.colors.onSurface,
    marginBottom: spacing.xs,
  },
  unreadTitle: {
    fontWeight: 'bold',
  },
  notificationBody: {
    color: theme.custom.colors.textSecondary,
    marginBottom: spacing.xs,
    lineHeight: 20,
  },
  notificationTime: {
    color: theme.custom.colors.textSecondary,
    fontSize: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  emptyTitle: {
    textAlign: 'center',
    marginBottom: spacing.sm,
    color: theme.colors.onBackground,
  },
  emptySubtitle: {
    textAlign: 'center',
    color: theme.custom.colors.textSecondary,
  },
});
