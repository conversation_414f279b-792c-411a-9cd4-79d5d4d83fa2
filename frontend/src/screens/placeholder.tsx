import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

// 通用占位符组件
function PlaceholderScreen({ title }: { title: string }) {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text variant="headlineMedium">{title}</Text>
        <Text variant="bodyMedium">Coming Soon...</Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
});

// 导出各种屏幕组件
export const ChatListScreen = () => <PlaceholderScreen title="Chat List" />;
export const ProfileScreen = () => <PlaceholderScreen title="Profile" />;
export const GuideDetailScreen = () => <PlaceholderScreen title="Guide Detail" />;
export const ProductDetailScreen = () => <PlaceholderScreen title="Product Detail" />;
export const BookingScreen = () => <PlaceholderScreen title="Booking" />;
export const ChatScreen = () => <PlaceholderScreen title="Chat" />;
export const OrderListScreen = () => <PlaceholderScreen title="Order List" />;
export const OrderDetailScreen = () => <PlaceholderScreen title="Order Detail" />;
export const RegisterScreen = () => <PlaceholderScreen title="Register" />;
export const ForgotPasswordScreen = () => <PlaceholderScreen title="Forgot Password" />;
export const VerificationScreen = () => <PlaceholderScreen title="Verification" />;
