import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  Avatar,
  Surface,
  IconButton,
  Chip,
  Divider
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';

import { RootStackParamList } from '@/navigation/AppNavigator';
import { theme, spacing } from '@/constants/theme';

type ProductDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ProductDetail'>;
type ProductDetailScreenRouteProp = RouteProp<RootStackParamList, 'ProductDetail'>;

interface Props {
  navigation: ProductDetailScreenNavigationProp;
  route: ProductDetailScreenRouteProp;
}

interface ProductDetail {
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  duration: string;
  category: string;
  rating: number;
  reviewCount: number;
  maxGroupSize: number;
  languages: string[];
  includes: string[];
  excludes: string[];
  meetingPoint: string;
  cancellationPolicy: string;
  guide: {
    id: number;
    name: string;
    avatar: string;
    rating: number;
    reviewCount: number;
    isVerified: boolean;
  };
  images: string[];
}

export function ProductDetailScreen({ navigation, route }: Props) {
  const { t } = useTranslation();
  const { productId } = route.params;
  
  const [product, setProduct] = useState<ProductDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProductDetails();
  }, [productId]);

  const loadProductDetails = async () => {
    try {
      setLoading(true);
      // 模拟产品详情数据
      const mockProduct: ProductDetail = {
        id: productId,
        title: 'Historic Barcelona Walking Tour',
        description: 'Discover the fascinating history of Barcelona\'s Gothic Quarter with an expert local guide. Walk through medieval streets, visit hidden courtyards, and learn about the city\'s Roman origins, medieval expansion, and modern transformation. This intimate tour is perfect for history lovers and first-time visitors alike.',
        price: 45.00,
        currency: 'EUR',
        duration: '3 hours',
        category: 'Cultural Tours',
        rating: 4.8,
        reviewCount: 89,
        maxGroupSize: 8,
        languages: ['English', 'Spanish'],
        includes: [
          'Professional local guide',
          'Small group experience (max 8 people)',
          'Historical insights and stories',
          'Photo opportunities at key locations'
        ],
        excludes: [
          'Food and drinks',
          'Transportation',
          'Museum entrance fees',
          'Gratuities'
        ],
        meetingPoint: 'Plaça de Catalunya, next to the fountain',
        cancellationPolicy: 'Free cancellation up to 24 hours before the tour starts',
        guide: {
          id: 1,
          name: 'Maria Rodriguez',
          avatar: 'https://via.placeholder.com/100',
          rating: 4.9,
          reviewCount: 127,
          isVerified: true
        },
        images: [
          'https://via.placeholder.com/400x300',
          'https://via.placeholder.com/400x300',
          'https://via.placeholder.com/400x300'
        ]
      };
      
      setProduct(mockProduct);
    } catch (error) {
      Alert.alert('Error', 'Failed to load product details');
    } finally {
      setLoading(false);
    }
  };

  const handleBookNow = () => {
    navigation.navigate('Booking', { productId });
  };

  const handleContactGuide = () => {
    if (product) {
      navigation.navigate('Chat', { 
        sessionId: product.guide.id, 
        recipientName: product.guide.name 
      });
    }
  };

  if (loading || !product) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text variant="bodyLarge">Loading service details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 产品头部信息 */}
        <Surface style={styles.header} elevation={2}>
          <Text variant="headlineSmall" style={styles.title}>
            {product.title}
          </Text>
          <View style={styles.ratingContainer}>
            <IconButton icon="star" size={20} iconColor="#FFD700" style={styles.starIcon} />
            <Text variant="bodyMedium" style={styles.rating}>
              {product.rating} ({product.reviewCount} reviews)
            </Text>
          </View>
          <Chip style={styles.categoryChip}>
            {product.category}
          </Chip>
        </Surface>

        {/* 价格和预订 */}
        <Surface style={styles.priceSection} elevation={1}>
          <View style={styles.priceContainer}>
            <Text variant="headlineMedium" style={styles.price}>
              €{product.price}
            </Text>
            <Text variant="bodyMedium" style={styles.priceLabel}>
              per person
            </Text>
          </View>
          <Button 
            mode="contained" 
            onPress={handleBookNow}
            style={styles.bookButton}
            contentStyle={styles.bookButtonContent}
          >
            Book Now
          </Button>
        </Surface>

        {/* 描述 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            About This Experience
          </Text>
          <Text variant="bodyMedium" style={styles.description}>
            {product.description}
          </Text>
        </Surface>

        {/* 详细信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Details
          </Text>
          <View style={styles.detailRow}>
            <IconButton icon="clock-outline" size={20} />
            <Text variant="bodyMedium">Duration: {product.duration}</Text>
          </View>
          <View style={styles.detailRow}>
            <IconButton icon="account-group" size={20} />
            <Text variant="bodyMedium">Max group size: {product.maxGroupSize} people</Text>
          </View>
          <View style={styles.detailRow}>
            <IconButton icon="translate" size={20} />
            <Text variant="bodyMedium">Languages: {product.languages.join(', ')}</Text>
          </View>
          <View style={styles.detailRow}>
            <IconButton icon="map-marker" size={20} />
            <Text variant="bodyMedium">Meeting point: {product.meetingPoint}</Text>
          </View>
        </Surface>

        {/* 包含和不包含 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            What's Included
          </Text>
          {product.includes.map((item, index) => (
            <View key={index} style={styles.includeItem}>
              <IconButton icon="check" size={16} iconColor={theme.colors.primary} />
              <Text variant="bodyMedium" style={styles.includeText}>
                {item}
              </Text>
            </View>
          ))}
          
          <Text variant="titleMedium" style={[styles.sectionTitle, { marginTop: spacing.lg }]}>
            What's Not Included
          </Text>
          {product.excludes.map((item, index) => (
            <View key={index} style={styles.includeItem}>
              <IconButton icon="close" size={16} iconColor={theme.colors.error} />
              <Text variant="bodyMedium" style={styles.includeText}>
                {item}
              </Text>
            </View>
          ))}
        </Surface>

        {/* 导游信息 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Your Guide
          </Text>
          <View style={styles.guideContainer}>
            <Avatar.Image
              size={60}
              source={{ uri: product.guide.avatar }}
              style={styles.guideAvatar}
            />
            <View style={styles.guideInfo}>
              <View style={styles.guideNameContainer}>
                <Text variant="titleMedium" style={styles.guideName}>
                  {product.guide.name}
                </Text>
                {product.guide.isVerified && (
                  <IconButton 
                    icon="check-decagram" 
                    size={16} 
                    iconColor={theme.colors.primary}
                  />
                )}
              </View>
              <View style={styles.guideRating}>
                <IconButton icon="star" size={16} iconColor="#FFD700" style={styles.starIcon} />
                <Text variant="bodySmall">
                  {product.guide.rating} ({product.guide.reviewCount} reviews)
                </Text>
              </View>
            </View>
            <Button 
              mode="outlined" 
              onPress={handleContactGuide}
              style={styles.contactButton}
            >
              Contact
            </Button>
          </View>
        </Surface>

        {/* 取消政策 */}
        <Surface style={styles.section} elevation={1}>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Cancellation Policy
          </Text>
          <Text variant="bodyMedium" style={styles.policy}>
            {product.cancellationPolicy}
          </Text>
        </Surface>

        {/* 底部预订按钮 */}
        <View style={styles.bottomButton}>
          <Button 
            mode="contained" 
            onPress={handleBookNow}
            style={styles.fixedBookButton}
            contentStyle={styles.fixedBookButtonContent}
          >
            Book This Experience - €{product.price}
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: spacing.lg,
    margin: spacing.md,
    borderRadius: 12,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  starIcon: {
    margin: 0,
    marginRight: spacing.xs,
  },
  rating: {
    color: theme.colors.onSurfaceVariant,
  },
  categoryChip: {
    alignSelf: 'flex-start',
  },
  priceSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    margin: spacing.md,
    borderRadius: 12,
  },
  priceContainer: {
    alignItems: 'flex-start',
  },
  price: {
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  priceLabel: {
    color: theme.colors.onSurfaceVariant,
  },
  bookButton: {
    minWidth: 120,
  },
  bookButtonContent: {
    paddingVertical: spacing.sm,
  },
  section: {
    margin: spacing.md,
    padding: spacing.lg,
    borderRadius: 12,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  description: {
    lineHeight: 22,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  includeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  includeText: {
    flex: 1,
    marginLeft: spacing.sm,
  },
  guideContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guideAvatar: {
    marginRight: spacing.md,
  },
  guideInfo: {
    flex: 1,
  },
  guideNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guideName: {
    fontWeight: '600',
  },
  guideRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  contactButton: {
    minWidth: 80,
  },
  policy: {
    lineHeight: 20,
  },
  bottomButton: {
    padding: spacing.lg,
    paddingBottom: spacing.xl,
  },
  fixedBookButton: {
    width: '100%',
  },
  fixedBookButtonContent: {
    paddingVertical: spacing.md,
  },
});
