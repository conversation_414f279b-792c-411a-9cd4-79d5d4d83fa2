/**
 * 用户相关类型定义
 */

export enum UserRole {
  CUSTOMER = 'CUSTOMER',
  GUIDE = 'GUIDE',
  ADMIN = 'ADMIN',
  SUPPORT = 'SUPPORT',
}

export enum KycStatus {
  PENDING = 'PENDING',
  IN_REVIEW = 'IN_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  REQUIRES_ADDITIONAL_INFO = 'REQUIRES_ADDITIONAL_INFO',
}

export interface User {
  id: number;
  username: string;
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  fullName: string;
  avatarUrl?: string;
  languagePreference: string;
  kycStatus: KycStatus;
  kycLevel: number;
  walletAddress?: string;
  isActive: boolean;
  roles: UserRole[];
  createdAt: string;
  updatedAt: string;
}

export interface UserCreateRequest {
  username: string;
  email: string;
  password: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  languagePreference?: string;
  walletAddress?: string;
}

export interface UserUpdateRequest {
  email?: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  languagePreference?: string;
  walletAddress?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface RegisterRequest extends UserCreateRequest {}

export interface RegisterResponse extends LoginResponse {}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}
