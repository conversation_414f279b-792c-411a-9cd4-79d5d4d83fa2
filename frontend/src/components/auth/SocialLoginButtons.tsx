import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { useTranslation } from 'react-i18next';

import { theme, spacing } from '@/constants/theme';
import { useAppDispatch, useAppSelector } from '@/store';
import { socialLogin } from '@/store/slices/authSlice';
import { isAppleSignInAvailable, isGoogleSignInAvailable } from '@/services/socialAuth';

interface Props {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function SocialLoginButtons({ onSuccess, onError }: Props) {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { isLoading } = useAppSelector(state => state.auth);

  const handleGoogleLogin = async () => {
    try {
      await dispatch(socialLogin('google')).unwrap();
      onSuccess?.();
    } catch (error: any) {
      onError?.(error.message || 'Google login failed');
    }
  };

  const handleAppleLogin = async () => {
    try {
      await dispatch(socialLogin('apple')).unwrap();
      onSuccess?.();
    } catch (error: any) {
      onError?.(error.message || 'Apple login failed');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.divider}>
        <View style={styles.dividerLine} />
        <Text variant="bodySmall" style={styles.dividerText}>
          {t('auth.orContinueWith', 'Or continue with')}
        </Text>
        <View style={styles.dividerLine} />
      </View>

      <View style={styles.socialButtons}>
        {/* Google登录按钮 */}
        {isGoogleSignInAvailable() && (
          <Button
            mode="outlined"
            onPress={handleGoogleLogin}
            disabled={isLoading}
            style={[styles.socialButton, styles.googleButton]}
            contentStyle={styles.socialButtonContent}
            labelStyle={styles.socialButtonLabel}
            icon={() => (
              <View style={styles.googleIcon}>
                <Text style={styles.googleIconText}>G</Text>
              </View>
            )}
          >
            {t('auth.continueWithGoogle', 'Continue with Google')}
          </Button>
        )}

        {/* Apple登录按钮 (仅iOS) */}
        {isAppleSignInAvailable() && (
          <Button
            mode="outlined"
            onPress={handleAppleLogin}
            disabled={isLoading}
            style={[styles.socialButton, styles.appleButton]}
            contentStyle={styles.socialButtonContent}
            labelStyle={[styles.socialButtonLabel, styles.appleButtonLabel]}
            icon={() => (
              <View style={styles.appleIcon}>
                <Text style={styles.appleIconText}></Text>
              </View>
            )}
          >
            {t('auth.continueWithApple', 'Continue with Apple')}
          </Button>
        )}
      </View>

      {/* 开发环境提示 */}
      {__DEV__ && (
        <View style={styles.devNotice}>
          <Text variant="bodySmall" style={styles.devNoticeText}>
            {t('auth.devModeNotice', 'Development mode: Using mock social login')}
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.outline,
  },
  dividerText: {
    marginHorizontal: spacing.md,
    color: theme.custom.colors.textSecondary,
  },
  socialButtons: {
    gap: spacing.md,
  },
  socialButton: {
    borderRadius: theme.custom.borderRadius.md,
    borderWidth: 1.5,
  },
  socialButtonContent: {
    paddingVertical: spacing.sm,
    flexDirection: 'row',
    alignItems: 'center',
  },
  socialButtonLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: spacing.sm,
  },
  googleButton: {
    borderColor: '#DB4437',
    backgroundColor: 'transparent',
  },
  googleIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#DB4437',
    justifyContent: 'center',
    alignItems: 'center',
  },
  googleIconText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  appleButton: {
    borderColor: Platform.OS === 'ios' ? '#000000' : theme.colors.outline,
    backgroundColor: Platform.OS === 'ios' ? '#000000' : 'transparent',
  },
  appleButtonLabel: {
    color: Platform.OS === 'ios' ? '#FFFFFF' : theme.colors.onSurface,
  },
  appleIcon: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: Platform.OS === 'ios' ? '#FFFFFF' : '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  appleIconText: {
    color: Platform.OS === 'ios' ? '#000000' : '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
  },
  devNotice: {
    marginTop: spacing.lg,
    padding: spacing.md,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: theme.custom.borderRadius.sm,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary,
  },
  devNoticeText: {
    color: theme.custom.colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});
