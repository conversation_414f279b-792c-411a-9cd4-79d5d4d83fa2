import React, { useState, useEffect } from 'react';
import { Image, ImageProps, View, StyleSheet, ActivityIndicator } from 'react-native';
import { Text } from 'react-native-paper';
import { imageCacheService } from '@/services/imageCacheService';
import { theme, spacing } from '@/constants/theme';

interface CachedImageProps extends Omit<ImageProps, 'source'> {
  source: { uri: string };
  placeholder?: React.ReactNode;
  errorComponent?: React.ReactNode;
  showLoading?: boolean;
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: any) => void;
}

export function CachedImage({
  source,
  placeholder,
  errorComponent,
  showLoading = true,
  onLoadStart,
  onLoadEnd,
  onError,
  style,
  ...props
}: CachedImageProps) {
  const [imageUri, setImageUri] = useState<string>(source.uri);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    loadImage();
  }, [source.uri]);

  const loadImage = async () => {
    try {
      setLoading(true);
      setError(false);
      onLoadStart?.();

      // 获取缓存的图片URI
      const cachedUri = await imageCacheService.getCachedImageUri(source.uri);
      setImageUri(cachedUri);
    } catch (err) {
      console.error('Failed to load cached image:', err);
      setError(true);
      onError?.(err);
    } finally {
      setLoading(false);
      onLoadEnd?.();
    }
  };

  const handleImageLoad = () => {
    setLoading(false);
    setError(false);
  };

  const handleImageError = (err: any) => {
    setLoading(false);
    setError(true);
    onError?.(err);
  };

  if (error) {
    return (
      <View style={[styles.container, style]}>
        {errorComponent || (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Failed to load image</Text>
          </View>
        )}
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {loading && showLoading && (
        <View style={styles.loadingContainer}>
          {placeholder || (
            <View style={styles.defaultPlaceholder}>
              <ActivityIndicator size="small" color={theme.colors.primary} />
            </View>
          )}
        </View>
      )}
      
      <Image
        {...props}
        source={{ uri: imageUri }}
        style={[style, loading && styles.hiddenImage]}
        onLoad={handleImageLoad}
        onError={handleImageError}
      />
    </View>
  );
}

// 预加载图片的Hook
export function useImagePreloader(uris: string[]) {
  const [preloaded, setPreloaded] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);

  const preloadImages = async (imageUris: string[]) => {
    setLoading(true);
    try {
      await imageCacheService.preloadImages(imageUris);
      setPreloaded(new Set(imageUris));
    } catch (error) {
      console.error('Failed to preload images:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (uris.length > 0) {
      preloadImages(uris);
    }
  }, [uris]);

  return { preloaded, loading, preloadImages };
}

// 图片缓存状态Hook
export function useImageCacheStatus(uri: string) {
  const [isCached, setIsCached] = useState<boolean>(false);
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    checkCacheStatus();
  }, [uri]);

  const checkCacheStatus = async () => {
    try {
      setChecking(true);
      const cached = await imageCacheService.isImageCached(uri);
      setIsCached(cached);
    } catch (error) {
      console.error('Failed to check cache status:', error);
      setIsCached(false);
    } finally {
      setChecking(false);
    }
  };

  return { isCached, checking, checkCacheStatus };
}

// 图片缓存管理Hook
export function useImageCacheManager() {
  const [stats, setStats] = useState(imageCacheService.getCacheStats());

  const refreshStats = () => {
    setStats(imageCacheService.getCacheStats());
  };

  const clearCache = async () => {
    try {
      await imageCacheService.clearCache();
      refreshStats();
    } catch (error) {
      console.error('Failed to clear image cache:', error);
    }
  };

  useEffect(() => {
    refreshStats();
  }, []);

  return {
    stats,
    refreshStats,
    clearCache,
  };
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceVariant,
    zIndex: 1,
  },
  defaultPlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.lg,
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.errorContainer,
    padding: spacing.md,
    borderRadius: theme.custom.borderRadius.sm,
  },
  errorText: {
    color: theme.colors.onErrorContainer,
    fontSize: 12,
    textAlign: 'center',
  },
  hiddenImage: {
    opacity: 0,
  },
});

// 高阶组件：为任何图片组件添加缓存功能
export function withImageCache<P extends { source: { uri: string } }>(
  WrappedComponent: React.ComponentType<P>
) {
  return React.forwardRef<any, P>((props, ref) => {
    const [cachedUri, setCachedUri] = useState<string>(props.source.uri);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      const loadCachedImage = async () => {
        try {
          setLoading(true);
          const cached = await imageCacheService.getCachedImageUri(props.source.uri);
          setCachedUri(cached);
        } catch (error) {
          console.error('Failed to load cached image:', error);
        } finally {
          setLoading(false);
        }
      };

      loadCachedImage();
    }, [props.source.uri]);

    return (
      <WrappedComponent
        {...props}
        ref={ref}
        source={{ uri: cachedUri }}
      />
    );
  });
}

// 预定义的缓存图片组件
export const CachedAvatar = withImageCache(Image);
export const CachedBackground = withImageCache(Image);
