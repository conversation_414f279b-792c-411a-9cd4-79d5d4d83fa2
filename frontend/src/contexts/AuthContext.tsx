import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { loadUserProfile } from '@/store/slices/authSlice';
import { storageService } from '@/services/storageService';

interface AuthContextType {
  // 认证上下文类型定义
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(state => state.auth);

  useEffect(() => {
    // 应用启动时检查本地存储的认证信息
    const checkAuthStatus = async () => {
      const token = await storageService.getItem('auth_token');
      if (token && !isAuthenticated) {
        // 尝试加载用户信息
        dispatch(loadUserProfile());
      }
    };

    checkAuthStatus();
  }, [dispatch, isAuthenticated]);

  return (
    <AuthContext.Provider value={{}}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
