import React, { ReactNode } from 'react';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 初始化i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: {
          app: {
            name: '<PERSON><PERSON>',
          },
          navigation: {
            home: 'Home',
            search: 'Search',
            orders: 'Orders',
            chats: 'Chats',
            profile: 'Profile',
          },
          auth: {
            login: 'Login',
            register: 'Sign Up',
            email: 'Email',
            password: 'Password',
            welcomeBack: 'Welcome Back',
            loginSubtitle: 'Sign in to your account',
            forgotPassword: 'Forgot Password?',
            noAccount: "Don't have an account?",
            signUp: 'Sign Up',
            loginFailed: 'Login Failed',
            loginError: 'An error occurred during login',
          },
          welcome: {
            subtitle: 'Connect with local guides worldwide',
            feature1: {
              title: 'Global Network',
              description: 'Find local guides in cities around the world',
            },
            feature2: {
              title: 'Real-time Chat',
              description: 'Communicate with guides using AI translation',
            },
            feature3: {
              title: 'Secure Payments',
              description: 'Pay securely with USDC cryptocurrency',
            },
            terms: 'By continuing, you agree to our Terms of Service and Privacy Policy',
          },
          validation: {
            emailRequired: 'Email is required',
            emailInvalid: 'Invalid email address',
            passwordRequired: 'Password is required',
            passwordMinLength: 'Password must be at least 6 characters',
          },
        },
      },
      zh: {
        translation: {
          app: {
            name: 'Tourna',
          },
          navigation: {
            home: '首页',
            search: '搜索',
            orders: '订单',
            chats: '聊天',
            profile: '我的',
          },
          auth: {
            login: '登录',
            register: '注册',
            email: '邮箱',
            password: '密码',
            welcomeBack: '欢迎回来',
            loginSubtitle: '登录您的账户',
            forgotPassword: '忘记密码？',
            noAccount: '还没有账户？',
            signUp: '注册',
            loginFailed: '登录失败',
            loginError: '登录过程中发生错误',
          },
          welcome: {
            subtitle: '连接全球当地导游',
            feature1: {
              title: '全球网络',
              description: '在世界各地城市找到当地导游',
            },
            feature2: {
              title: '实时聊天',
              description: '使用AI翻译与导游沟通',
            },
            feature3: {
              title: '安全支付',
              description: '使用USDC加密货币安全支付',
            },
            terms: '继续使用即表示您同意我们的服务条款和隐私政策',
          },
          validation: {
            emailRequired: '邮箱不能为空',
            emailInvalid: '邮箱格式不正确',
            passwordRequired: '密码不能为空',
            passwordMinLength: '密码至少需要6个字符',
          },
        },
      },
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

interface I18nProviderProps {
  children: ReactNode;
}

export function I18nProvider({ children }: I18nProviderProps) {
  return <>{children}</>;
}
