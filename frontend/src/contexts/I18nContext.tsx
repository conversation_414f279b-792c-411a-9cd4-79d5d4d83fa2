import React, { ReactNode } from 'react';
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// 初始化i18n
i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: {
          app: {
            name: '<PERSON><PERSON>',
          },
          navigation: {
            home: 'Home',
            discover: 'Discover',
            publish: 'Publish',
            chat: 'Chat',
            me: 'Me',
          },
          auth: {
            login: 'Login',
            register: 'Sign Up',
            email: 'Email',
            password: 'Password',
            username: '<PERSON>rna<PERSON>',
            firstName: 'First Name',
            lastName: 'Last Name',
            phone: 'Phone',
            confirmPassword: 'Confirm Password',
            welcomeBack: 'Welcome Back',
            loginSubtitle: 'Sign in to your account',
            createAccount: 'Create Account',
            registerSubtitle: 'Join the global travel community',
            forgotPassword: 'Forgot Password?',
            noAccount: "Don't have an account?",
            hasAccount: 'Already have an account?',
            signUp: 'Sign Up',
            loginFailed: 'Login Failed',
            loginError: 'An error occurred during login',
            registerFailed: 'Registration Failed',
            registerError: 'An error occurred during registration',
          },
          welcome: {
            subtitle: 'Connect with local guides worldwide',
            feature1: {
              title: 'Global Network',
              description: 'Find local guides in cities around the world',
            },
            feature2: {
              title: 'Real-time Chat',
              description: 'Communicate with guides using AI translation',
            },
            feature3: {
              title: 'Secure Payments',
              description: 'Pay securely with USDC cryptocurrency',
            },
            terms: 'By continuing, you agree to our Terms of Service and Privacy Policy',
          },
          validation: {
            emailRequired: 'Email is required',
            emailInvalid: 'Invalid email address',
            passwordRequired: 'Password is required',
            passwordMinLength: 'Password must be at least 6 characters',
            usernameRequired: 'Username is required',
            usernameMinLength: 'Username must be at least 3 characters',
            usernameMaxLength: 'Username must be less than 50 characters',
            confirmPasswordRequired: 'Please confirm your password',
            passwordMismatch: 'Passwords do not match',
          },
          common: {
            loading: 'Loading...',
            error: 'Error',
            success: 'Success',
            cancel: 'Cancel',
            confirm: 'Confirm',
            save: 'Save',
            delete: 'Delete',
            edit: 'Edit',
            back: 'Back',
            next: 'Next',
            done: 'Done',
            retry: 'Retry',
            seeAll: 'See All',
            allow: 'Allow',
            logout: 'Logout',
          },
          home: {
            greeting: 'Hello',
            subtitle: 'Discover amazing experiences with local guides',
            searchPlaceholder: 'Search tours, activities, guides...',
            filterByCity: 'Filter by City',
            recommended: 'Recommended for You',
            popular: 'Popular This Week',
            allServices: 'All Services',
          },
          discover: {
            subtitle: 'Find local guides near you',
            specialties: 'Specialties',
            maxDistance: 'Max Distance',
            onlineOnly: 'Online Only',
            verifiedOnly: 'Verified Only',
            languages: 'Languages',
            popularServices: 'Popular Services',
            viewProfile: 'View Profile',
            contact: 'Contact',
            locationPermission: 'Location Permission',
            locationMessage: 'Allow access to your location to find nearby guides?',
            mapView: 'Map View',
            mapViewSoon: 'Map view coming soon!',
          },
          publish: {
            title: 'Publish Your Service',
            subtitle: 'Share your expertise with travelers',
            basicInfo: 'Basic Information',
            serviceTitle: 'Service Title',
            description: 'Description',
            category: 'Category',
            pricingDuration: 'Pricing & Duration',
            price: 'Price',
            maxGroupSize: 'Max Group Size',
            duration: 'Duration',
            languages: 'Languages',
            languagesDescription: 'Select languages you can conduct tours in',
            includes: 'What\'s Included',
            transport: 'Transportation',
            meals: 'Meals/Refreshments',
            tickets: 'Entry Tickets',
            meetingPolicy: 'Meeting Point & Policies',
            meetingPoint: 'Meeting Point',
            cancellationPolicy: 'Cancellation Policy',
            publishSettings: 'Publish Settings',
            activeService: 'Active Service',
            activeDescription: 'Make this service visible to travelers',
            publishService: 'Publish Service',
          },
          chat: {
            conversations: 'Conversations',
            online: 'Online',
            offline: 'Offline',
            isTyping: 'is typing...',
            typeMessage: 'Type a message...',
            bookNow: 'Book Now',
          },
          me: {
            logoutConfirm: 'Logout',
            logoutMessage: 'Are you sure you want to logout?',
            withdraw: 'Withdraw Funds',
            withdrawMessage: 'Available balance',
            withdrawConfirm: 'Withdraw',
            withdrawSuccess: 'Withdrawal request submitted',
            verifiedGuide: 'Verified Guide',
            guideMode: 'Guide Mode',
            guideModeDescription: 'Switch between traveler and guide features',
            financialOverview: 'Financial Overview',
            availableBalance: 'Available Balance',
            pendingBalance: 'Pending',
            totalEarnings: 'Total Earnings',
            guideLevel: 'Guide Level',
            levelDescription: 'Complete more tours to reach the next level',
            guideStats: 'Guide Statistics',
            completedTours: 'Completed Tours',
            averageRating: 'Average Rating',
            responseRate: 'Response Rate',
            totalBookings: 'Total Bookings',
            settings: 'Settings',
            notifications: 'Notifications',
            notificationsDescription: 'Receive booking and message alerts',
            paymentMethods: 'Payment Methods',
            paymentDescription: 'Manage your payment options',
            language: 'Language',
            languageDescription: 'Change app language',
            help: 'Help & Support',
            helpDescription: 'Get help and contact support',
            privacy: 'Privacy Policy',
            terms: 'Terms of Service',
          },
        },
      },
      zh: {
        translation: {
          app: {
            name: 'Tourna',
          },
          navigation: {
            home: '首页',
            search: '搜索',
            orders: '订单',
            chats: '聊天',
            profile: '我的',
          },
          auth: {
            login: '登录',
            register: '注册',
            email: '邮箱',
            password: '密码',
            welcomeBack: '欢迎回来',
            loginSubtitle: '登录您的账户',
            forgotPassword: '忘记密码？',
            noAccount: '还没有账户？',
            signUp: '注册',
            loginFailed: '登录失败',
            loginError: '登录过程中发生错误',
          },
          welcome: {
            subtitle: '连接全球当地导游',
            feature1: {
              title: '全球网络',
              description: '在世界各地城市找到当地导游',
            },
            feature2: {
              title: '实时聊天',
              description: '使用AI翻译与导游沟通',
            },
            feature3: {
              title: '安全支付',
              description: '使用USDC加密货币安全支付',
            },
            terms: '继续使用即表示您同意我们的服务条款和隐私政策',
          },
          validation: {
            emailRequired: '邮箱不能为空',
            emailInvalid: '邮箱格式不正确',
            passwordRequired: '密码不能为空',
            passwordMinLength: '密码至少需要6个字符',
          },
        },
      },
    },
    lng: 'en',
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
  });

interface I18nProviderProps {
  children: ReactNode;
}

export function I18nProvider({ children }: I18nProviderProps) {
  return <>{children}</>;
}
