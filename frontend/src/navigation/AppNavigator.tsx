import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

import { useAppSelector } from '@/store';
import { AuthNavigator } from './AuthNavigator';
import { HomeScreen } from '@/screens/main/HomeScreen';
import { DiscoverScreen } from '@/screens/main/DiscoverScreen';
import { PublishScreen } from '@/screens/main/PublishScreen';
import { ChatScreen } from '@/screens/main/ChatScreen';
import { MeScreen } from '@/screens/main/MeScreen';
// 导入真实的屏幕组件
import { GuideDetailScreen } from '@/screens/guide/GuideDetailScreen';
import { ProductDetailScreen } from '@/screens/product/ProductDetailScreen';
import { BookingScreen } from '@/screens/booking/BookingScreen';
import { OrderDetailScreen } from '@/screens/order/OrderDetailScreen';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  GuideDetail: { guideId: number };
  ProductDetail: { productId: number };
  Booking: { productId: number };
  Chat: { sessionId: number; recipientName: string };
  OrderDetail: { orderId: number };
};

export type MainTabParamList = {
  Home: undefined;
  Discover: undefined;
  Publish: undefined;
  Chat: undefined;
  Me: undefined;
};

const RootStack = createStackNavigator<RootStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();

/**
 * 主标签导航器
 */
function MainTabNavigator() {
  const { t } = useTranslation();

  return (
    <MainTab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = focused ? 'home' : 'home-outline';
              break;
            case 'Discover':
              iconName = focused ? 'compass' : 'compass-outline';
              break;
            case 'Publish':
              iconName = focused ? 'add-circle' : 'add-circle-outline';
              break;
            case 'Chat':
              iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
              break;
            case 'Me':
              iconName = focused ? 'person' : 'person-outline';
              break;
            default:
              iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#2196F3',
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <MainTab.Screen
        name="Home"
        component={HomeScreen}
        options={{ tabBarLabel: t('navigation.home', 'Home') }}
      />
      <MainTab.Screen
        name="Discover"
        component={DiscoverScreen}
        options={{ tabBarLabel: t('navigation.discover', 'Discover') }}
      />
      <MainTab.Screen
        name="Publish"
        component={PublishScreen}
        options={{ tabBarLabel: t('navigation.publish', 'Publish') }}
      />
      <MainTab.Screen
        name="Chat"
        component={ChatScreen}
        options={{ tabBarLabel: t('navigation.chat', 'Chat') }}
      />
      <MainTab.Screen
        name="Me"
        component={MeScreen}
        options={{ tabBarLabel: t('navigation.me', 'Me') }}
      />
    </MainTab.Navigator>
  );
}

/**
 * 应用主导航器
 */
export function AppNavigator() {
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated);

  return (
    <RootStack.Navigator screenOptions={{ headerShown: false }}>
      {!isAuthenticated ? (
        // 未认证用户显示认证流程
        <RootStack.Screen name="Auth" component={AuthNavigator} />
      ) : (
        // 已认证用户显示主应用
        <>
          <RootStack.Screen name="Main" component={MainTabNavigator} />
          <RootStack.Screen 
            name="GuideDetail" 
            component={GuideDetailScreen}
            options={{ 
              headerShown: true,
              title: 'Guide Details',
              presentation: 'modal'
            }}
          />
          <RootStack.Screen 
            name="ProductDetail" 
            component={ProductDetailScreen}
            options={{ 
              headerShown: true,
              title: 'Service Details',
              presentation: 'modal'
            }}
          />
          <RootStack.Screen 
            name="Booking" 
            component={BookingScreen}
            options={{ 
              headerShown: true,
              title: 'Book Service',
              presentation: 'modal'
            }}
          />
          <RootStack.Screen 
            name="Chat" 
            component={ChatScreen}
            options={({ route }) => ({ 
              headerShown: true,
              title: route.params.recipientName,
            })}
          />
          <RootStack.Screen 
            name="OrderDetail" 
            component={OrderDetailScreen}
            options={{ 
              headerShown: true,
              title: 'Order Details',
            }}
          />
        </>
      )}
    </RootStack.Navigator>
  );
}
