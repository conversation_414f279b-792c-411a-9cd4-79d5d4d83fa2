import { storageService } from './storageService';

export interface WebSocketMessage {
  type: 'message' | 'notification' | 'booking_update' | 'user_status' | 'typing' | 'heartbeat';
  data: any;
  timestamp: string;
  userId?: number;
  sessionId?: number;
}

export interface ConnectionStatus {
  connected: boolean;
  reconnecting: boolean;
  lastConnected?: Date;
  reconnectAttempts: number;
}

type MessageHandler = (message: WebSocketMessage) => void;
type StatusHandler = (status: ConnectionStatus) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private token: string | null = null;
  private messageHandlers: Map<string, MessageHandler[]> = new Map();
  private statusHandlers: StatusHandler[] = [];
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private status: ConnectionStatus = {
    connected: false,
    reconnecting: false,
    reconnectAttempts: 0,
  };
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 初始重连延迟（毫秒）
  private heartbeatInterval = 30000; // 心跳间隔（毫秒）

  constructor() {
    this.url = process.env.EXPO_PUBLIC_WS_URL || 'ws://localhost:3000/ws';
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    try {
      // 获取认证token
      this.token = await storageService.getToken();
      if (!this.token) {
        throw new Error('No authentication token available');
      }

      // 如果已经连接，先断开
      if (this.ws) {
        this.disconnect();
      }

      // 创建WebSocket连接
      const wsUrl = `${this.url}?token=${this.token}`;
      this.ws = new WebSocket(wsUrl);

      // 设置事件监听器
      this.setupEventListeners();

      return new Promise((resolve, reject) => {
        if (!this.ws) {
          reject(new Error('WebSocket not initialized'));
          return;
        }

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.status = {
            connected: true,
            reconnecting: false,
            lastConnected: new Date(),
            reconnectAttempts: 0,
          };
          this.notifyStatusHandlers();
          this.startHeartbeat();
          resolve();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket connection error:', error);
          reject(error);
        };
      });
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      throw error;
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.status = {
      connected: false,
      reconnecting: false,
      reconnectAttempts: 0,
    };
    this.notifyStatusHandlers();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      this.status.connected = false;
      this.notifyStatusHandlers();

      // 如果不是主动断开，尝试重连
      if (event.code !== 1000 && this.status.reconnectAttempts < this.maxReconnectAttempts) {
        this.attemptReconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  /**
   * 处理收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    console.log('WebSocket message received:', message);

    // 处理心跳响应
    if (message.type === 'heartbeat') {
      return;
    }

    // 通知相应的消息处理器
    const handlers = this.messageHandlers.get(message.type) || [];
    handlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });

    // 通知所有消息处理器
    const allHandlers = this.messageHandlers.get('*') || [];
    allHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in global message handler:', error);
      }
    });
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.status.reconnecting) return;

    this.status.reconnecting = true;
    this.status.reconnectAttempts++;
    this.notifyStatusHandlers();

    const delay = this.reconnectDelay * Math.pow(2, this.status.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.status.reconnectAttempts})`);

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('Reconnection failed:', error);
        this.status.reconnecting = false;
        
        if (this.status.reconnectAttempts < this.maxReconnectAttempts) {
          this.attemptReconnect();
        } else {
          console.error('Max reconnection attempts reached');
          this.notifyStatusHandlers();
        }
      }
    }, delay);
  }

  /**
   * 发送消息
   */
  send(message: Omit<WebSocketMessage, 'timestamp'>): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not connected, cannot send message');
      return false;
    }

    try {
      const fullMessage: WebSocketMessage = {
        ...message,
        timestamp: new Date().toISOString(),
      };

      this.ws.send(JSON.stringify(fullMessage));
      return true;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      return false;
    }
  }

  /**
   * 发送聊天消息
   */
  sendChatMessage(sessionId: number, message: string): boolean {
    return this.send({
      type: 'message',
      data: {
        sessionId,
        message,
        type: 'text',
      },
    });
  }

  /**
   * 发送打字状态
   */
  sendTypingStatus(sessionId: number, isTyping: boolean): boolean {
    return this.send({
      type: 'typing',
      data: {
        sessionId,
        isTyping,
      },
    });
  }

  /**
   * 发送用户状态更新
   */
  sendUserStatus(status: 'online' | 'offline' | 'away'): boolean {
    return this.send({
      type: 'user_status',
      data: {
        status,
      },
    });
  }

  /**
   * 开始心跳
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      this.send({
        type: 'heartbeat',
        data: {},
      });
    }, this.heartbeatInterval);
  }

  /**
   * 添加消息处理器
   */
  addMessageHandler(type: string, handler: MessageHandler): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  /**
   * 移除消息处理器
   */
  removeMessageHandler(type: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 添加状态处理器
   */
  addStatusHandler(handler: StatusHandler): void {
    this.statusHandlers.push(handler);
  }

  /**
   * 移除状态处理器
   */
  removeStatusHandler(handler: StatusHandler): void {
    const index = this.statusHandlers.indexOf(handler);
    if (index > -1) {
      this.statusHandlers.splice(index, 1);
    }
  }

  /**
   * 通知状态处理器
   */
  private notifyStatusHandlers(): void {
    this.statusHandlers.forEach(handler => {
      try {
        handler(this.status);
      } catch (error) {
        console.error('Error in status handler:', error);
      }
    });
  }

  /**
   * 获取连接状态
   */
  getStatus(): ConnectionStatus {
    return { ...this.status };
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.status.connected;
  }

  /**
   * 重置重连计数
   */
  resetReconnectAttempts(): void {
    this.status.reconnectAttempts = 0;
  }
}

// 创建全局WebSocket服务实例
export const websocketService = new WebSocketService();

// React Hook for WebSocket
export function useWebSocket() {
  const [status, setStatus] = React.useState<ConnectionStatus>(websocketService.getStatus());

  React.useEffect(() => {
    const handleStatusChange = (newStatus: ConnectionStatus) => {
      setStatus(newStatus);
    };

    websocketService.addStatusHandler(handleStatusChange);

    return () => {
      websocketService.removeStatusHandler(handleStatusChange);
    };
  }, []);

  return {
    status,
    send: websocketService.send.bind(websocketService),
    sendChatMessage: websocketService.sendChatMessage.bind(websocketService),
    sendTypingStatus: websocketService.sendTypingStatus.bind(websocketService),
    addMessageHandler: websocketService.addMessageHandler.bind(websocketService),
    removeMessageHandler: websocketService.removeMessageHandler.bind(websocketService),
    connect: websocketService.connect.bind(websocketService),
    disconnect: websocketService.disconnect.bind(websocketService),
  };
}
