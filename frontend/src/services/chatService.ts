import { apiClient } from './apiClient';

export interface ChatMessage {
  id: number;
  sessionId: number;
  senderId: number;
  senderName: string;
  senderAvatar?: string;
  message: string;
  timestamp: string;
  type: 'text' | 'service' | 'booking' | 'image';
  serviceData?: {
    id: number;
    title: string;
    price: number;
    duration: string;
  };
  isRead: boolean;
}

export interface ChatSession {
  id: number;
  participantId: number;
  participantName: string;
  participantAvatar?: string;
  isGuide: boolean;
  isOnline: boolean;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount: number;
  serviceId?: number;
  bookingId?: number;
}

export interface SendMessageRequest {
  sessionId: number;
  message: string;
  type: 'text' | 'service' | 'booking' | 'image';
  serviceData?: {
    id: number;
    title: string;
    price: number;
    duration: string;
  };
}

export interface CreateSessionRequest {
  participantId: number;
  serviceId?: number;
  initialMessage?: string;
}

export const chatService = {
  /**
   * 获取聊天会话列表
   */
  async getChatSessions(): Promise<ChatSession[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: ChatSession[], message: string}>(
        '/chat/sessions'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching chat sessions:', error);
      // 返回模拟数据
      return [
        {
          id: 1,
          participantId: 101,
          participantName: 'Maria Santos',
          participantAvatar: 'https://via.placeholder.com/50',
          isGuide: true,
          isOnline: true,
          lastMessage: 'Thank you for booking my tour! I\'ll see you tomorrow at 10 AM.',
          lastMessageTime: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          unreadCount: 2,
          serviceId: 1,
        },
        {
          id: 2,
          participantId: 102,
          participantName: 'Ahmed Hassan',
          participantAvatar: 'https://via.placeholder.com/50',
          isGuide: true,
          isOnline: false,
          lastMessage: 'The weather looks perfect for our architecture tour!',
          lastMessageTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          unreadCount: 0,
          serviceId: 2,
        },
        {
          id: 3,
          participantId: 103,
          participantName: 'Elena Rossi',
          participantAvatar: 'https://via.placeholder.com/50',
          isGuide: true,
          isOnline: true,
          lastMessage: 'I have some great art galleries to show you!',
          lastMessageTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          unreadCount: 1,
          serviceId: 3,
        },
      ];
    }
  },

  /**
   * 获取聊天消息
   */
  async getChatMessages(sessionId: number, page: number = 0, size: number = 50): Promise<ChatMessage[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: ChatMessage[], message: string}>(
        `/chat/sessions/${sessionId}/messages?page=${page}&size=${size}`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      // 返回模拟数据
      return [
        {
          id: 1,
          sessionId,
          senderId: 101,
          senderName: 'Maria Santos',
          senderAvatar: 'https://via.placeholder.com/50',
          message: 'Hi! Thank you for your interest in my Gothic Quarter tour.',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          type: 'text',
          isRead: true,
        },
        {
          id: 2,
          sessionId,
          senderId: 1, // Current user
          senderName: 'You',
          message: 'Hello! I\'d love to book your tour for tomorrow. Is 10 AM available?',
          timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
          type: 'text',
          isRead: true,
        },
        {
          id: 3,
          sessionId,
          senderId: 101,
          senderName: 'Maria Santos',
          senderAvatar: 'https://via.placeholder.com/50',
          message: 'Perfect! 10 AM works great. Here\'s the service details:',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          type: 'text',
          isRead: true,
        },
        {
          id: 4,
          sessionId,
          senderId: 101,
          senderName: 'Maria Santos',
          senderAvatar: 'https://via.placeholder.com/50',
          message: '',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          type: 'service',
          serviceData: {
            id: 1,
            title: 'Gothic Quarter Walking Tour',
            price: 45,
            duration: '3h',
          },
          isRead: true,
        },
        {
          id: 5,
          sessionId,
          senderId: 101,
          senderName: 'Maria Santos',
          senderAvatar: 'https://via.placeholder.com/50',
          message: 'Thank you for booking my tour! I\'ll see you tomorrow at 10 AM at the Cathedral.',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          type: 'text',
          isRead: false,
        },
      ];
    }
  },

  /**
   * 发送消息
   */
  async sendMessage(request: SendMessageRequest): Promise<ChatMessage> {
    try {
      const response = await apiClient.post<{success: boolean, data: ChatMessage, message: string}>(
        '/chat/messages',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  },

  /**
   * 创建新的聊天会话
   */
  async createChatSession(request: CreateSessionRequest): Promise<ChatSession> {
    try {
      const response = await apiClient.post<{success: boolean, data: ChatSession, message: string}>(
        '/chat/sessions',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw error;
    }
  },

  /**
   * 标记消息为已读
   */
  async markMessagesAsRead(sessionId: number): Promise<void> {
    try {
      await apiClient.post(`/chat/sessions/${sessionId}/mark-read`);
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  },

  /**
   * 获取未读消息总数
   */
  async getUnreadCount(): Promise<number> {
    try {
      const response = await apiClient.get<{success: boolean, data: {count: number}, message: string}>(
        '/chat/unread-count'
      );
      return response.data.data.count;
    } catch (error) {
      console.error('Error fetching unread count:', error);
      return 0;
    }
  },

  /**
   * 删除聊天会话
   */
  async deleteChatSession(sessionId: number): Promise<void> {
    try {
      await apiClient.delete(`/chat/sessions/${sessionId}`);
    } catch (error) {
      console.error('Error deleting chat session:', error);
      throw error;
    }
  },
};
