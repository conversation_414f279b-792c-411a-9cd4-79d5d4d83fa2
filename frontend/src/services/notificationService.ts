import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import { apiClient } from './apiClient';

export interface NotificationData {
  id: string;
  type: 'booking_confirmed' | 'booking_cancelled' | 'message_received' | 'payment_success' | 'payment_failed' | 'guide_update' | 'reminder';
  title: string;
  body: string;
  data?: Record<string, any>;
  userId: number;
  isRead: boolean;
  createdAt: string;
  scheduledFor?: string;
}

export interface PushNotificationToken {
  token: string;
  platform: 'ios' | 'android' | 'web';
  deviceId: string;
  userId: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationSettings {
  pushEnabled: boolean;
  emailEnabled: boolean;
  smsEnabled: boolean;
  categories: {
    bookings: boolean;
    messages: boolean;
    payments: boolean;
    marketing: boolean;
    reminders: boolean;
  };
  quietHours: {
    enabled: boolean;
    startTime: string; // HH:mm
    endTime: string; // HH:mm
  };
}

// 配置通知处理
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

class NotificationService {
  private expoPushToken: string | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;

  /**
   * 初始化通知服务
   */
  async initialize(): Promise<void> {
    try {
      // 注册推送通知
      await this.registerForPushNotifications();
      
      // 设置通知监听器
      this.setupNotificationListeners();
      
      // 请求权限
      await this.requestPermissions();
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
    }
  }

  /**
   * 请求通知权限
   */
  async requestPermissions(): Promise<boolean> {
    try {
      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;
        
        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }
        
        if (finalStatus !== 'granted') {
          console.warn('Failed to get push token for push notification!');
          return false;
        }
        
        return true;
      } else {
        console.warn('Must use physical device for Push Notifications');
        return false;
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  /**
   * 注册推送通知
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Must use physical device for Push Notifications');
        return null;
      }

      const token = (await Notifications.getExpoPushTokenAsync()).data;
      this.expoPushToken = token;

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      // 将token发送到服务器
      await this.sendTokenToServer(token);

      return token;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      return null;
    }
  }

  /**
   * 将推送token发送到服务器
   */
  async sendTokenToServer(token: string): Promise<void> {
    try {
      await apiClient.post('/notifications/register-token', {
        token,
        platform: Platform.OS,
        deviceId: Device.osInternalBuildId || 'unknown',
      });
    } catch (error) {
      console.error('Error sending token to server:', error);
    }
  }

  /**
   * 设置通知监听器
   */
  setupNotificationListeners(): void {
    // 监听收到的通知
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // 监听通知响应（用户点击通知）
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  /**
   * 处理收到的通知
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    const { data } = notification.request.content;
    
    // 根据通知类型执行相应操作
    switch (data?.type) {
      case 'message_received':
        // 更新聊天未读计数
        this.updateChatBadge(data.sessionId);
        break;
      case 'booking_confirmed':
        // 刷新预订列表
        this.refreshBookings();
        break;
      case 'payment_success':
        // 显示支付成功提示
        this.showPaymentSuccessNotification();
        break;
    }
  }

  /**
   * 处理通知响应（用户点击）
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const { data } = response.notification.request.content;
    
    // 根据通知类型导航到相应页面
    switch (data?.type) {
      case 'message_received':
        // 导航到聊天页面
        this.navigateToChat(data.sessionId);
        break;
      case 'booking_confirmed':
      case 'booking_cancelled':
        // 导航到预订详情页面
        this.navigateToBooking(data.bookingId);
        break;
      case 'payment_success':
      case 'payment_failed':
        // 导航到支付页面或预订页面
        this.navigateToPayment(data.paymentId);
        break;
    }
  }

  /**
   * 发送本地通知
   */
  async sendLocalNotification(
    title: string,
    body: string,
    data?: Record<string, any>,
    scheduledFor?: Date
  ): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: true,
        },
        trigger: scheduledFor ? { date: scheduledFor } : null,
      });

      return notificationId;
    } catch (error) {
      console.error('Error sending local notification:', error);
      throw error;
    }
  }

  /**
   * 取消本地通知
   */
  async cancelLocalNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error cancelling local notification:', error);
    }
  }

  /**
   * 获取通知历史
   */
  async getNotificationHistory(page: number = 0, size: number = 20): Promise<{
    notifications: NotificationData[];
    total: number;
    unreadCount: number;
  }> {
    try {
      const response = await apiClient.get<{success: boolean, data: any, message: string}>(
        `/notifications/history?page=${page}&size=${size}`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching notification history:', error);
      return {
        notifications: this.getMockNotifications(),
        total: 5,
        unreadCount: 2,
      };
    }
  }

  /**
   * 获取模拟通知数据
   */
  getMockNotifications(): NotificationData[] {
    return [
      {
        id: '1',
        type: 'booking_confirmed',
        title: 'Booking Confirmed',
        body: 'Your Gothic Quarter Walking Tour has been confirmed for Jan 20, 2024',
        data: { bookingId: 1 },
        userId: 1,
        isRead: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      },
      {
        id: '2',
        type: 'message_received',
        title: 'New Message from Maria Santos',
        body: 'Looking forward to showing you around Barcelona!',
        data: { sessionId: 1, senderId: 101 },
        userId: 1,
        isRead: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: '3',
        type: 'payment_success',
        title: 'Payment Successful',
        body: 'Your payment of $90.00 has been processed successfully',
        data: { paymentId: 'pi_1', bookingId: 1 },
        userId: 1,
        isRead: true,
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      },
    ];
  }

  /**
   * 标记通知为已读
   */
  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await apiClient.patch(`/notifications/${notificationId}/read`);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * 标记所有通知为已读
   */
  async markAllNotificationsAsRead(): Promise<void> {
    try {
      await apiClient.patch('/notifications/read-all');
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  /**
   * 获取通知设置
   */
  async getNotificationSettings(): Promise<NotificationSettings> {
    try {
      const response = await apiClient.get<{success: boolean, data: NotificationSettings, message: string}>(
        '/notifications/settings'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      return this.getDefaultNotificationSettings();
    }
  }

  /**
   * 更新通知设置
   */
  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<NotificationSettings> {
    try {
      const response = await apiClient.patch<{success: boolean, data: NotificationSettings, message: string}>(
        '/notifications/settings',
        settings
      );
      return response.data.data;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      throw error;
    }
  }

  /**
   * 获取默认通知设置
   */
  getDefaultNotificationSettings(): NotificationSettings {
    return {
      pushEnabled: true,
      emailEnabled: true,
      smsEnabled: false,
      categories: {
        bookings: true,
        messages: true,
        payments: true,
        marketing: false,
        reminders: true,
      },
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
      },
    };
  }

  // 私有辅助方法
  private updateChatBadge(sessionId: number): void {
    // 实现聊天徽章更新逻辑
  }

  private refreshBookings(): void {
    // 实现预订刷新逻辑
  }

  private showPaymentSuccessNotification(): void {
    // 实现支付成功通知逻辑
  }

  private navigateToChat(sessionId: number): void {
    // 实现导航到聊天页面的逻辑
  }

  private navigateToBooking(bookingId: number): void {
    // 实现导航到预订页面的逻辑
  }

  private navigateToPayment(paymentId: string): void {
    // 实现导航到支付页面的逻辑
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

// 创建全局通知服务实例
export const notificationService = new NotificationService();
