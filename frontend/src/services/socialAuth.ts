import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import * as Crypto from 'expo-crypto';
import { Platform } from 'react-native';

// 完成WebBrowser会话以便在Web上正常工作
WebBrowser.maybeCompleteAuthSession();

// OAuth配置
const GOOGLE_CLIENT_ID = {
  web: 'YOUR_GOOGLE_WEB_CLIENT_ID',
  ios: 'YOUR_GOOGLE_IOS_CLIENT_ID',
  android: 'YOUR_GOOGLE_ANDROID_CLIENT_ID',
};

const APPLE_CLIENT_ID = 'YOUR_APPLE_CLIENT_ID';

export interface SocialAuthResult {
  success: boolean;
  data?: {
    id: string;
    email: string;
    name: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    provider: 'google' | 'apple';
  };
  error?: string;
}

// Google OAuth配置
const googleConfig = {
  clientId: Platform.select({
    web: GOOGLE_CLIENT_ID.web,
    ios: GOOGLE_CLIENT_ID.ios,
    android: GOOGLE_CLIENT_ID.android,
    default: GOOGLE_CLIENT_ID.web,
  }),
  scopes: ['openid', 'profile', 'email'],
  additionalParameters: {},
  customParameters: {},
};

// Apple OAuth配置
const appleConfig = {
  clientId: APPLE_CLIENT_ID,
  scopes: ['name', 'email'],
  additionalParameters: {},
  customParameters: {},
};

/**
 * Google登录
 */
export const signInWithGoogle = async (): Promise<SocialAuthResult> => {
  try {
    // 创建授权请求
    const request = new AuthSession.AuthRequest({
      clientId: googleConfig.clientId!,
      scopes: googleConfig.scopes,
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'tourna',
        useProxy: true,
      }),
      responseType: AuthSession.ResponseType.Code,
      state: await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        Math.random().toString(),
        { encoding: Crypto.CryptoEncoding.HEX }
      ),
      codeChallenge: await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        Math.random().toString(),
        { encoding: Crypto.CryptoEncoding.BASE64URL }
      ),
      codeChallengeMethod: Crypto.CryptoDigestAlgorithm.SHA256,
    });

    // 发起授权请求
    const result = await request.promptAsync({
      authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      useProxy: true,
    });

    if (result.type === 'success') {
      // 获取用户信息
      const userInfoResponse = await fetch(
        `https://www.googleapis.com/oauth2/v2/userinfo?access_token=${result.params.access_token}`
      );
      const userInfo = await userInfoResponse.json();

      return {
        success: true,
        data: {
          id: userInfo.id,
          email: userInfo.email,
          name: userInfo.name,
          firstName: userInfo.given_name,
          lastName: userInfo.family_name,
          avatar: userInfo.picture,
          provider: 'google',
        },
      };
    } else {
      return {
        success: false,
        error: 'Google login was cancelled or failed',
      };
    }
  } catch (error) {
    console.error('Google login error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Google login failed',
    };
  }
};

/**
 * Apple登录 (仅在iOS上可用)
 */
export const signInWithApple = async (): Promise<SocialAuthResult> => {
  try {
    if (Platform.OS !== 'ios') {
      return {
        success: false,
        error: 'Apple Sign In is only available on iOS',
      };
    }

    // 创建授权请求
    const request = new AuthSession.AuthRequest({
      clientId: appleConfig.clientId,
      scopes: appleConfig.scopes,
      redirectUri: AuthSession.makeRedirectUri({
        scheme: 'tourna',
        useProxy: true,
      }),
      responseType: AuthSession.ResponseType.Code,
      state: await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        Math.random().toString(),
        { encoding: Crypto.CryptoEncoding.HEX }
      ),
    });

    // 发起授权请求
    const result = await request.promptAsync({
      authorizationEndpoint: 'https://appleid.apple.com/auth/authorize',
      useProxy: true,
    });

    if (result.type === 'success') {
      // Apple返回的用户信息有限
      const { id_token } = result.params;
      
      // 解析JWT token获取用户信息
      const tokenParts = id_token.split('.');
      const payload = JSON.parse(
        Buffer.from(tokenParts[1], 'base64').toString()
      );

      return {
        success: true,
        data: {
          id: payload.sub,
          email: payload.email,
          name: payload.email?.split('@')[0] || 'Apple User',
          provider: 'apple',
        },
      };
    } else {
      return {
        success: false,
        error: 'Apple login was cancelled or failed',
      };
    }
  } catch (error) {
    console.error('Apple login error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Apple login failed',
    };
  }
};

/**
 * 模拟社交登录 (用于开发测试)
 */
export const mockSocialLogin = async (provider: 'google' | 'apple'): Promise<SocialAuthResult> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1500));

  const mockUsers = {
    google: {
      id: 'google_123456789',
      email: '<EMAIL>',
      name: 'John Doe',
      firstName: 'John',
      lastName: 'Doe',
      avatar: 'https://via.placeholder.com/100',
      provider: 'google' as const,
    },
    apple: {
      id: 'apple_987654321',
      email: '<EMAIL>',
      name: 'Jane Smith',
      firstName: 'Jane',
      lastName: 'Smith',
      provider: 'apple' as const,
    },
  };

  return {
    success: true,
    data: mockUsers[provider],
  };
};

/**
 * 检查是否支持Apple登录
 */
export const isAppleSignInAvailable = (): boolean => {
  return Platform.OS === 'ios';
};

/**
 * 检查是否支持Google登录
 */
export const isGoogleSignInAvailable = (): boolean => {
  return true; // Google登录在所有平台都可用
};
