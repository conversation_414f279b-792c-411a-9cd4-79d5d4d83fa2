import { AppState, AppStateStatus } from 'react-native';
import { websocketService } from './websocketService';
import { notificationService } from './notificationService';
import { storageService } from './storageService';

class AppInitService {
  private isInitialized = false;
  private appStateSubscription: any = null;

  /**
   * 初始化应用服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing app services...');

      // 1. 初始化通知服务
      await notificationService.initialize();

      // 2. 检查用户是否已登录
      const token = await storageService.getToken();
      if (token) {
        // 3. 连接WebSocket
        await this.connectWebSocket();
      }

      // 4. 设置应用状态监听
      this.setupAppStateListener();

      // 5. 设置WebSocket消息处理
      this.setupWebSocketHandlers();

      this.isInitialized = true;
      console.log('App services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize app services:', error);
    }
  }

  /**
   * 连接WebSocket
   */
  async connectWebSocket(): Promise<void> {
    try {
      if (!websocketService.isConnected()) {
        await websocketService.connect();
        console.log('WebSocket connected');
      }
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
    }
  }

  /**
   * 断开WebSocket
   */
  disconnectWebSocket(): void {
    websocketService.disconnect();
    console.log('WebSocket disconnected');
  }

  /**
   * 设置应用状态监听
   */
  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      this.handleAppStateChange.bind(this)
    );
  }

  /**
   * 处理应用状态变化
   */
  private handleAppStateChange(nextAppState: AppStateStatus): void {
    console.log('App state changed to:', nextAppState);

    switch (nextAppState) {
      case 'active':
        // 应用进入前台
        this.handleAppForeground();
        break;
      case 'background':
        // 应用进入后台
        this.handleAppBackground();
        break;
      case 'inactive':
        // 应用变为非活跃状态
        break;
    }
  }

  /**
   * 处理应用进入前台
   */
  private async handleAppForeground(): Promise<void> {
    try {
      // 重新连接WebSocket
      const token = await storageService.getToken();
      if (token && !websocketService.isConnected()) {
        await this.connectWebSocket();
      }

      // 发送用户在线状态
      if (websocketService.isConnected()) {
        websocketService.sendUserStatus('online');
      }

      // 清除应用图标徽章
      await notificationService.sendLocalNotification('', '', {}, undefined);
    } catch (error) {
      console.error('Error handling app foreground:', error);
    }
  }

  /**
   * 处理应用进入后台
   */
  private handleAppBackground(): void {
    try {
      // 发送用户离线状态
      if (websocketService.isConnected()) {
        websocketService.sendUserStatus('away');
      }

      // 可以选择断开WebSocket以节省电量
      // this.disconnectWebSocket();
    } catch (error) {
      console.error('Error handling app background:', error);
    }
  }

  /**
   * 设置WebSocket消息处理
   */
  private setupWebSocketHandlers(): void {
    // 处理通知消息
    websocketService.addMessageHandler('notification', (message) => {
      this.handleNotificationMessage(message);
    });

    // 处理预订更新
    websocketService.addMessageHandler('booking_update', (message) => {
      this.handleBookingUpdate(message);
    });

    // 处理用户状态更新
    websocketService.addMessageHandler('user_status', (message) => {
      this.handleUserStatusUpdate(message);
    });
  }

  /**
   * 处理通知消息
   */
  private async handleNotificationMessage(message: any): Promise<void> {
    try {
      const { data } = message;
      
      // 发送本地通知
      await notificationService.sendLocalNotification(
        data.title,
        data.body,
        data.data
      );
    } catch (error) {
      console.error('Error handling notification message:', error);
    }
  }

  /**
   * 处理预订更新
   */
  private handleBookingUpdate(message: any): void {
    try {
      const { data } = message;
      console.log('Booking update received:', data);
      
      // 这里可以触发预订列表刷新
      // 或者发送事件给相关组件
    } catch (error) {
      console.error('Error handling booking update:', error);
    }
  }

  /**
   * 处理用户状态更新
   */
  private handleUserStatusUpdate(message: any): void {
    try {
      const { data } = message;
      console.log('User status update:', data);
      
      // 这里可以更新用户在线状态显示
    } catch (error) {
      console.error('Error handling user status update:', error);
    }
  }

  /**
   * 用户登录后的初始化
   */
  async onUserLogin(): Promise<void> {
    try {
      // 连接WebSocket
      await this.connectWebSocket();

      // 发送用户在线状态
      if (websocketService.isConnected()) {
        websocketService.sendUserStatus('online');
      }

      // 重新注册推送通知token
      await notificationService.registerForPushNotifications();
    } catch (error) {
      console.error('Error in user login initialization:', error);
    }
  }

  /**
   * 用户登出时的清理
   */
  async onUserLogout(): Promise<void> {
    try {
      // 发送用户离线状态
      if (websocketService.isConnected()) {
        websocketService.sendUserStatus('offline');
      }

      // 断开WebSocket
      this.disconnectWebSocket();

      // 清除本地通知
      // await notificationService.cancelAllLocalNotifications();
    } catch (error) {
      console.error('Error in user logout cleanup:', error);
    }
  }

  /**
   * 检查网络连接状态
   */
  async checkNetworkAndReconnect(): Promise<void> {
    try {
      const token = await storageService.getToken();
      if (token && !websocketService.isConnected()) {
        await this.connectWebSocket();
      }
    } catch (error) {
      console.error('Error checking network and reconnecting:', error);
    }
  }

  /**
   * 获取应用状态
   */
  getStatus(): {
    initialized: boolean;
    websocketConnected: boolean;
    notificationsEnabled: boolean;
  } {
    return {
      initialized: this.isInitialized,
      websocketConnected: websocketService.isConnected(),
      notificationsEnabled: true, // 可以从通知服务获取实际状态
    };
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    this.disconnectWebSocket();
    notificationService.cleanup();
    
    this.isInitialized = false;
  }
}

// 创建全局应用初始化服务实例
export const appInitService = new AppInitService();

// React Hook for app initialization
export function useAppInit() {
  const [status, setStatus] = React.useState(appInitService.getStatus());

  React.useEffect(() => {
    const checkStatus = () => {
      setStatus(appInitService.getStatus());
    };

    // 定期检查状态
    const interval = setInterval(checkStatus, 5000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return {
    status,
    initialize: appInitService.initialize.bind(appInitService),
    onUserLogin: appInitService.onUserLogin.bind(appInitService),
    onUserLogout: appInitService.onUserLogout.bind(appInitService),
    checkNetworkAndReconnect: appInitService.checkNetworkAndReconnect.bind(appInitService),
  };
}
