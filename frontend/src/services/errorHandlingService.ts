import { Alert } from 'react-native';

export interface ErrorInfo {
  message: string;
  code?: string | number;
  details?: any;
  timestamp: number;
  userId?: string;
  context?: Record<string, any>;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // 基础延迟时间（毫秒）
  maxDelay: number; // 最大延迟时间（毫秒）
  backoffFactor: number; // 退避因子
}

class ErrorHandlingService {
  private errorQueue: ErrorInfo[] = [];
  private maxQueueSize = 100;

  /**
   * 记录错误
   */
  logError(error: Error | string, context?: Record<string, any>): void {
    const errorInfo: ErrorInfo = {
      message: typeof error === 'string' ? error : error.message,
      details: typeof error === 'object' ? error : undefined,
      timestamp: Date.now(),
      context,
    };

    // 添加到错误队列
    this.addToQueue(errorInfo);

    // 发送到错误监控服务
    this.sendToMonitoring(errorInfo);

    // 在开发环境中打印错误
    if (__DEV__) {
      console.error('Error logged:', errorInfo);
    }
  }

  /**
   * 添加错误到队列
   */
  private addToQueue(errorInfo: ErrorInfo): void {
    this.errorQueue.push(errorInfo);
    
    // 保持队列大小限制
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
  }

  /**
   * 发送错误到监控服务
   */
  private sendToMonitoring(errorInfo: ErrorInfo): void {
    try {
      // TODO: 发送到Sentry（如果配置了）
      // if (Sentry.getCurrentHub().getClient()) {
      //   Sentry.captureException(new Error(errorInfo.message), {
      //     extra: {
      //       details: errorInfo.details,
      //       context: errorInfo.context,
      //       timestamp: errorInfo.timestamp,
      //     },
      //   });
      // }

      // 暂时只在控制台记录
      console.warn('Error monitoring:', errorInfo);
    } catch (error) {
      console.error('Failed to send error to monitoring service:', error);
    }
  }

  /**
   * 处理API错误
   */
  handleApiError(error: any, showAlert: boolean = true): string {
    let message = 'An unexpected error occurred';
    
    if (error?.response) {
      // HTTP错误响应
      const status = error.response.status;
      const data = error.response.data;
      
      switch (status) {
        case 400:
          message = data?.message || 'Invalid request';
          break;
        case 401:
          message = 'Authentication required';
          break;
        case 403:
          message = 'Access denied';
          break;
        case 404:
          message = 'Resource not found';
          break;
        case 429:
          message = 'Too many requests. Please try again later';
          break;
        case 500:
          message = 'Server error. Please try again later';
          break;
        default:
          message = data?.message || `HTTP ${status} error`;
      }
    } else if (error?.message) {
      // 网络错误或其他错误
      if (error.message.includes('Network Error')) {
        message = 'Network connection error. Please check your internet connection';
      } else {
        message = error.message;
      }
    }

    // 记录错误
    this.logError(error, { type: 'api_error' });

    // 显示用户友好的错误提示
    if (showAlert) {
      Alert.alert('Error', message);
    }

    return message;
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error: any): string {
    const message = 'Network connection error. Please check your internet connection and try again.';
    
    this.logError(error, { type: 'network_error' });
    
    Alert.alert(
      'Connection Error',
      message,
      [
        { text: 'OK', style: 'default' },
        { text: 'Retry', onPress: () => window.location.reload?.() },
      ]
    );

    return message;
  }

  /**
   * 重试机制
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig: RetryConfig = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      ...config,
    };

    let lastError: any;
    
    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === retryConfig.maxRetries) {
          // 最后一次尝试失败
          this.logError(error, { 
            type: 'retry_exhausted',
            attempts: attempt + 1,
            maxRetries: retryConfig.maxRetries 
          });
          throw error;
        }

        // 计算延迟时间（指数退避）
        const delay = Math.min(
          retryConfig.baseDelay * Math.pow(retryConfig.backoffFactor, attempt),
          retryConfig.maxDelay
        );

        // 记录重试
        this.logError(error, { 
          type: 'retry_attempt',
          attempt: attempt + 1,
          delay,
          maxRetries: retryConfig.maxRetries 
        });

        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  }

  /**
   * 安全执行异步操作
   */
  async safeAsync<T>(
    operation: () => Promise<T>,
    fallback?: T,
    onError?: (error: any) => void
  ): Promise<T | undefined> {
    try {
      return await operation();
    } catch (error) {
      this.logError(error, { type: 'safe_async_error' });
      
      if (onError) {
        onError(error);
      }
      
      return fallback;
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorInfo[] {
    return [...this.errorQueue];
  }

  /**
   * 清空错误历史
   */
  clearErrorHistory(): void {
    this.errorQueue = [];
  }

  /**
   * 检查是否为网络错误
   */
  isNetworkError(error: any): boolean {
    return (
      error?.code === 'NETWORK_ERROR' ||
      error?.message?.includes('Network Error') ||
      error?.message?.includes('fetch') ||
      !navigator.onLine
    );
  }

  /**
   * 检查是否为认证错误
   */
  isAuthError(error: any): boolean {
    return (
      error?.response?.status === 401 ||
      error?.code === 'UNAUTHORIZED' ||
      error?.message?.includes('unauthorized')
    );
  }

  /**
   * 检查是否为服务器错误
   */
  isServerError(error: any): boolean {
    const status = error?.response?.status;
    return status >= 500 && status < 600;
  }

  /**
   * 格式化错误消息
   */
  formatErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error?.response?.data?.message) {
      return error.response.data.message;
    }

    if (error?.message) {
      return error.message;
    }

    return 'An unexpected error occurred';
  }

  /**
   * 创建错误边界处理器
   */
  createErrorBoundaryHandler() {
    return (error: Error, errorInfo: any) => {
      this.logError(error, {
        type: 'react_error_boundary',
        componentStack: errorInfo.componentStack,
      });
    };
  }
}

// 创建全局错误处理服务实例
export const errorHandlingService = new ErrorHandlingService();

// 错误处理装饰器
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  showAlert: boolean = true
) {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: Parameters<T>) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        return errorHandlingService.handleApiError(error, showAlert);
      }
    };
  };
}

// 重试装饰器
export function withRetry(config?: Partial<RetryConfig>) {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      return errorHandlingService.withRetry(
        () => originalMethod.apply(this, args),
        config
      );
    };
  };
}
