import { apiClient } from './apiClient';

export interface SocialAuthRequest {
  provider: 'google' | 'apple';
  accessToken: string;
  idToken?: string;
  email?: string;
  name?: string;
  profilePicture?: string;
}

export interface SocialAuthResponse {
  user: {
    id: number;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    avatarUrl?: string;
    isVerified: boolean;
    createdAt: string;
    updatedAt: string;
  };
  token: string;
  refreshToken: string;
  isNewUser: boolean;
}

export interface SocialAccount {
  id: number;
  provider: 'google' | 'apple';
  providerId: string;
  email: string;
  name: string;
  profilePicture?: string;
  isActive: boolean;
  connectedAt: string;
}

export const socialAuthService = {
  /**
   * Google登录
   */
  async loginWithGoogle(googleToken: string): Promise<SocialAuthResponse> {
    try {
      const response = await apiClient.post<{success: boolean, data: SocialAuthResponse, message: string}>(
        '/auth/social/google',
        {
          provider: 'google',
          accessToken: googleToken,
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Google login failed:', error);
      throw error;
    }
  },

  /**
   * Apple登录
   */
  async loginWithApple(appleData: {
    identityToken: string;
    authorizationCode: string;
    email?: string;
    fullName?: {
      givenName?: string;
      familyName?: string;
    };
  }): Promise<SocialAuthResponse> {
    try {
      const response = await apiClient.post<{success: boolean, data: SocialAuthResponse, message: string}>(
        '/auth/social/apple',
        {
          provider: 'apple',
          idToken: appleData.identityToken,
          authorizationCode: appleData.authorizationCode,
          email: appleData.email,
          name: appleData.fullName ? 
            `${appleData.fullName.givenName || ''} ${appleData.fullName.familyName || ''}`.trim() : 
            undefined,
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Apple login failed:', error);
      throw error;
    }
  },

  /**
   * 获取用户的社交账户
   */
  async getSocialAccounts(): Promise<SocialAccount[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: SocialAccount[], message: string}>(
        '/users/social-accounts'
      );
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch social accounts:', error);
      return [];
    }
  },

  /**
   * 连接社交账户
   */
  async connectSocialAccount(request: SocialAuthRequest): Promise<SocialAccount> {
    try {
      const response = await apiClient.post<{success: boolean, data: SocialAccount, message: string}>(
        '/users/social-accounts/connect',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Failed to connect social account:', error);
      throw error;
    }
  },

  /**
   * 断开社交账户连接
   */
  async disconnectSocialAccount(accountId: number): Promise<void> {
    try {
      await apiClient.delete(`/users/social-accounts/${accountId}`);
    } catch (error) {
      console.error('Failed to disconnect social account:', error);
      throw error;
    }
  },

  /**
   * 刷新社交账户令牌
   */
  async refreshSocialToken(provider: 'google' | 'apple', refreshToken: string): Promise<{
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  }> {
    try {
      const response = await apiClient.post<{success: boolean, data: any, message: string}>(
        '/auth/social/refresh',
        {
          provider,
          refreshToken,
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Failed to refresh social token:', error);
      throw error;
    }
  },

  /**
   * 验证社交登录状态
   */
  async verifySocialAuth(provider: 'google' | 'apple'): Promise<boolean> {
    try {
      const response = await apiClient.get<{success: boolean, data: {isValid: boolean}, message: string}>(
        `/auth/social/${provider}/verify`
      );
      return response.data.data.isValid;
    } catch (error) {
      console.error('Failed to verify social auth:', error);
      return false;
    }
  },

  /**
   * 获取Google登录URL（用于Web）
   */
  getGoogleAuthUrl(): string {
    const clientId = process.env.EXPO_PUBLIC_GOOGLE_CLIENT_ID;
    const redirectUri = encodeURIComponent(process.env.EXPO_PUBLIC_GOOGLE_REDIRECT_URI || 'http://localhost:8081/auth/google/callback');
    const scope = encodeURIComponent('openid email profile');
    const responseType = 'code';
    const state = Math.random().toString(36).substring(2, 15);

    return `https://accounts.google.com/oauth/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&response_type=${responseType}&state=${state}`;
  },

  /**
   * 处理Google OAuth回调
   */
  async handleGoogleCallback(code: string, state: string): Promise<SocialAuthResponse> {
    try {
      const response = await apiClient.post<{success: boolean, data: SocialAuthResponse, message: string}>(
        '/auth/social/google/callback',
        {
          code,
          state,
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Failed to handle Google callback:', error);
      throw error;
    }
  },

  /**
   * 注销社交登录
   */
  async logoutSocial(provider: 'google' | 'apple'): Promise<void> {
    try {
      await apiClient.post(`/auth/social/${provider}/logout`);
    } catch (error) {
      console.error('Failed to logout from social provider:', error);
      throw error;
    }
  },
};
