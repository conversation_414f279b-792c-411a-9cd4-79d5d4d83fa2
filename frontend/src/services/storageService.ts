import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * 存储服务
 * 提供统一的数据存储接口，支持Web和移动端
 */
class StorageService {
  private readonly SECURE_KEYS = [
    'auth_token',
    'refresh_token',
    'wallet_private_key',
    'biometric_key',
  ];

  /**
   * 存储数据
   */
  async setItem(key: string, value: string): Promise<void> {
    try {
      if (this.isSecureKey(key) && this.isSecureStoreAvailable()) {
        // 敏感数据使用安全存储
        await SecureStore.setItemAsync(key, value);
      } else {
        // 普通数据使用AsyncStorage
        await AsyncStorage.setItem(key, value);
      }
    } catch (error) {
      console.error(`Failed to store item ${key}:`, error);
      throw new Error(`存储数据失败: ${key}`);
    }
  }

  /**
   * 获取数据
   */
  async getItem(key: string): Promise<string | null> {
    try {
      if (this.isSecureKey(key) && this.isSecureStoreAvailable()) {
        // 从安全存储获取敏感数据
        return await SecureStore.getItemAsync(key);
      } else {
        // 从AsyncStorage获取普通数据
        return await AsyncStorage.getItem(key);
      }
    } catch (error) {
      console.error(`Failed to get item ${key}:`, error);
      return null;
    }
  }

  /**
   * 删除数据
   */
  async removeItem(key: string): Promise<void> {
    try {
      if (this.isSecureKey(key) && this.isSecureStoreAvailable()) {
        // 从安全存储删除敏感数据
        await SecureStore.deleteItemAsync(key);
      } else {
        // 从AsyncStorage删除普通数据
        await AsyncStorage.removeItem(key);
      }
    } catch (error) {
      console.error(`Failed to remove item ${key}:`, error);
      throw new Error(`删除数据失败: ${key}`);
    }
  }

  /**
   * 存储对象数据
   */
  async setObject(key: string, value: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(value);
      await this.setItem(key, jsonValue);
    } catch (error) {
      console.error(`Failed to store object ${key}:`, error);
      throw new Error(`存储对象失败: ${key}`);
    }
  }

  /**
   * 获取对象数据
   */
  async getObject<T = any>(key: string): Promise<T | null> {
    try {
      const jsonValue = await this.getItem(key);
      return jsonValue ? JSON.parse(jsonValue) : null;
    } catch (error) {
      console.error(`Failed to get object ${key}:`, error);
      return null;
    }
  }

  /**
   * 批量存储数据
   */
  async setMultiple(keyValuePairs: Array<[string, string]>): Promise<void> {
    try {
      const secureItems: Array<[string, string]> = [];
      const normalItems: Array<[string, string]> = [];

      // 分类敏感数据和普通数据
      keyValuePairs.forEach(([key, value]) => {
        if (this.isSecureKey(key) && this.isSecureStoreAvailable()) {
          secureItems.push([key, value]);
        } else {
          normalItems.push([key, value]);
        }
      });

      // 并行存储
      const promises: Promise<void>[] = [];

      if (normalItems.length > 0) {
        promises.push(AsyncStorage.multiSet(normalItems));
      }

      if (secureItems.length > 0) {
        secureItems.forEach(([key, value]) => {
          promises.push(SecureStore.setItemAsync(key, value));
        });
      }

      await Promise.all(promises);
    } catch (error) {
      console.error('Failed to set multiple items:', error);
      throw new Error('批量存储数据失败');
    }
  }

  /**
   * 批量获取数据
   */
  async getMultiple(keys: string[]): Promise<Array<[string, string | null]>> {
    try {
      const secureKeys = keys.filter(key => this.isSecureKey(key) && this.isSecureStoreAvailable());
      const normalKeys = keys.filter(key => !this.isSecureKey(key) || !this.isSecureStoreAvailable());

      const promises: Promise<Array<[string, string | null]>>[] = [];

      // 获取普通数据
      if (normalKeys.length > 0) {
        promises.push(AsyncStorage.multiGet(normalKeys));
      }

      // 获取敏感数据
      if (secureKeys.length > 0) {
        const securePromise = Promise.all(
          secureKeys.map(async (key) => {
            const value = await SecureStore.getItemAsync(key);
            return [key, value] as [string, string | null];
          })
        );
        promises.push(securePromise);
      }

      const results = await Promise.all(promises);
      return results.flat();
    } catch (error) {
      console.error('Failed to get multiple items:', error);
      return keys.map(key => [key, null]);
    }
  }

  /**
   * 批量删除数据
   */
  async removeMultiple(keys: string[]): Promise<void> {
    try {
      const secureKeys = keys.filter(key => this.isSecureKey(key) && this.isSecureStoreAvailable());
      const normalKeys = keys.filter(key => !this.isSecureKey(key) || !this.isSecureStoreAvailable());

      const promises: Promise<void>[] = [];

      // 删除普通数据
      if (normalKeys.length > 0) {
        promises.push(AsyncStorage.multiRemove(normalKeys));
      }

      // 删除敏感数据
      if (secureKeys.length > 0) {
        secureKeys.forEach(key => {
          promises.push(SecureStore.deleteItemAsync(key));
        });
      }

      await Promise.all(promises);
    } catch (error) {
      console.error('Failed to remove multiple items:', error);
      throw new Error('批量删除数据失败');
    }
  }

  /**
   * 清除所有数据
   */
  async clear(): Promise<void> {
    try {
      // 清除AsyncStorage
      await AsyncStorage.clear();

      // 清除SecureStore中的敏感数据
      if (this.isSecureStoreAvailable()) {
        const clearPromises = this.SECURE_KEYS.map(async (key) => {
          try {
            await SecureStore.deleteItemAsync(key);
          } catch (error) {
            // 忽略不存在的key的错误
            console.warn(`Failed to delete secure item ${key}:`, error);
          }
        });
        await Promise.all(clearPromises);
      }
    } catch (error) {
      console.error('Failed to clear storage:', error);
      throw new Error('清除存储失败');
    }
  }

  /**
   * 获取所有键名
   */
  async getAllKeys(): Promise<string[]> {
    try {
      return await AsyncStorage.getAllKeys();
    } catch (error) {
      console.error('Failed to get all keys:', error);
      return [];
    }
  }

  /**
   * 检查键是否存在
   */
  async hasKey(key: string): Promise<boolean> {
    try {
      const value = await this.getItem(key);
      return value !== null;
    } catch (error) {
      console.error(`Failed to check key ${key}:`, error);
      return false;
    }
  }

  /**
   * 获取存储大小（仅AsyncStorage）
   */
  async getStorageSize(): Promise<number> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const items = await AsyncStorage.multiGet(keys);
      
      let totalSize = 0;
      items.forEach(([key, value]) => {
        if (value) {
          totalSize += key.length + value.length;
        }
      });
      
      return totalSize;
    } catch (error) {
      console.error('Failed to get storage size:', error);
      return 0;
    }
  }

  /**
   * 检查是否为敏感数据键
   */
  private isSecureKey(key: string): boolean {
    return this.SECURE_KEYS.includes(key);
  }

  /**
   * 检查SecureStore是否可用
   */
  private isSecureStoreAvailable(): boolean {
    // Web环境下SecureStore不可用
    return Platform.OS !== 'web';
  }
}

export const storageService = new StorageService();
