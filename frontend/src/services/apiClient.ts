import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { storageService } from './storageService';

/**
 * API客户端配置
 */
class ApiClient {
  private instance: AxiosInstance;
  private readonly baseURL: string;

  constructor() {
    // 根据环境设置API基础URL
    this.baseURL = __DEV__ 
      ? 'http://localhost:8080/api/v1'
      : 'https://api.tourna.io/v1';

    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器 - 添加认证令牌
    this.instance.interceptors.request.use(
      async (config) => {
        const token = await storageService.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // 添加请求ID用于调试
        config.headers['X-Request-ID'] = this.generateRequestId();
        
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理通用错误和令牌刷新
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // 处理401未授权错误
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // 尝试刷新令牌
            const refreshToken = await storageService.getItem('refresh_token');
            if (refreshToken) {
              const response = await this.post('/auth/refresh', { refreshToken });
              
              // 保存新令牌
              await storageService.setItem('auth_token', response.data.data.token);
              await storageService.setItem('refresh_token', response.data.data.refreshToken);
              
              // 重试原始请求
              originalRequest.headers.Authorization = `Bearer ${response.data.data.token}`;
              return this.instance(originalRequest);
            }
          } catch (refreshError) {
            // 刷新失败，清除认证信息并跳转到登录页
            await this.clearAuthData();
            // 这里可以触发导航到登录页的事件
            console.error('Token refresh failed, redirecting to login');
          }
        }

        // 处理网络错误
        if (!error.response) {
          console.error('❌ Network Error:', error.message);
          throw new Error('网络连接失败，请检查网络设置');
        }

        // 处理其他HTTP错误
        const { status, data } = error.response;
        console.error(`❌ API Error: ${status} ${error.config.url}`, data);

        // 抛出格式化的错误
        throw this.formatError(error);
      }
    );
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.get<T>(url, config);
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.post<T>(url, data, config);
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.put<T>(url, data, config);
  }

  /**
   * PATCH请求
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.patch<T>(url, data, config);
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.delete<T>(url, config);
  }

  /**
   * 上传文件
   */
  async upload<T = any>(url: string, formData: FormData, onProgress?: (progress: number) => void): Promise<AxiosResponse<T>> {
    return this.instance.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  /**
   * 下载文件
   */
  async download(url: string, filename?: string): Promise<void> {
    const response = await this.instance.get(url, {
      responseType: 'blob',
    });

    // 创建下载链接
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string): void {
    this.instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * 清除认证令牌
   */
  clearAuthToken(): void {
    delete this.instance.defaults.headers.common['Authorization'];
  }

  /**
   * 清除认证数据
   */
  private async clearAuthData(): Promise<void> {
    await storageService.removeItem('auth_token');
    await storageService.removeItem('refresh_token');
    await storageService.removeItem('user_data');
    this.clearAuthToken();
  }

  /**
   * 格式化错误信息
   */
  private formatError(error: any): Error {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message);
    }
    
    if (error.response?.status) {
      const statusMessages: { [key: number]: string } = {
        400: '请求参数错误',
        401: '未授权访问',
        403: '禁止访问',
        404: '资源不存在',
        409: '资源冲突',
        422: '数据验证失败',
        429: '请求过于频繁',
        500: '服务器内部错误',
        502: '网关错误',
        503: '服务不可用',
        504: '网关超时',
      };
      
      const message = statusMessages[error.response.status] || `HTTP错误: ${error.response.status}`;
      return new Error(message);
    }
    
    return new Error(error.message || '未知错误');
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 获取基础URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }
}

export const apiClient = new ApiClient();
