import { apiClient } from './apiClient';

export interface UserStats {
  totalEarnings: number;
  availableBalance: number;
  pendingBalance: number;
  totalBookings: number;
  completedTours: number;
  rating: number;
  reviewCount: number;
  responseRate: number;
  isGuideVerified: boolean;
  guideLevel: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
}

export interface UserProfile {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  avatarUrl?: string;
  bio?: string;
  languages: string[];
  location?: {
    city: string;
    country: string;
  };
  isVerified: boolean;
  isGuide: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WithdrawalRequest {
  amount: number;
  method: 'bank_transfer' | 'paypal' | 'stripe';
  accountDetails: {
    accountNumber?: string;
    routingNumber?: string;
    paypalEmail?: string;
    stripeAccountId?: string;
  };
}

export interface WithdrawalHistory {
  id: number;
  amount: number;
  method: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedAt: string;
  processedAt?: string;
  fee: number;
  netAmount: number;
}

export interface EarningsHistory {
  id: number;
  bookingId: number;
  serviceName: string;
  customerName: string;
  amount: number;
  commission: number;
  netEarnings: number;
  date: string;
  status: 'pending' | 'completed';
}

export const userProfileService = {
  /**
   * 获取用户资料
   */
  async getUserProfile(): Promise<UserProfile> {
    try {
      const response = await apiClient.get<{success: boolean, data: UserProfile, message: string}>(
        '/users/profile'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw error;
    }
  },

  /**
   * 更新用户资料
   */
  async updateUserProfile(profile: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response = await apiClient.put<{success: boolean, data: UserProfile, message: string}>(
        '/users/profile',
        profile
      );
      return response.data.data;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  },

  /**
   * 获取用户统计数据
   */
  async getUserStats(): Promise<UserStats> {
    try {
      const response = await apiClient.get<{success: boolean, data: UserStats, message: string}>(
        '/users/stats'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      // 返回模拟数据
      return {
        totalEarnings: 2450.75,
        availableBalance: 1200.50,
        pendingBalance: 350.25,
        totalBookings: 47,
        completedTours: 43,
        rating: 4.8,
        reviewCount: 156,
        responseRate: 95,
        isGuideVerified: true,
        guideLevel: 'Gold',
      };
    }
  },

  /**
   * 申请提现
   */
  async requestWithdrawal(request: WithdrawalRequest): Promise<WithdrawalHistory> {
    try {
      const response = await apiClient.post<{success: boolean, data: WithdrawalHistory, message: string}>(
        '/users/withdrawals',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error requesting withdrawal:', error);
      throw error;
    }
  },

  /**
   * 获取提现历史
   */
  async getWithdrawalHistory(): Promise<WithdrawalHistory[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: WithdrawalHistory[], message: string}>(
        '/users/withdrawals'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching withdrawal history:', error);
      return [];
    }
  },

  /**
   * 获取收入历史
   */
  async getEarningsHistory(page: number = 0, size: number = 20): Promise<EarningsHistory[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: EarningsHistory[], message: string}>(
        `/users/earnings?page=${page}&size=${size}`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching earnings history:', error);
      return [];
    }
  },

  /**
   * 上传头像
   */
  async uploadAvatar(imageFile: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('avatar', imageFile);

      const response = await apiClient.upload<{success: boolean, data: {avatarUrl: string}, message: string}>(
        '/users/avatar',
        formData
      );
      return response.data.data.avatarUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw error;
    }
  },

  /**
   * 申请成为导游
   */
  async applyToBeGuide(application: {
    bio: string;
    languages: string[];
    specialties: string[];
    experience: string;
    certifications?: File[];
  }): Promise<void> {
    try {
      const formData = new FormData();
      formData.append('bio', application.bio);
      formData.append('languages', JSON.stringify(application.languages));
      formData.append('specialties', JSON.stringify(application.specialties));
      formData.append('experience', application.experience);
      
      if (application.certifications) {
        application.certifications.forEach((file, index) => {
          formData.append(`certifications`, file);
        });
      }

      await apiClient.upload('/users/apply-guide', formData);
    } catch (error) {
      console.error('Error applying to be guide:', error);
      throw error;
    }
  },

  /**
   * 获取导游申请状态
   */
  async getGuideApplicationStatus(): Promise<{
    status: 'none' | 'pending' | 'approved' | 'rejected';
    submittedAt?: string;
    reviewedAt?: string;
    feedback?: string;
  }> {
    try {
      const response = await apiClient.get<{success: boolean, data: any, message: string}>(
        '/users/guide-application-status'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching guide application status:', error);
      return { status: 'none' };
    }
  },

  /**
   * 删除账户
   */
  async deleteAccount(): Promise<void> {
    try {
      await apiClient.delete('/users/account');
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  },
};
