interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

class PerformanceService {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  /**
   * 初始化性能观察器
   */
  private initializeObservers(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        // 观察导航性能
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.logNavigationMetric(entry as PerformanceNavigationTiming);
          }
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navObserver);

        // 观察资源加载性能
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.logResourceMetric(entry as PerformanceResourceTiming);
          }
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
        this.observers.push(resourceObserver);

        // 观察用户交互性能
        const measureObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.logMeasureMetric(entry as PerformanceMeasure);
          }
        });
        measureObserver.observe({ entryTypes: ['measure'] });
        this.observers.push(measureObserver);
      } catch (error) {
        console.warn('Failed to initialize performance observers:', error);
      }
    }
  }

  /**
   * 开始性能测量
   */
  startMeasure(name: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      startTime: performance.now(),
      metadata,
    };
    
    this.metrics.set(name, metric);
    
    // 使用Performance API标记
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(`${name}-start`);
    }
  }

  /**
   * 结束性能测量
   */
  endMeasure(name: string, additionalMetadata?: Record<string, any>): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;
    metric.metadata = { ...metric.metadata, ...additionalMetadata };

    // 使用Performance API测量
    if (typeof performance !== 'undefined' && performance.mark && performance.measure) {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
    }

    // 记录到控制台（开发环境）
    if (__DEV__) {
      console.log(`⏱️ Performance: ${name} took ${duration.toFixed(2)}ms`, metric.metadata);
    }

    // 发送到分析服务
    this.sendMetricToAnalytics(metric);

    return duration;
  }

  /**
   * 测量异步操作
   */
  async measureAsync<T>(
    name: string,
    operation: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    this.startMeasure(name, metadata);
    
    try {
      const result = await operation();
      this.endMeasure(name, { success: true });
      return result;
    } catch (error) {
      this.endMeasure(name, { success: false, error: error.message });
      throw error;
    }
  }

  /**
   * 测量同步操作
   */
  measureSync<T>(
    name: string,
    operation: () => T,
    metadata?: Record<string, any>
  ): T {
    this.startMeasure(name, metadata);
    
    try {
      const result = operation();
      this.endMeasure(name, { success: true });
      return result;
    } catch (error) {
      this.endMeasure(name, { success: false, error: error.message });
      throw error;
    }
  }

  /**
   * 记录导航性能指标
   */
  private logNavigationMetric(entry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      domInteractive: entry.domInteractive - entry.navigationStart,
      firstPaint: this.getFirstPaint(),
      firstContentfulPaint: this.getFirstContentfulPaint(),
    };

    if (__DEV__) {
      console.log('📊 Navigation Performance:', metrics);
    }

    this.sendMetricToAnalytics({
      name: 'navigation',
      startTime: entry.navigationStart,
      endTime: entry.loadEventEnd,
      duration: entry.loadEventEnd - entry.navigationStart,
      metadata: metrics,
    });
  }

  /**
   * 记录资源加载性能指标
   */
  private logResourceMetric(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime;
    
    // 只记录较慢的资源加载
    if (duration > 100) {
      const metric = {
        name: 'resource-load',
        startTime: entry.startTime,
        endTime: entry.responseEnd,
        duration,
        metadata: {
          url: entry.name,
          type: this.getResourceType(entry.name),
          size: entry.transferSize,
        },
      };

      if (__DEV__) {
        console.log(`📦 Slow Resource: ${entry.name} took ${duration.toFixed(2)}ms`);
      }

      this.sendMetricToAnalytics(metric);
    }
  }

  /**
   * 记录自定义测量指标
   */
  private logMeasureMetric(entry: PerformanceMeasure): void {
    const metric = {
      name: entry.name,
      startTime: entry.startTime,
      endTime: entry.startTime + entry.duration,
      duration: entry.duration,
      metadata: { type: 'custom-measure' },
    };

    this.sendMetricToAnalytics(metric);
  }

  /**
   * 获取First Paint时间
   */
  private getFirstPaint(): number | null {
    if (typeof performance !== 'undefined' && performance.getEntriesByType) {
      const paintEntries = performance.getEntriesByType('paint');
      const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
      return firstPaint ? firstPaint.startTime : null;
    }
    return null;
  }

  /**
   * 获取First Contentful Paint时间
   */
  private getFirstContentfulPaint(): number | null {
    if (typeof performance !== 'undefined' && performance.getEntriesByType) {
      const paintEntries = performance.getEntriesByType('paint');
      const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      return fcp ? fcp.startTime : null;
    }
    return null;
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image';
    if (url.match(/\.(js)$/i)) return 'script';
    if (url.match(/\.(css)$/i)) return 'stylesheet';
    if (url.match(/\.(woff|woff2|ttf|eot)$/i)) return 'font';
    if (url.includes('/api/')) return 'api';
    return 'other';
  }

  /**
   * 获取内存使用情况
   */
  getMemoryInfo(): MemoryInfo | null {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      };
    }
    return null;
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * 清除所有性能指标
   */
  clearMetrics(): void {
    this.metrics.clear();
    
    if (typeof performance !== 'undefined' && performance.clearMarks && performance.clearMeasures) {
      performance.clearMarks();
      performance.clearMeasures();
    }
  }

  /**
   * 发送指标到分析服务
   */
  private sendMetricToAnalytics(metric: PerformanceMetric): void {
    // 这里可以集成第三方分析服务，如Google Analytics、Mixpanel等
    // 目前只在开发环境记录
    if (__DEV__ && metric.duration && metric.duration > 1000) {
      console.warn(`🐌 Slow Operation: ${metric.name} took ${metric.duration.toFixed(2)}ms`);
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.clearMetrics();
  }
}

// 创建全局性能服务实例
export const performanceService = new PerformanceService();

// 性能测量装饰器
export function measurePerformance(name?: string) {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    const measureName = name || `${target.constructor.name}.${propertyKey}`;
    
    descriptor.value = async function (...args: any[]) {
      return performanceService.measureAsync(
        measureName,
        () => originalMethod.apply(this, args),
        { method: propertyKey, args: args.length }
      );
    };
  };
}
