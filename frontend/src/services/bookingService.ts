import { apiClient } from './apiClient';
import { CACHE_KEYS } from './cacheService';

export interface TimeSlot {
  id: string;
  startTime: string; // ISO string
  endTime: string; // ISO string
  isAvailable: boolean;
  price: number;
  maxGroupSize: number;
  currentBookings: number;
}

export interface BookingRequest {
  serviceId: number;
  guideId: number;
  timeSlotId: string;
  date: string; // YYYY-MM-DD
  groupSize: number;
  specialRequests?: string;
  contactInfo: {
    name: string;
    email: string;
    phone: string;
  };
  preferences?: {
    language: string;
    accessibility?: string[];
    dietaryRestrictions?: string[];
  };
}

export interface BookingResponse {
  id: number;
  bookingNumber: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'in_progress';
  serviceId: number;
  serviceName: string;
  serviceImage: string;
  guideId: number;
  guideName: string;
  guideAvatar: string;
  guidePhone: string;
  date: string;
  startTime: string;
  endTime: string;
  duration: string;
  groupSize: number;
  totalPrice: number;
  currency: string;
  meetingPoint: string;
  specialRequests?: string;
  contactInfo: {
    name: string;
    email: string;
    phone: string;
  };
  preferences?: {
    language: string;
    accessibility?: string[];
    dietaryRestrictions?: string[];
  };
  createdAt: string;
  updatedAt: string;
  cancellationPolicy: string;
  refundPolicy: string;
}

export interface AvailabilityRequest {
  serviceId: number;
  date: string; // YYYY-MM-DD
  duration?: number; // minutes
}

export interface PriceCalculation {
  basePrice: number;
  groupSizeMultiplier: number;
  seasonalAdjustment: number;
  weekendSurcharge: number;
  serviceFee: number;
  taxes: number;
  totalPrice: number;
  currency: string;
  breakdown: {
    label: string;
    amount: number;
    type: 'base' | 'multiplier' | 'fee' | 'tax' | 'discount';
  }[];
}

export interface BookingFilters {
  status?: string[];
  dateFrom?: string;
  dateTo?: string;
  guideId?: number;
  serviceId?: number;
  page?: number;
  size?: number;
}

export const bookingService = {
  /**
   * 获取服务可用时间段
   */
  async getAvailableTimeSlots(request: AvailabilityRequest): Promise<TimeSlot[]> {
    try {
      const response = await apiClient.getCached<{success: boolean, data: TimeSlot[], message: string}>(
        `/services/${request.serviceId}/availability`,
        `${CACHE_KEYS.SERVICES}_availability`,
        {
          cacheParams: request,
          cacheTTL: 5 * 60 * 1000, // 5分钟缓存
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching available time slots:', error);
      // 返回模拟数据
      return this.getMockTimeSlots();
    }
  },

  /**
   * 获取模拟时间段数据
   */
  getMockTimeSlots(): TimeSlot[] {
    const now = new Date();
    const slots: TimeSlot[] = [];
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() + i);
      
      // 每天3个时间段
      const timeSlots = [
        { hour: 9, minute: 0 },
        { hour: 14, minute: 0 },
        { hour: 17, minute: 30 },
      ];
      
      timeSlots.forEach((time, index) => {
        const startTime = new Date(date);
        startTime.setHours(time.hour, time.minute, 0, 0);
        
        const endTime = new Date(startTime);
        endTime.setHours(startTime.getHours() + 3); // 3小时时长
        
        slots.push({
          id: `slot_${i}_${index}`,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          isAvailable: Math.random() > 0.3, // 70%可用率
          price: 45 + (index * 10), // 不同时段不同价格
          maxGroupSize: 8,
          currentBookings: Math.floor(Math.random() * 3),
        });
      });
    }
    
    return slots;
  },

  /**
   * 计算预订价格
   */
  async calculatePrice(
    serviceId: number,
    timeSlotId: string,
    groupSize: number,
    date: string
  ): Promise<PriceCalculation> {
    try {
      const response = await apiClient.post<{success: boolean, data: PriceCalculation, message: string}>(
        '/bookings/calculate-price',
        {
          serviceId,
          timeSlotId,
          groupSize,
          date,
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error calculating price:', error);
      // 返回模拟价格计算
      return this.getMockPriceCalculation(groupSize);
    }
  },

  /**
   * 获取模拟价格计算
   */
  getMockPriceCalculation(groupSize: number): PriceCalculation {
    const basePrice = 45;
    const groupSizeMultiplier = groupSize > 1 ? (groupSize - 1) * 0.8 : 0;
    const serviceFee = basePrice * 0.1;
    const taxes = (basePrice + groupSizeMultiplier + serviceFee) * 0.08;
    const totalPrice = basePrice + groupSizeMultiplier + serviceFee + taxes;

    return {
      basePrice,
      groupSizeMultiplier,
      seasonalAdjustment: 0,
      weekendSurcharge: 0,
      serviceFee,
      taxes,
      totalPrice,
      currency: 'USD',
      breakdown: [
        { label: 'Base Price', amount: basePrice, type: 'base' },
        { label: 'Additional Guests', amount: groupSizeMultiplier, type: 'multiplier' },
        { label: 'Service Fee', amount: serviceFee, type: 'fee' },
        { label: 'Taxes', amount: taxes, type: 'tax' },
      ],
    };
  },

  /**
   * 创建预订
   */
  async createBooking(request: BookingRequest): Promise<BookingResponse> {
    try {
      const response = await apiClient.post<{success: boolean, data: BookingResponse, message: string}>(
        '/bookings',
        request
      );
      
      // 清除相关缓存
      await this.clearBookingCaches(request.serviceId);
      
      return response.data.data;
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    }
  },

  /**
   * 获取用户预订列表
   */
  async getUserBookings(filters?: BookingFilters): Promise<{
    bookings: BookingResponse[];
    total: number;
    page: number;
    size: number;
  }> {
    try {
      const response = await apiClient.getCached<{success: boolean, data: any, message: string}>(
        '/bookings/my-bookings',
        'user_bookings',
        {
          cacheParams: filters,
          cacheTTL: 2 * 60 * 1000, // 2分钟缓存
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching user bookings:', error);
      return {
        bookings: this.getMockBookings(),
        total: 3,
        page: 0,
        size: 20,
      };
    }
  },

  /**
   * 获取模拟预订数据
   */
  getMockBookings(): BookingResponse[] {
    return [
      {
        id: 1,
        bookingNumber: 'TRN-2024-001',
        status: 'confirmed',
        serviceId: 1,
        serviceName: 'Gothic Quarter Walking Tour',
        serviceImage: 'https://via.placeholder.com/300x200',
        guideId: 101,
        guideName: 'Maria Santos',
        guideAvatar: 'https://via.placeholder.com/50',
        guidePhone: '+34 123 456 789',
        date: '2024-01-20',
        startTime: '10:00',
        endTime: '13:00',
        duration: '3 hours',
        groupSize: 2,
        totalPrice: 90,
        currency: 'USD',
        meetingPoint: 'Cathedral Square, Barcelona',
        contactInfo: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '****** 567 8900',
        },
        preferences: {
          language: 'English',
        },
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        cancellationPolicy: 'Free cancellation up to 24 hours before the tour',
        refundPolicy: 'Full refund for cancellations made 24+ hours in advance',
      },
      {
        id: 2,
        bookingNumber: 'TRN-2024-002',
        status: 'pending',
        serviceId: 2,
        serviceName: 'Tapas & Wine Experience',
        serviceImage: 'https://via.placeholder.com/300x200',
        guideId: 102,
        guideName: 'Ahmed Hassan',
        guideAvatar: 'https://via.placeholder.com/50',
        guidePhone: '+34 987 654 321',
        date: '2024-01-25',
        startTime: '18:00',
        endTime: '22:00',
        duration: '4 hours',
        groupSize: 4,
        totalPrice: 260,
        currency: 'USD',
        meetingPoint: 'Plaça de Catalunya, Barcelona',
        contactInfo: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '****** 567 8900',
        },
        preferences: {
          language: 'English',
          dietaryRestrictions: ['vegetarian'],
        },
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        cancellationPolicy: 'Free cancellation up to 48 hours before the tour',
        refundPolicy: 'Full refund for cancellations made 48+ hours in advance',
      },
    ];
  },

  /**
   * 取消预订
   */
  async cancelBooking(bookingId: number, reason?: string): Promise<BookingResponse> {
    try {
      const response = await apiClient.patch<{success: boolean, data: BookingResponse, message: string}>(
        `/bookings/${bookingId}/cancel`,
        { reason }
      );
      
      // 清除缓存
      await this.clearUserBookingCaches();
      
      return response.data.data;
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    }
  },

  /**
   * 获取预订详情
   */
  async getBookingDetails(bookingId: number): Promise<BookingResponse> {
    try {
      const response = await apiClient.get<{success: boolean, data: BookingResponse, message: string}>(
        `/bookings/${bookingId}`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching booking details:', error);
      throw error;
    }
  },

  /**
   * 清除预订相关缓存
   */
  async clearBookingCaches(serviceId?: number): Promise<void> {
    try {
      if (serviceId) {
        await apiClient.delete(`/cache/services/${serviceId}/availability`);
      }
      await this.clearUserBookingCaches();
    } catch (error) {
      console.error('Error clearing booking caches:', error);
    }
  },

  /**
   * 清除用户预订缓存
   */
  async clearUserBookingCaches(): Promise<void> {
    try {
      await apiClient.delete('/cache/user-bookings');
    } catch (error) {
      console.error('Error clearing user booking caches:', error);
    }
  },
};
