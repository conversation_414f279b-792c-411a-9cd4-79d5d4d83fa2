import { apiClient } from './apiClient';

export interface ServicePublishRequest {
  title: string;
  description: string;
  category: string;
  duration: string;
  price: number;
  currency: string;
  maxGroupSize: number;
  languages: string[];
  includesTransport: boolean;
  includesMeals: boolean;
  includesTickets: boolean;
  meetingPoint: string;
  cancellationPolicy: string;
  isActive: boolean;
  images?: string[];
  availableTimeSlots?: TimeSlot[];
}

export interface TimeSlot {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  isAvailable: boolean;
}

export interface ServiceResponse {
  id: number;
  guideId: number;
  title: string;
  description: string;
  category: string;
  duration: string;
  price: number;
  currency: string;
  maxGroupSize: number;
  languages: string[];
  includesTransport: boolean;
  includesMeals: boolean;
  includesTickets: boolean;
  meetingPoint: string;
  cancellationPolicy: string;
  isActive: boolean;
  images: string[];
  rating: number;
  reviewCount: number;
  bookingCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface GuideService {
  id: number;
  title: string;
  category: string;
  price: number;
  currency: string;
  isActive: boolean;
  bookingCount: number;
  rating: number;
  reviewCount: number;
  createdAt: string;
}

export const servicePublishService = {
  /**
   * 发布新服务
   */
  async publishService(request: ServicePublishRequest): Promise<ServiceResponse> {
    try {
      const response = await apiClient.post<{success: boolean, data: ServiceResponse, message: string}>(
        '/guides/services',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error publishing service:', error);
      throw error;
    }
  },

  /**
   * 更新服务
   */
  async updateService(serviceId: number, request: Partial<ServicePublishRequest>): Promise<ServiceResponse> {
    try {
      const response = await apiClient.put<{success: boolean, data: ServiceResponse, message: string}>(
        `/guides/services/${serviceId}`,
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error updating service:', error);
      throw error;
    }
  },

  /**
   * 获取导游的所有服务
   */
  async getGuideServices(): Promise<GuideService[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: GuideService[], message: string}>(
        '/guides/my-services'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching guide services:', error);
      throw error;
    }
  },

  /**
   * 删除服务
   */
  async deleteService(serviceId: number): Promise<void> {
    try {
      await apiClient.delete(`/guides/services/${serviceId}`);
    } catch (error) {
      console.error('Error deleting service:', error);
      throw error;
    }
  },

  /**
   * 切换服务状态
   */
  async toggleServiceStatus(serviceId: number, isActive: boolean): Promise<ServiceResponse> {
    try {
      const response = await apiClient.patch<{success: boolean, data: ServiceResponse, message: string}>(
        `/guides/services/${serviceId}/status`,
        { isActive }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error toggling service status:', error);
      throw error;
    }
  },

  /**
   * 上传服务图片
   */
  async uploadServiceImages(serviceId: number, images: File[]): Promise<string[]> {
    try {
      const formData = new FormData();
      images.forEach((image, index) => {
        formData.append(`images`, image);
      });

      const response = await apiClient.upload<{success: boolean, data: string[], message: string}>(
        `/guides/services/${serviceId}/images`,
        formData
      );
      return response.data.data;
    } catch (error) {
      console.error('Error uploading service images:', error);
      throw error;
    }
  },

  /**
   * 设置服务可用时间
   */
  async setServiceAvailability(serviceId: number, timeSlots: TimeSlot[]): Promise<void> {
    try {
      await apiClient.post(
        `/guides/services/${serviceId}/availability`,
        { timeSlots }
      );
    } catch (error) {
      console.error('Error setting service availability:', error);
      throw error;
    }
  },

  /**
   * 获取服务统计数据
   */
  async getServiceStats(serviceId: number): Promise<{
    totalBookings: number;
    totalRevenue: number;
    averageRating: number;
    recentBookings: any[];
  }> {
    try {
      const response = await apiClient.get<{success: boolean, data: any, message: string}>(
        `/guides/services/${serviceId}/stats`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching service stats:', error);
      throw error;
    }
  },
};
