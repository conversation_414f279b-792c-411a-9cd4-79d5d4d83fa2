import * as FileSystem from 'expo-file-system';
import * as Crypto from 'expo-crypto';
import { Image } from 'react-native';

interface ImageCacheConfig {
  maxCacheSize: number; // 最大缓存大小（字节）
  maxCacheAge: number; // 最大缓存时间（毫秒）
  compressionQuality: number; // 图片压缩质量 (0-1)
}

interface CachedImageInfo {
  uri: string;
  localPath: string;
  size: number;
  timestamp: number;
  lastAccessed: number;
}

class ImageCacheService {
  private config: ImageCacheConfig;
  private cacheDir: string;
  private cacheIndex: Map<string, CachedImageInfo>;
  private initialized: boolean = false;

  constructor(config: Partial<ImageCacheConfig> = {}) {
    this.config = {
      maxCacheSize: 100 * 1024 * 1024, // 100MB
      maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 7天
      compressionQuality: 0.8,
      ...config,
    };
    
    this.cacheDir = `${FileSystem.cacheDirectory}images/`;
    this.cacheIndex = new Map();
  }

  /**
   * 初始化缓存服务
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 确保缓存目录存在
      const dirInfo = await FileSystem.getInfoAsync(this.cacheDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.cacheDir, { intermediates: true });
      }

      // 加载缓存索引
      await this.loadCacheIndex();
      
      // 清理过期缓存
      await this.cleanupExpiredCache();
      
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize image cache:', error);
    }
  }

  /**
   * 生成缓存文件名
   */
  private async generateCacheKey(uri: string): Promise<string> {
    const hash = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      uri,
      { encoding: Crypto.CryptoEncoding.HEX }
    );
    return hash;
  }

  /**
   * 加载缓存索引
   */
  private async loadCacheIndex(): Promise<void> {
    try {
      const indexPath = `${this.cacheDir}index.json`;
      const indexInfo = await FileSystem.getInfoAsync(indexPath);
      
      if (indexInfo.exists) {
        const indexContent = await FileSystem.readAsStringAsync(indexPath);
        const indexData = JSON.parse(indexContent);
        
        for (const [key, value] of Object.entries(indexData)) {
          this.cacheIndex.set(key, value as CachedImageInfo);
        }
      }
    } catch (error) {
      console.error('Failed to load cache index:', error);
      this.cacheIndex.clear();
    }
  }

  /**
   * 保存缓存索引
   */
  private async saveCacheIndex(): Promise<void> {
    try {
      const indexPath = `${this.cacheDir}index.json`;
      const indexData = Object.fromEntries(this.cacheIndex);
      await FileSystem.writeAsStringAsync(indexPath, JSON.stringify(indexData));
    } catch (error) {
      console.error('Failed to save cache index:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  private async cleanupExpiredCache(): Promise<void> {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, info] of this.cacheIndex.entries()) {
      if (now - info.timestamp > this.config.maxCacheAge) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      await this.deleteCachedImage(key);
    }

    // 如果缓存大小超过限制，删除最旧的文件
    await this.enforceMaxCacheSize();
  }

  /**
   * 强制执行最大缓存大小限制
   */
  private async enforceMaxCacheSize(): Promise<void> {
    const totalSize = Array.from(this.cacheIndex.values())
      .reduce((sum, info) => sum + info.size, 0);

    if (totalSize <= this.config.maxCacheSize) return;

    // 按最后访问时间排序，删除最旧的文件
    const sortedEntries = Array.from(this.cacheIndex.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    let currentSize = totalSize;
    for (const [key, info] of sortedEntries) {
      if (currentSize <= this.config.maxCacheSize) break;
      
      await this.deleteCachedImage(key);
      currentSize -= info.size;
    }
  }

  /**
   * 删除缓存的图片
   */
  private async deleteCachedImage(key: string): Promise<void> {
    try {
      const info = this.cacheIndex.get(key);
      if (info) {
        const fileInfo = await FileSystem.getInfoAsync(info.localPath);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(info.localPath);
        }
        this.cacheIndex.delete(key);
      }
    } catch (error) {
      console.error('Failed to delete cached image:', error);
    }
  }

  /**
   * 下载并缓存图片
   */
  private async downloadAndCacheImage(uri: string, cacheKey: string): Promise<string | null> {
    try {
      const localPath = `${this.cacheDir}${cacheKey}`;
      
      // 下载图片
      const downloadResult = await FileSystem.downloadAsync(uri, localPath);
      
      if (downloadResult.status === 200) {
        // 获取文件信息
        const fileInfo = await FileSystem.getInfoAsync(localPath);
        
        if (fileInfo.exists && fileInfo.size) {
          // 更新缓存索引
          const cacheInfo: CachedImageInfo = {
            uri,
            localPath,
            size: fileInfo.size,
            timestamp: Date.now(),
            lastAccessed: Date.now(),
          };
          
          this.cacheIndex.set(cacheKey, cacheInfo);
          await this.saveCacheIndex();
          
          return localPath;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Failed to download and cache image:', error);
      return null;
    }
  }

  /**
   * 获取缓存的图片URI
   */
  async getCachedImageUri(uri: string): Promise<string> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      const cacheKey = await this.generateCacheKey(uri);
      const cachedInfo = this.cacheIndex.get(cacheKey);

      if (cachedInfo) {
        // 检查文件是否仍然存在
        const fileInfo = await FileSystem.getInfoAsync(cachedInfo.localPath);
        
        if (fileInfo.exists) {
          // 更新最后访问时间
          cachedInfo.lastAccessed = Date.now();
          this.cacheIndex.set(cacheKey, cachedInfo);
          
          return cachedInfo.localPath;
        } else {
          // 文件不存在，从索引中删除
          this.cacheIndex.delete(cacheKey);
        }
      }

      // 下载并缓存图片
      const localPath = await this.downloadAndCacheImage(uri, cacheKey);
      return localPath || uri; // 如果缓存失败，返回原始URI
      
    } catch (error) {
      console.error('Failed to get cached image:', error);
      return uri; // 出错时返回原始URI
    }
  }

  /**
   * 预加载图片
   */
  async preloadImage(uri: string): Promise<void> {
    try {
      await this.getCachedImageUri(uri);
    } catch (error) {
      console.error('Failed to preload image:', error);
    }
  }

  /**
   * 预加载多张图片
   */
  async preloadImages(uris: string[]): Promise<void> {
    const promises = uris.map(uri => this.preloadImage(uri));
    await Promise.allSettled(promises);
  }

  /**
   * 清空所有缓存
   */
  async clearCache(): Promise<void> {
    try {
      // 删除所有缓存文件
      const dirInfo = await FileSystem.getInfoAsync(this.cacheDir);
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(this.cacheDir);
        await FileSystem.makeDirectoryAsync(this.cacheDir, { intermediates: true });
      }
      
      // 清空索引
      this.cacheIndex.clear();
      await this.saveCacheIndex();
    } catch (error) {
      console.error('Failed to clear image cache:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    totalSize: number;
    totalFiles: number;
    oldestFile: number | null;
    newestFile: number | null;
  } {
    const infos = Array.from(this.cacheIndex.values());
    
    return {
      totalSize: infos.reduce((sum, info) => sum + info.size, 0),
      totalFiles: infos.length,
      oldestFile: infos.length > 0 ? Math.min(...infos.map(info => info.timestamp)) : null,
      newestFile: infos.length > 0 ? Math.max(...infos.map(info => info.timestamp)) : null,
    };
  }

  /**
   * 检查图片是否已缓存
   */
  async isImageCached(uri: string): Promise<boolean> {
    try {
      const cacheKey = await this.generateCacheKey(uri);
      const cachedInfo = this.cacheIndex.get(cacheKey);
      
      if (cachedInfo) {
        const fileInfo = await FileSystem.getInfoAsync(cachedInfo.localPath);
        return fileInfo.exists;
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }
}

// 创建全局图片缓存服务实例
export const imageCacheService = new ImageCacheService({
  maxCacheSize: 100 * 1024 * 1024, // 100MB
  maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 7天
  compressionQuality: 0.8,
});

// 缓存图片组件的高阶组件
export function withImageCache<P extends { source: { uri: string } }>(
  WrappedComponent: React.ComponentType<P>
) {
  return function CachedImageComponent(props: P) {
    const [cachedUri, setCachedUri] = React.useState<string>(props.source.uri);

    React.useEffect(() => {
      const loadCachedImage = async () => {
        try {
          const cached = await imageCacheService.getCachedImageUri(props.source.uri);
          setCachedUri(cached);
        } catch (error) {
          console.error('Failed to load cached image:', error);
        }
      };

      loadCachedImage();
    }, [props.source.uri]);

    return <WrappedComponent {...props} source={{ uri: cachedUri }} />;
  };
}
