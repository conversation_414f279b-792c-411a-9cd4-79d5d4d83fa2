import AsyncStorage from '@react-native-async-storage/async-storage';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresIn: number; // 过期时间（毫秒）
}

interface CacheConfig {
  defaultTTL: number; // 默认缓存时间（毫秒）
  maxSize: number; // 最大缓存项数
}

class CacheService {
  private config: CacheConfig;
  private memoryCache: Map<string, CacheItem<any>>;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxSize: 100,
      ...config,
    };
    this.memoryCache = new Map();
  }

  /**
   * 生成缓存键
   */
  private generateKey(prefix: string, params?: Record<string, any>): string {
    if (!params) return prefix;
    
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${JSON.stringify(params[key])}`)
      .join('&');
    
    return `${prefix}?${sortedParams}`;
  }

  /**
   * 检查缓存项是否过期
   */
  private isExpired(item: CacheItem<any>): boolean {
    return Date.now() - item.timestamp > item.expiresIn;
  }

  /**
   * 清理过期的内存缓存
   */
  private cleanupMemoryCache(): void {
    for (const [key, item] of this.memoryCache.entries()) {
      if (this.isExpired(item)) {
        this.memoryCache.delete(key);
      }
    }

    // 如果缓存项过多，删除最旧的项
    if (this.memoryCache.size > this.config.maxSize) {
      const entries = Array.from(this.memoryCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toDelete = entries.slice(0, entries.length - this.config.maxSize);
      toDelete.forEach(([key]) => this.memoryCache.delete(key));
    }
  }

  /**
   * 从内存缓存获取数据
   */
  async getFromMemory<T>(key: string): Promise<T | null> {
    const item = this.memoryCache.get(key);
    
    if (!item) return null;
    
    if (this.isExpired(item)) {
      this.memoryCache.delete(key);
      return null;
    }
    
    return item.data;
  }

  /**
   * 存储到内存缓存
   */
  async setToMemory<T>(key: string, data: T, ttl?: number): Promise<void> {
    const expiresIn = ttl || this.config.defaultTTL;
    
    this.memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      expiresIn,
    });
    
    this.cleanupMemoryCache();
  }

  /**
   * 从持久化存储获取数据
   */
  async getFromStorage<T>(key: string): Promise<T | null> {
    try {
      const stored = await AsyncStorage.getItem(`cache_${key}`);
      if (!stored) return null;
      
      const item: CacheItem<T> = JSON.parse(stored);
      
      if (this.isExpired(item)) {
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }
      
      return item.data;
    } catch (error) {
      console.error('Failed to get from storage cache:', error);
      return null;
    }
  }

  /**
   * 存储到持久化存储
   */
  async setToStorage<T>(key: string, data: T, ttl?: number): Promise<void> {
    try {
      const expiresIn = ttl || this.config.defaultTTL;
      
      const item: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiresIn,
      };
      
      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(item));
    } catch (error) {
      console.error('Failed to set to storage cache:', error);
    }
  }

  /**
   * 获取缓存数据（先检查内存，再检查存储）
   */
  async get<T>(prefix: string, params?: Record<string, any>): Promise<T | null> {
    const key = this.generateKey(prefix, params);
    
    // 先检查内存缓存
    const memoryData = await this.getFromMemory<T>(key);
    if (memoryData !== null) {
      return memoryData;
    }
    
    // 再检查持久化存储
    const storageData = await this.getFromStorage<T>(key);
    if (storageData !== null) {
      // 将数据加载到内存缓存
      await this.setToMemory(key, storageData);
      return storageData;
    }
    
    return null;
  }

  /**
   * 设置缓存数据（同时存储到内存和持久化存储）
   */
  async set<T>(
    prefix: string, 
    data: T, 
    params?: Record<string, any>, 
    options?: { ttl?: number; memoryOnly?: boolean }
  ): Promise<void> {
    const key = this.generateKey(prefix, params);
    const ttl = options?.ttl;
    
    // 存储到内存缓存
    await this.setToMemory(key, data, ttl);
    
    // 如果不是仅内存模式，也存储到持久化存储
    if (!options?.memoryOnly) {
      await this.setToStorage(key, data, ttl);
    }
  }

  /**
   * 删除缓存
   */
  async delete(prefix: string, params?: Record<string, any>): Promise<void> {
    const key = this.generateKey(prefix, params);
    
    // 从内存缓存删除
    this.memoryCache.delete(key);
    
    // 从持久化存储删除
    try {
      await AsyncStorage.removeItem(`cache_${key}`);
    } catch (error) {
      console.error('Failed to delete from storage cache:', error);
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    // 清空内存缓存
    this.memoryCache.clear();
    
    // 清空持久化存储中的缓存
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      await AsyncStorage.multiRemove(cacheKeys);
    } catch (error) {
      console.error('Failed to clear storage cache:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): {
    memorySize: number;
    memoryKeys: string[];
  } {
    return {
      memorySize: this.memoryCache.size,
      memoryKeys: Array.from(this.memoryCache.keys()),
    };
  }

  /**
   * 缓存装饰器 - 用于API调用
   */
  cached<T extends (...args: any[]) => Promise<any>>(
    prefix: string,
    ttl?: number,
    keyGenerator?: (...args: Parameters<T>) => Record<string, any>
  ) {
    return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
      const originalMethod = descriptor.value;
      
      descriptor.value = async function (...args: Parameters<T>) {
        const params = keyGenerator ? keyGenerator(...args) : { args: JSON.stringify(args) };
        
        // 尝试从缓存获取
        const cached = await cacheService.get<Awaited<ReturnType<T>>>(prefix, params);
        if (cached !== null) {
          return cached;
        }
        
        // 调用原方法
        const result = await originalMethod.apply(this, args);
        
        // 缓存结果
        await cacheService.set(prefix, result, params, { ttl });
        
        return result;
      };
    };
  }
}

// 创建全局缓存服务实例
export const cacheService = new CacheService({
  defaultTTL: 5 * 60 * 1000, // 5分钟
  maxSize: 100,
});

// 预定义的缓存键
export const CACHE_KEYS = {
  GUIDES: 'guides',
  SERVICES: 'services',
  USER_PROFILE: 'user_profile',
  USER_STATS: 'user_stats',
  CHAT_SESSIONS: 'chat_sessions',
  CHAT_MESSAGES: 'chat_messages',
  POPULAR_SERVICES: 'popular_services',
  RECOMMENDED_GUIDES: 'recommended_guides',
  SEARCH_RESULTS: 'search_results',
} as const;
