import { apiClient } from './apiClient';
import { storageService } from './storageService';
import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  RefreshTokenResponse,
  User,
} from '@/types/user';

/**
 * 认证服务
 */
class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_KEY = 'user_data';

  /**
   * 用户登录
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // 临时使用模拟数据，直到后端修复
      if (process.env.NODE_ENV === 'development') {
        // 模拟登录验证
        if (credentials.email === '<EMAIL>' && credentials.password === 'demo123') {
          const mockResponse: LoginResponse = {
            user: {
              id: 1,
              username: 'demo_user',
              email: credentials.email,
              firstName: 'Demo',
              lastName: 'User',
              phone: '+1234567890',
              fullName: 'Demo User',
              avatarUrl: 'https://via.placeholder.com/100',
              languagePreference: 'en',
              kycStatus: 'VERIFIED',
              kycLevel: 2,
              walletAddress: null,
              isActive: true,
              roles: ['CUSTOMER'],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            token: 'mock-jwt-token-' + Date.now(),
            refreshToken: 'mock-refresh-token-' + Date.now(),
            expiresIn: 86400,
          };

          // 保存认证信息到本地存储
          await this.saveAuthData(mockResponse);

          return mockResponse;
        } else {
          throw new Error('Invalid credentials');
        }
      }

      const response = await apiClient.post<LoginResponse>('/auth/login', credentials);

      // 保存认证信息到本地存储
      await this.saveAuthData(response.data);

      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * 用户注册
   */
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    try {
      // 临时使用模拟数据，直到后端修复
      if (process.env.NODE_ENV === 'development') {
        const mockResponse: RegisterResponse = {
          user: {
            id: Math.floor(Math.random() * 1000),
            username: userData.username,
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            phone: userData.phone || '',
            fullName: `${userData.firstName} ${userData.lastName}`,
            avatarUrl: null,
            languagePreference: 'en',
            kycStatus: 'PENDING',
            kycLevel: 0,
            walletAddress: null,
            isActive: true,
            roles: ['CUSTOMER'],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          token: 'mock-jwt-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expiresIn: 86400,
        };

        // 保存认证信息到本地存储
        await this.saveAuthData(mockResponse);

        return mockResponse;
      }

      const response = await apiClient.post<RegisterResponse>('/auth/register', userData);

      // 保存认证信息到本地存储
      await this.saveAuthData(response.data);

      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * 社交登录
   */
  async socialLogin(socialData: {
    provider: 'google' | 'apple';
    socialId: string;
    email: string;
    name: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
  }): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/social-login', socialData);

      // 保存认证信息到本地存储
      await this.saveAuthData(response.data);

      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      // 调用后端登出接口
      await apiClient.post('/auth/logout');
    } catch (error) {
      // 即使后端登出失败，也要清除本地数据
      console.warn('Backend logout failed:', error);
    } finally {
      // 清除本地存储的认证信息
      await this.clearAuthData();
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      const response = await apiClient.post<RefreshTokenResponse>('/auth/refresh', {
        refreshToken,
      });
      
      // 更新本地存储的令牌
      await storageService.setItem(this.TOKEN_KEY, response.data.token);
      await storageService.setItem(this.REFRESH_TOKEN_KEY, response.data.refreshToken);
      
      return response.data;
    } catch (error) {
      // 刷新失败，清除认证信息
      await this.clearAuthData();
      throw this.handleAuthError(error);
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await apiClient.get<{data: User}>('/auth/me');

      // 更新本地存储的用户信息
      await storageService.setItem(this.USER_KEY, JSON.stringify(response.data.data));

      return response.data.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * 检查用户名是否可用
   */
  async checkUsernameAvailability(username: string): Promise<boolean> {
    try {
      const response = await apiClient.get<boolean>('/users/check-username', {
        params: { username },
      });
      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * 检查邮箱是否可用
   */
  async checkEmailAvailability(email: string): Promise<boolean> {
    try {
      const response = await apiClient.get<boolean>('/users/check-email', {
        params: { email },
      });
      return response.data;
    } catch (error) {
      throw this.handleAuthError(error);
    }
  }

  /**
   * 从本地存储获取认证令牌
   */
  async getToken(): Promise<string | null> {
    return await storageService.getItem(this.TOKEN_KEY);
  }

  /**
   * 从本地存储获取刷新令牌
   */
  async getRefreshToken(): Promise<string | null> {
    return await storageService.getItem(this.REFRESH_TOKEN_KEY);
  }

  /**
   * 从本地存储获取用户信息
   */
  async getStoredUser(): Promise<User | null> {
    try {
      const userData = await storageService.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Failed to parse stored user data:', error);
      return null;
    }
  }

  /**
   * 检查是否已认证
   */
  async isAuthenticated(): Promise<boolean> {
    const token = await this.getToken();
    return !!token;
  }

  /**
   * 保存认证数据到本地存储
   */
  private async saveAuthData(authData: LoginResponse | RegisterResponse): Promise<void> {
    await Promise.all([
      storageService.setItem(this.TOKEN_KEY, authData.token),
      storageService.setItem(this.REFRESH_TOKEN_KEY, authData.refreshToken),
      storageService.setItem(this.USER_KEY, JSON.stringify(authData.user)),
    ]);
  }

  /**
   * 清除本地存储的认证数据
   */
  private async clearAuthData(): Promise<void> {
    await Promise.all([
      storageService.removeItem(this.TOKEN_KEY),
      storageService.removeItem(this.REFRESH_TOKEN_KEY),
      storageService.removeItem(this.USER_KEY),
    ]);
  }

  /**
   * 处理认证错误
   */
  private handleAuthError(error: any): Error {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message);
    }
    if (error.message) {
      return new Error(error.message);
    }
    return new Error('Authentication failed');
  }
}

export const authService = new AuthService();
