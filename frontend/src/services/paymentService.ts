import { apiClient } from './apiClient';

export interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
  isDefault: boolean;
  card?: {
    brand: string;
    last4: string;
    expiryMonth: number;
    expiryYear: number;
    holderName: string;
  };
  paypal?: {
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled';
  bookingId: number;
  metadata: Record<string, any>;
}

export interface PaymentRequest {
  bookingId: number;
  paymentMethodId?: string;
  amount: number;
  currency: string;
  savePaymentMethod?: boolean;
  returnUrl?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentIntentId: string;
  status: string;
  error?: {
    code: string;
    message: string;
    type: string;
  };
}

export interface RefundRequest {
  paymentIntentId: string;
  amount?: number; // 部分退款金额，不提供则全额退款
  reason?: string;
}

export interface RefundResult {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed';
  reason?: string;
  createdAt: string;
}

export interface PaymentHistory {
  id: string;
  bookingId: number;
  serviceName: string;
  amount: number;
  currency: string;
  status: 'pending' | 'succeeded' | 'failed' | 'refunded' | 'partially_refunded';
  paymentMethod: {
    type: string;
    last4?: string;
    brand?: string;
  };
  createdAt: string;
  refunds?: RefundResult[];
}

export const paymentService = {
  /**
   * 获取用户的支付方式
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const response = await apiClient.get<{success: boolean, data: PaymentMethod[], message: string}>(
        '/payments/methods'
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      // 返回模拟数据
      return this.getMockPaymentMethods();
    }
  },

  /**
   * 获取模拟支付方式
   */
  getMockPaymentMethods(): PaymentMethod[] {
    return [
      {
        id: 'pm_1',
        type: 'card',
        isDefault: true,
        card: {
          brand: 'visa',
          last4: '4242',
          expiryMonth: 12,
          expiryYear: 2025,
          holderName: 'John Doe',
        },
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'pm_2',
        type: 'paypal',
        isDefault: false,
        paypal: {
          email: '<EMAIL>',
        },
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
      },
    ];
  },

  /**
   * 添加新的支付方式
   */
  async addPaymentMethod(paymentMethodData: {
    type: 'card' | 'paypal';
    token: string; // Stripe token or PayPal token
    setAsDefault?: boolean;
  }): Promise<PaymentMethod> {
    try {
      const response = await apiClient.post<{success: boolean, data: PaymentMethod, message: string}>(
        '/payments/methods',
        paymentMethodData
      );
      return response.data.data;
    } catch (error) {
      console.error('Error adding payment method:', error);
      throw error;
    }
  },

  /**
   * 删除支付方式
   */
  async deletePaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      await apiClient.delete(`/payments/methods/${paymentMethodId}`);
    } catch (error) {
      console.error('Error deleting payment method:', error);
      throw error;
    }
  },

  /**
   * 设置默认支付方式
   */
  async setDefaultPaymentMethod(paymentMethodId: string): Promise<void> {
    try {
      await apiClient.patch(`/payments/methods/${paymentMethodId}/default`);
    } catch (error) {
      console.error('Error setting default payment method:', error);
      throw error;
    }
  },

  /**
   * 创建支付意图
   */
  async createPaymentIntent(request: PaymentRequest): Promise<PaymentIntent> {
    try {
      const response = await apiClient.post<{success: boolean, data: PaymentIntent, message: string}>(
        '/payments/intents',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  },

  /**
   * 确认支付
   */
  async confirmPayment(paymentIntentId: string, paymentMethodId?: string): Promise<PaymentResult> {
    try {
      const response = await apiClient.post<{success: boolean, data: PaymentResult, message: string}>(
        `/payments/intents/${paymentIntentId}/confirm`,
        { paymentMethodId }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error confirming payment:', error);
      throw error;
    }
  },

  /**
   * 处理支付（完整流程）
   */
  async processPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // 1. 创建支付意图
      const paymentIntent = await this.createPaymentIntent(request);
      
      // 2. 确认支付
      const result = await this.confirmPayment(paymentIntent.id, request.paymentMethodId);
      
      return result;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  },

  /**
   * 申请退款
   */
  async requestRefund(request: RefundRequest): Promise<RefundResult> {
    try {
      const response = await apiClient.post<{success: boolean, data: RefundResult, message: string}>(
        '/payments/refunds',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error requesting refund:', error);
      throw error;
    }
  },

  /**
   * 获取支付历史
   */
  async getPaymentHistory(page: number = 0, size: number = 20): Promise<{
    payments: PaymentHistory[];
    total: number;
    page: number;
    size: number;
  }> {
    try {
      const response = await apiClient.get<{success: boolean, data: any, message: string}>(
        `/payments/history?page=${page}&size=${size}`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      return {
        payments: this.getMockPaymentHistory(),
        total: 2,
        page: 0,
        size: 20,
      };
    }
  },

  /**
   * 获取模拟支付历史
   */
  getMockPaymentHistory(): PaymentHistory[] {
    return [
      {
        id: 'pi_1',
        bookingId: 1,
        serviceName: 'Gothic Quarter Walking Tour',
        amount: 90,
        currency: 'USD',
        status: 'succeeded',
        paymentMethod: {
          type: 'card',
          last4: '4242',
          brand: 'visa',
        },
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'pi_2',
        bookingId: 2,
        serviceName: 'Tapas & Wine Experience',
        amount: 260,
        currency: 'USD',
        status: 'succeeded',
        paymentMethod: {
          type: 'paypal',
        },
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      },
    ];
  },

  /**
   * 验证支付状态
   */
  async verifyPaymentStatus(paymentIntentId: string): Promise<PaymentResult> {
    try {
      const response = await apiClient.get<{success: boolean, data: PaymentResult, message: string}>(
        `/payments/intents/${paymentIntentId}/status`
      );
      return response.data.data;
    } catch (error) {
      console.error('Error verifying payment status:', error);
      throw error;
    }
  },

  /**
   * 检查Apple Pay可用性
   */
  async isApplePayAvailable(): Promise<boolean> {
    try {
      // 在实际应用中，这里会检查设备和Stripe的Apple Pay支持
      return Promise.resolve(true);
    } catch (error) {
      return false;
    }
  },

  /**
   * 检查Google Pay可用性
   */
  async isGooglePayAvailable(): Promise<boolean> {
    try {
      // 在实际应用中，这里会检查设备和Stripe的Google Pay支持
      return Promise.resolve(true);
    } catch (error) {
      return false;
    }
  },

  /**
   * 处理Apple Pay支付
   */
  async processApplePayPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // 在实际应用中，这里会集成Apple Pay SDK
      const response = await apiClient.post<{success: boolean, data: PaymentResult, message: string}>(
        '/payments/apple-pay',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error processing Apple Pay payment:', error);
      throw error;
    }
  },

  /**
   * 处理Google Pay支付
   */
  async processGooglePayPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      // 在实际应用中，这里会集成Google Pay SDK
      const response = await apiClient.post<{success: boolean, data: PaymentResult, message: string}>(
        '/payments/google-pay',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error processing Google Pay payment:', error);
      throw error;
    }
  },
};
