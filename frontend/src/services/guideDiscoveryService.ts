import { apiClient } from './apiClient';

export interface GuideDiscoveryRequest {
  latitude?: number;
  longitude?: number;
  radiusKm?: number;
  specialties?: string[];
  languages?: string[];
  minRating?: number;
  maxHourlyRate?: number;
  onlineOnly?: boolean;
  verifiedOnly?: boolean;
  sortBy?: 'DISTANCE' | 'RATING' | 'HOURLY_RATE' | 'RESPONSE_TIME' | 'CREATED_AT';
  sortDirection?: 'ASC' | 'DESC';
  page?: number;
  size?: number;
}

export interface LocationInfo {
  city: string;
  country: string;
  address: string;
  latitude: number;
  longitude: number;
}

export interface PopularService {
  id: number;
  title: string;
  price: number;
  duration: string;
  category: string;
  rating: number;
  bookingCount: number;
}

export interface GuideDiscoveryResponse {
  id: number;
  userId: number;
  name: string;
  avatarUrl: string;
  rating: number;
  reviewCount: number;
  distance: number;
  isOnline: boolean;
  isVerified: boolean;
  specialties: string[];
  hourlyRate: number;
  currency: string;
  languages: string[];
  responseTime: string;
  location: LocationInfo;
  popularServices: PopularService[];
  bio: string;
  completedTours: number;
  responseRate: number;
  lastActiveAt: string;
}

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

export const guideDiscoveryService = {
  /**
   * 发现附近的导游
   */
  async discoverGuides(request: GuideDiscoveryRequest): Promise<PageResponse<GuideDiscoveryResponse>> {
    try {
      const response = await apiClient.post<{success: boolean, data: PageResponse<GuideDiscoveryResponse>, message: string}>(
        '/guides/discover',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error discovering guides:', error);
      throw error;
    }
  },

  /**
   * 获取导游详细信息
   */
  async getGuideDetails(guideId: number): Promise<GuideDiscoveryResponse> {
    try {
      const response = await apiClient.get<GuideDiscoveryResponse>(`/guides/${guideId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching guide details:', error);
      throw error;
    }
  },

  /**
   * 获取导游可用时间
   */
  async getGuideAvailability(
    guideId: number,
    startDate?: string,
    endDate?: string
  ): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiClient.get(
        `/guides/${guideId}/availability?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching guide availability:', error);
      throw error;
    }
  },

  /**
   * 模拟获取用户位置
   */
  async getCurrentLocation(): Promise<{ latitude: number; longitude: number }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          // 返回默认位置（巴塞罗那）
          resolve({
            latitude: 41.3851,
            longitude: 2.1734,
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5分钟缓存
        }
      );
    });
  },

  /**
   * 根据地址获取坐标（地理编码）
   */
  async geocodeAddress(address: string): Promise<{ latitude: number; longitude: number }> {
    // 在实际应用中，这里会调用地理编码API（如Google Maps Geocoding API）
    // 现在返回模拟数据
    const mockLocations: Record<string, { latitude: number; longitude: number }> = {
      'Barcelona, Spain': { latitude: 41.3851, longitude: 2.1734 },
      'Madrid, Spain': { latitude: 40.4168, longitude: -3.7038 },
      'Paris, France': { latitude: 48.8566, longitude: 2.3522 },
      'London, UK': { latitude: 51.5074, longitude: -0.1278 },
      'New York, USA': { latitude: 40.7128, longitude: -74.0060 },
    };

    return mockLocations[address] || mockLocations['Barcelona, Spain'];
  },

  /**
   * 计算两点之间的距离（公里）
   */
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return Math.round(distance * 10) / 10; // 保留一位小数
  },

  /**
   * 角度转弧度
   */
  deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  },
};

export default guideDiscoveryService;
