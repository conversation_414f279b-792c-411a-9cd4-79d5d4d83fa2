import { apiClient } from './apiClient';
import { CACHE_KEYS } from './cacheService';

export interface GuideDiscoveryRequest {
  latitude?: number;
  longitude?: number;
  radiusKm?: number;
  specialties?: string[];
  languages?: string[];
  minRating?: number;
  maxHourlyRate?: number;
  onlineOnly?: boolean;
  verifiedOnly?: boolean;
  sortBy?: 'DISTANCE' | 'RATING' | 'HOURLY_RATE' | 'RESPONSE_TIME' | 'CREATED_AT';
  sortDirection?: 'ASC' | 'DESC';
  page?: number;
  size?: number;
}

export interface LocationInfo {
  city: string;
  country: string;
  address: string;
  latitude: number;
  longitude: number;
}

export interface PopularService {
  id: number;
  title: string;
  price: number;
  duration: string;
  category: string;
  rating: number;
  bookingCount: number;
}

export interface GuideDiscoveryResponse {
  id: number;
  userId: number;
  name: string;
  avatarUrl: string;
  rating: number;
  reviewCount: number;
  distance: number;
  isOnline: boolean;
  isVerified: boolean;
  specialties: string[];
  hourlyRate: number;
  currency: string;
  languages: string[];
  responseTime: string;
  location: LocationInfo;
  popularServices: PopularService[];
  bio: string;
  completedTours: number;
  responseRate: number;
  lastActiveAt: string;
}

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

export const guideDiscoveryService = {
  /**
   * 发现附近的导游
   */
  async discoverGuides(request: GuideDiscoveryRequest): Promise<PageResponse<GuideDiscoveryResponse>> {
    try {
      const response = await apiClient.post<{success: boolean, data: PageResponse<GuideDiscoveryResponse>, message: string}>(
        '/guides/discover',
        request
      );
      return response.data.data;
    } catch (error) {
      console.error('Error discovering guides:', error);
      throw error;
    }
  },

  /**
   * 获取推荐导游（带缓存）
   */
  async getRecommendedGuides(limit: number = 10): Promise<GuideDiscoveryResponse[]> {
    try {
      const response = await apiClient.getCached<{success: boolean, data: GuideDiscoveryResponse[], message: string}>(
        '/guides/recommended',
        CACHE_KEYS.RECOMMENDED_GUIDES,
        {
          cacheParams: { limit },
          cacheTTL: 10 * 60 * 1000, // 10分钟缓存
        }
      );
      return response.data.data;
    } catch (error) {
      console.error('Error fetching recommended guides:', error);
      // 返回模拟数据作为降级
      return this.getMockRecommendedGuides(limit);
    }
  },

  /**
   * 获取模拟推荐导游数据
   */
  getMockRecommendedGuides(limit: number = 10): GuideDiscoveryResponse[] {
    const mockGuides: GuideDiscoveryResponse[] = [
      {
        id: 1,
        name: 'Maria Santos',
        bio: 'Passionate local guide with 5+ years of experience showing visitors the best of Barcelona.',
        rating: 4.8,
        reviewCount: 156,
        hourlyRate: 45,
        location: { city: 'Barcelona', country: 'Spain', latitude: 41.3851, longitude: 2.1734 },
        avatarUrl: 'https://via.placeholder.com/100',
        isVerified: true,
        languages: ['English', 'Spanish', 'Catalan'],
        specialties: ['Cultural Tours', 'Food & Wine', 'Historical Sites'],
        distance: 2.5,
        isOnline: true,
        responseTime: 'within 1 hour',
        completedTours: 234,
      },
      {
        id: 2,
        name: 'Ahmed Hassan',
        bio: 'Architecture enthusiast and certified guide specializing in Gaudí and modernist buildings.',
        rating: 4.9,
        reviewCount: 89,
        hourlyRate: 55,
        location: { city: 'Barcelona', country: 'Spain', latitude: 41.3851, longitude: 2.1734 },
        avatarUrl: 'https://via.placeholder.com/100',
        isVerified: true,
        languages: ['English', 'Arabic', 'Spanish'],
        specialties: ['Architecture', 'Art & Museums', 'Photography'],
        distance: 1.8,
        isOnline: true,
        responseTime: 'within 30 minutes',
        completedTours: 167,
      },
    ];

    return mockGuides.slice(0, limit);
  },

  /**
   * 获取热门服务
   */
  async getPopularServices(limit: number = 10): Promise<PopularService[]> {
    try {
      // 模拟热门服务数据，实际应该从后端获取
      return [
        {
          id: 1,
          title: "Gothic Quarter Walking Tour",
          price: 45,
          duration: "3h",
          category: "Cultural",
          rating: 4.8,
          bookingCount: 156
        },
        {
          id: 2,
          title: "Tapas & Wine Experience",
          price: 65,
          duration: "4h",
          category: "Food",
          rating: 4.9,
          bookingCount: 89
        },
        {
          id: 3,
          title: "Sagrada Familia Deep Dive",
          price: 55,
          duration: "2.5h",
          category: "Architecture",
          rating: 4.7,
          bookingCount: 203
        },
        {
          id: 4,
          title: "Picasso Museum & Art Walk",
          price: 50,
          duration: "3h",
          category: "Art",
          rating: 4.6,
          bookingCount: 134
        }
      ].slice(0, limit);
    } catch (error) {
      console.error('Error fetching popular services:', error);
      throw error;
    }
  },

  /**
   * 搜索服务
   */
  async searchServices(query: string, city?: string): Promise<GuideDiscoveryResponse[]> {
    try {
      const request: GuideDiscoveryRequest = {
        latitude: 41.3851, // 默认巴塞罗那，实际应该根据城市获取坐标
        longitude: 2.1734,
        radiusKm: city ? 20 : 100,
        page: 0,
        size: 20,
      };
      const response = await this.discoverGuides(request);
      // 在前端进行搜索过滤，实际应该在后端实现
      return response.content.filter(guide =>
        guide.name.toLowerCase().includes(query.toLowerCase()) ||
        guide.bio.toLowerCase().includes(query.toLowerCase()) ||
        guide.specialties.some(specialty =>
          specialty.toLowerCase().includes(query.toLowerCase())
        )
      );
    } catch (error) {
      console.error('Error searching services:', error);
      throw error;
    }
  },

  /**
   * 获取导游详细信息
   */
  async getGuideDetails(guideId: number): Promise<GuideDiscoveryResponse> {
    try {
      const response = await apiClient.get<GuideDiscoveryResponse>(`/guides/${guideId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching guide details:', error);
      throw error;
    }
  },

  /**
   * 获取导游可用时间
   */
  async getGuideAvailability(
    guideId: number,
    startDate?: string,
    endDate?: string
  ): Promise<any> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response = await apiClient.get(
        `/guides/${guideId}/availability?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching guide availability:', error);
      throw error;
    }
  },

  /**
   * 模拟获取用户位置
   */
  async getCurrentLocation(): Promise<{ latitude: number; longitude: number }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error) => {
          console.error('Error getting location:', error);
          // 返回默认位置（巴塞罗那）
          resolve({
            latitude: 41.3851,
            longitude: 2.1734,
          });
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5分钟缓存
        }
      );
    });
  },

  /**
   * 根据地址获取坐标（地理编码）
   */
  async geocodeAddress(address: string): Promise<{ latitude: number; longitude: number }> {
    // 在实际应用中，这里会调用地理编码API（如Google Maps Geocoding API）
    // 现在返回模拟数据
    const mockLocations: Record<string, { latitude: number; longitude: number }> = {
      'Barcelona, Spain': { latitude: 41.3851, longitude: 2.1734 },
      'Madrid, Spain': { latitude: 40.4168, longitude: -3.7038 },
      'Paris, France': { latitude: 48.8566, longitude: 2.3522 },
      'London, UK': { latitude: 51.5074, longitude: -0.1278 },
      'New York, USA': { latitude: 40.7128, longitude: -74.0060 },
    };

    return mockLocations[address] || mockLocations['Barcelona, Spain'];
  },

  /**
   * 计算两点之间的距离（公里）
   */
  calculateDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) *
        Math.cos(this.deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return Math.round(distance * 10) / 10; // 保留一位小数
  },

  /**
   * 角度转弧度
   */
  deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  },
};

export default guideDiscoveryService;
