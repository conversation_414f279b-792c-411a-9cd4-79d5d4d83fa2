# Assets Directory

This directory contains static assets for the Tourna mobile application.

## Structure

```
assets/
├── images/          # Application images
├── icons/           # Icon files
├── fonts/           # Custom fonts
├── logo.png         # Application logo
├── icon.png         # App icon
├── splash.png       # Splash screen
├── adaptive-icon.png # Android adaptive icon
└── favicon.png      # Web favicon
```

## Image Guidelines

- Use PNG format for images with transparency
- Use JPEG for photos and complex images
- Optimize images for mobile devices
- Provide multiple resolutions (@1x, @2x, @3x)

## Icon Guidelines

- Use vector icons when possible
- Follow platform-specific design guidelines
- Ensure icons are accessible and clear at small sizes

## Logo Requirements

- Provide logo in multiple formats (PNG, SVG)
- Include variations (light/dark themes)
- Maintain brand consistency

## Note

Currently using placeholder assets. Replace with actual branded assets before production deployment.
