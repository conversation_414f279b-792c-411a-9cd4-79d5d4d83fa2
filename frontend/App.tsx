import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { Provider } from 'react-redux';
import { NavigationContainer } from '@react-navigation/native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

import { store } from './src/store';
import { AppNavigator } from './src/navigation/AppNavigator';
import { theme } from './src/constants/theme';
import { AuthProvider } from './src/contexts/AuthContext';
import { I18nProvider } from './src/contexts/I18nContext';
import { NotificationProvider } from './src/contexts/NotificationContext';

/**
 * Tourna App主组件
 * 全球旅游导游撮合平台移动应用
 */
export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <Provider store={store}>
          <PaperProvider theme={theme}>
            <I18nProvider>
              <AuthProvider>
                <NotificationProvider>
                  <NavigationContainer>
                    <AppNavigator />
                    <StatusBar style="auto" />
                  </NavigationContainer>
                </NotificationProvider>
              </AuthProvider>
            </I18nProvider>
          </PaperProvider>
        </Provider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
